#!/usr/bin/env python3
"""
اختبار endpoints الذكاء الاصطناعي
"""

import requests
import json

def test_ai_endpoints():
    """اختبار جميع endpoints الذكاء الاصطناعي"""
    
    print("🧪 اختبار endpoints الذكاء الاصطناعي")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    # 1. اختبار الحصول على الإعدادات
    print("1️⃣ اختبار الحصول على الإعدادات...")
    try:
        response = requests.get(f"{base_url}/api/ai-responses/settings/current")
        print(f"   📊 كود الاستجابة: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ تم جلب الإعدادات بنجاح")
            print(f"   🤖 نموذج GPT: {data.get('gpt_model', 'غير محدد')}")
            print(f"   🔄 الرد التلقائي: {'مفعل' if data.get('auto_reply_enabled') else 'معطل'}")
        else:
            print(f"   ❌ فشل: {response.text}")
    except Exception as e:
        print(f"   ❌ خطأ: {e}")
    
    # 2. اختبار toggle auto reply
    print(f"\n2️⃣ اختبار تبديل الرد التلقائي...")
    try:
        response = requests.post(f"{base_url}/api/ai-responses/toggle-auto-reply")
        print(f"   📊 كود الاستجابة: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ تم تبديل الحالة بنجاح")
            print(f"   📝 الرسالة: {data.get('message', 'غير متوفرة')}")
            print(f"   🔄 الحالة الجديدة: {'مفعل' if data.get('auto_reply_enabled') else 'معطل'}")
        else:
            print(f"   ❌ فشل: {response.text}")
    except Exception as e:
        print(f"   ❌ خطأ: {e}")
    
    # 3. اختبار الرد التلقائي
    print(f"\n3️⃣ اختبار الرد التلقائي...")
    try:
        test_data = {
            "test_message": "مرحباً، أريد معلومات عن منتجاتكم"
        }
        response = requests.post(
            f"{base_url}/api/ai-responses/test",
            json=test_data,
            headers={"Content-Type": "application/json"}
        )
        print(f"   📊 كود الاستجابة: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ تم اختبار الرد بنجاح")
            print(f"   💬 الرسالة الأصلية: {data.get('original_message', 'غير متوفرة')}")
            print(f"   🤖 رد الذكاء الاصطناعي: {data.get('ai_response', 'غير متوفر')[:100]}...")
            print(f"   ⏱️ وقت الاستجابة: {data.get('response_time_ms', 0)} مللي ثانية")
        else:
            print(f"   ❌ فشل: {response.text}")
    except Exception as e:
        print(f"   ❌ خطأ: {e}")
    
    # 4. اختبار جلب الردود
    print(f"\n4️⃣ اختبار جلب الردود...")
    try:
        response = requests.get(f"{base_url}/api/ai-responses/")
        print(f"   📊 كود الاستجابة: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ تم جلب الردود بنجاح")
            print(f"   📊 عدد الردود: {len(data)}")
        else:
            print(f"   ❌ فشل: {response.text}")
    except Exception as e:
        print(f"   ❌ خطأ: {e}")
    
    print(f"\n" + "=" * 50)
    print("انتهى اختبار endpoints الذكاء الاصطناعي")

if __name__ == "__main__":
    test_ai_endpoints()
