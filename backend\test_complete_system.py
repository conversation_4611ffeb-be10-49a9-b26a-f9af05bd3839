#!/usr/bin/env python3
"""
اختبار النظام الكامل للردود الذكية المحسن
"""

import asyncio
import sys
import os
from pathlib import Path
import requests
import json
import time

# إضافة مسار المشروع
sys.path.append(str(Path(__file__).parent))

import logging

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

BASE_URL = "http://localhost:8000"

def test_complete_smart_reply_system():
    """اختبار النظام الكامل للردود الذكية"""
    
    print("🚀 اختبار النظام الكامل للردود الذكية المحسن")
    print("=" * 80)
    
    # 1. فحص الخادم والإعدادات
    print("1️⃣ فحص الخادم والإعدادات...")
    if not check_server_and_settings():
        return False
    
    # 2. فحص WhatsApp Web
    print("\n2️⃣ فحص WhatsApp Web...")
    if not check_whatsapp_web():
        return False
    
    # 3. إيقاف أي خدمة تعمل حالياً
    print("\n3️⃣ إيقاف الخدمات الحالية...")
    stop_current_services()
    
    # 4. تشغيل النظام المحسن
    print("\n4️⃣ تشغيل النظام المحسن...")
    if not start_enhanced_system():
        return False
    
    # 5. مراقبة النظام
    print("\n5️⃣ مراقبة النظام...")
    monitor_system()
    
    # 6. إيقاف النظام
    print("\n6️⃣ إيقاف النظام...")
    stop_system()
    
    return True

def check_server_and_settings():
    """فحص الخادم والإعدادات"""
    try:
        # فحص حالة الخادم
        response = requests.get(f"{BASE_URL}/api/ai-responses/auto-reply/status", timeout=5)
        if response.status_code != 200:
            print(f"   ❌ خطأ في الخادم: {response.status_code}")
            return False
        
        print("   ✅ الخادم يعمل")
        
        # فحص الإعدادات
        response = requests.get(f"{BASE_URL}/api/ai-responses/settings/current", timeout=5)
        if response.status_code == 200:
            settings = response.json()
            print(f"   🤖 OpenAI API: {'✅ مُعد' if settings.get('openai_api_key') else '❌ غير مُعد'}")
            print(f"   🧠 نموذج GPT: {settings.get('gpt_model', 'غير محدد')}")
            
            if not settings.get('openai_api_key'):
                print("   💡 يرجى إعداد OpenAI API Key من صفحة الإعدادات")
                return False
        else:
            print(f"   ❌ خطأ في الإعدادات: {response.status_code}")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في فحص الخادم: {e}")
        return False

def check_whatsapp_web():
    """فحص WhatsApp Web"""
    try:
        response = requests.get(f"{BASE_URL}/api/whatsapp/status", timeout=5)
        if response.status_code == 200:
            status = response.json()
            print(f"   📱 الجلسة نشطة: {'✅' if status.get('session_active', False) else '❌'}")
            print(f"   🔐 مسجل دخول: {'✅' if status.get('logged_in', False) else '❌'}")
            print(f"   🌐 المتصفح: {'✅ متاح' if status.get('driver_available', False) else '❌ غير متاح'}")
            
            if not status.get('session_active', False):
                print("   💡 يرجى تسجيل الدخول في WhatsApp Web من صفحة الإعدادات")
                return False
            
            return True
        else:
            print(f"   ❌ خطأ في WhatsApp Web: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ في فحص WhatsApp Web: {e}")
        return False

def stop_current_services():
    """إيقاف الخدمات الحالية"""
    try:
        response = requests.post(
            f"{BASE_URL}/api/ai-responses/toggle-auto-reply",
            json={"enabled": False},
            timeout=10
        )
        if response.status_code == 200:
            print("   ✅ تم إيقاف الخدمات الحالية")
        time.sleep(2)
        
    except Exception as e:
        print(f"   ⚠️ خطأ في إيقاف الخدمات: {e}")

def start_enhanced_system():
    """تشغيل النظام المحسن"""
    try:
        print("   🔄 تشغيل النظام المحسن...")
        
        response = requests.post(
            f"{BASE_URL}/api/ai-responses/toggle-auto-reply",
            json={"enabled": True},
            timeout=20
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"   📊 النتيجة: {result.get('message', 'غير محدد')}")
            
            if result.get('is_running', False):
                print("   🎉 تم تشغيل النظام المحسن بنجاح!")
                print("   📋 المميزات المحسنة:")
                print("      ✅ اكتشاف ذكي للمحادثات")
                print("      ✅ استخراج النص بدقة")
                print("      ✅ تجنب الوسائط (صور/صوت)")
                print("      ✅ إرسال آمن ومحسن")
                print("      ✅ تتبع الرسائل المُعالجة")
                return True
            else:
                print(f"   ❌ فشل في التشغيل: {result.get('message', 'غير محدد')}")
                return False
        else:
            print(f"   ❌ خطأ في API: {response.status_code}")
            try:
                error_detail = response.json()
                print(f"   📝 التفاصيل: {error_detail}")
            except:
                print(f"   📝 النص: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ في تشغيل النظام: {e}")
        return False

def monitor_system():
    """مراقبة النظام"""
    try:
        print("   👁️ مراقبة النظام لمدة 45 ثانية...")
        print("   💡 الآن يمكنك:")
        print("      📱 إرسال رسالة نصية من هاتف آخر")
        print("      🚫 تجنب إرسال صور أو ملفات صوتية")
        print("      ⏰ انتظار الرد التلقائي")
        print()
        
        for i in range(9):  # 9 فحوصات كل 5 ثوانٍ
            time.sleep(5)
            
            try:
                status_response = requests.get(f"{BASE_URL}/api/ai-responses/auto-reply/status", timeout=5)
                if status_response.status_code == 200:
                    status = status_response.json()
                    is_running = status.get('is_running', False)
                    
                    if is_running:
                        print(f"   📊 الفحص {i+1}/9: ✅ النظام يعمل بنجاح")
                    else:
                        print(f"   📊 الفحص {i+1}/9: ❌ النظام توقف")
                        print("   💡 فحص السجلات في الخادم للأخطاء")
                        break
                else:
                    print(f"   📊 الفحص {i+1}/9: ❌ خطأ في الاستعلام")
                    
            except Exception as e:
                print(f"   📊 الفحص {i+1}/9: ❌ خطأ في الاتصال: {e}")
        
        print("\n   ✅ انتهت فترة المراقبة")
        
    except Exception as e:
        print(f"   ❌ خطأ في المراقبة: {e}")

def stop_system():
    """إيقاف النظام"""
    try:
        response = requests.post(
            f"{BASE_URL}/api/ai-responses/toggle-auto-reply",
            json={"enabled": False},
            timeout=10
        )
        if response.status_code == 200:
            print("   ✅ تم إيقاف النظام بنجاح")
        else:
            print(f"   ❌ خطأ في إيقاف النظام: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ خطأ في إيقاف النظام: {e}")

def main():
    """الدالة الرئيسية"""
    
    print("🔧 النظام الكامل للردود الذكية المحسن")
    print("=" * 80)
    print("📋 التحسينات الجديدة:")
    print("   🎯 اكتشاف محسن للمحادثات الجديدة")
    print("   📝 استخراج ذكي للنصوص فقط")
    print("   🚫 تجاهل الصور والملفات الصوتية")
    print("   🔒 إرسال آمن ومحمي")
    print("   🔄 تتبع الرسائل لتجنب التكرار")
    print("   ⚡ أداء محسن وسرعة أكبر")
    print()
    
    success = test_complete_smart_reply_system()
    
    print("\n" + "=" * 80)
    print("📊 نتائج الاختبار الشامل:")
    print(f"   🎯 النظام الكامل: {'✅ نجح بامتياز' if success else '❌ فشل'}")
    
    if success:
        print("\n🎉 النظام الكامل يعمل بامتياز!")
        print("💪 قوة النظام الجديد:")
        print("   ✅ يكتشف المحادثات الجديدة تلقائياً")
        print("   ✅ يستخرج النصوص بدقة عالية")
        print("   ✅ يتجاهل الوسائط بذكاء")
        print("   ✅ يرسل الردود بأمان")
        print("   ✅ يتجنب الرسائل المكررة")
        print("   ✅ يعمل بسرعة وكفاءة")
        print("\n🚀 النظام جاهز للاستخدام الفعلي!")
        print("📱 جرب إرسال رسالة نصية من هاتف آخر وشاهد السحر!")
    else:
        print("\n⚠️ يحتاج النظام إلى مراجعة:")
        print("   🔧 تأكد من إعداد OpenAI API Key")
        print("   📱 تأكد من تسجيل الدخول في WhatsApp Web")
        print("   📋 فحص السجلات للأخطاء التفصيلية")

if __name__ == "__main__":
    main()
