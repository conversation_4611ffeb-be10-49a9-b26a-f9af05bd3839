#!/usr/bin/env python3
"""
اختبار مبسط لخدمة الردود التلقائية
"""

import asyncio
import requests
import json

async def test_auto_reply_simple():
    """اختبار مبسط للردود التلقائية"""
    
    print("🧪 اختبار مبسط لخدمة الردود التلقائية")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    # 1. فحص حالة WhatsApp Web
    print("1️⃣ فحص حالة WhatsApp Web...")
    try:
        response = requests.get(f"{base_url}/api/whatsapp/status")
        if response.status_code == 200:
            data = response.json()
            print(f"   📱 الجلسة: {'نشطة' if data.get('session_active') else 'غير نشطة'}")
            print(f"   🔐 تسجيل الدخول: {'مُسجل' if data.get('logged_in') else 'غير مُسجل'}")
            
            if not data.get('session_active') or not data.get('logged_in'):
                print("   ❌ WhatsApp Web غير مُسجل دخول!")
                return False
        else:
            print(f"   ❌ خطأ في فحص WhatsApp: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ خطأ في الاتصال: {e}")
        return False
    
    # 2. فحص الإعدادات
    print("\n2️⃣ فحص الإعدادات...")
    try:
        response = requests.get(f"{base_url}/api/ai-responses/settings/current")
        if response.status_code == 200:
            data = response.json()
            print(f"   🔑 مفتاح OpenAI: {'موجود' if data.get('openai_api_key') else 'غير موجود'}")
            print(f"   🔄 الرد التلقائي: {'مفعل' if data.get('auto_reply_enabled') else 'معطل'}")
            
            if not data.get('openai_api_key'):
                print("   ❌ مفتاح OpenAI غير موجود!")
                return False
        else:
            print(f"   ❌ خطأ في فحص الإعدادات: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ خطأ في فحص الإعدادات: {e}")
        return False
    
    # 3. محاولة تفعيل الردود التلقائية
    print("\n3️⃣ محاولة تفعيل الردود التلقائية...")
    try:
        response = requests.post(f"{base_url}/api/ai-responses/toggle-auto-reply")
        print(f"   📊 كود الاستجابة: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ الاستجابة: {data.get('message', 'غير متوفرة')}")
            print(f"   🔄 حالة الإعداد: {'مفعل' if data.get('auto_reply_enabled') else 'معطل'}")
            print(f"   🤖 حالة الخدمة: {'تعمل' if data.get('service_running') else 'متوقفة'}")
            
            if 'error' in data:
                print(f"   ⚠️ خطأ: {data['error']}")
                return False
                
        else:
            print(f"   ❌ فشل في التفعيل: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   📝 تفاصيل الخطأ: {error_data}")
            except:
                print(f"   📝 نص الخطأ: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ في التفعيل: {e}")
        return False
    
    # 4. فحص حالة الخدمة بعد التفعيل
    print("\n4️⃣ فحص حالة الخدمة...")
    for i in range(3):  # فحص 3 مرات
        try:
            await asyncio.sleep(2)  # انتظار بين الفحوصات
            
            response = requests.get(f"{base_url}/api/ai-responses/auto-reply/status")
            if response.status_code == 200:
                data = response.json()
                print(f"   📊 فحص {i+1}: {'تعمل' if data.get('is_running') else 'متوقفة'}")
                print(f"   📝 الرسالة: {data.get('message', 'غير متوفرة')}")
                
                if data.get('is_running'):
                    print("   🎉 الخدمة تعمل بنجاح!")
                    return True
            else:
                print(f"   ❌ خطأ في فحص الحالة: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ خطأ في فحص الحالة: {e}")
    
    print("   ❌ الخدمة لا تعمل بعد المحاولات")
    return False

if __name__ == "__main__":
    result = asyncio.run(test_auto_reply_simple())
    
    if result:
        print("\n🎉 النظام يعمل بنجاح!")
        print("💡 جرب إرسال رسالة لنفسك من هاتف آخر")
    else:
        print("\n❌ النظام لا يعمل!")
        print("🔧 تحقق من:")
        print("   - تسجيل الدخول لـ WhatsApp Web")
        print("   - مفتاح OpenAI API")
        print("   - logs الخادم للأخطاء")
