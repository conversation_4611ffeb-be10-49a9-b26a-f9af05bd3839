#!/usr/bin/env python3
"""
اختبار إرسال حملة مباشرة
"""

import sys
import os
import asyncio

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.getcwd())

from database import SessionLocal
from services.campaign_service import CampaignService

async def test_campaign_send():
    """اختبار إرسال حملة"""
    
    print("🧪 اختبار إرسال حملة مباشرة")
    print("=" * 50)
    
    db = SessionLocal()
    try:
        # إنشاء خدمة الحملة
        campaign_service = CampaignService(db)
        
        # معرف الحملة للاختبار (غير هذا حسب الحملة الموجودة)
        campaign_id = 3  # غير هذا إلى معرف الحملة التي تريد اختبارها
        
        print(f"📤 محاولة إرسال الحملة {campaign_id}...")
        
        # إرسال الحملة
        result = await campaign_service.send_campaign(campaign_id)
        
        print(f"\n✅ نتيجة الإرسال:")
        print(f"   📊 إجمالي جهات الاتصال: {result.get('total_contacts', 0)}")
        print(f"   ✅ تم الإرسال: {result.get('sent_count', 0)}")
        print(f"   📨 تم التسليم: {result.get('delivered_count', 0)}")
        print(f"   ❌ فشل: {result.get('failed_count', 0)}")
        
        if result.get('sent_count', 0) > 0:
            print("\n🎉 تم إرسال بعض الرسائل بنجاح!")
        else:
            print("\n⚠️ لم يتم إرسال أي رسالة")
        
    except Exception as e:
        print(f"\n❌ خطأ في إرسال الحملة: {e}")
        print(f"📝 تفاصيل الخطأ: {type(e).__name__}")
        
    finally:
        db.close()
    
    print("\n" + "=" * 50)
    print("انتهى اختبار إرسال الحملة")

if __name__ == "__main__":
    asyncio.run(test_campaign_send())
