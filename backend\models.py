from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, Float, ForeignKey, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from database import Base
from datetime import datetime

class Campaign(Base):
    """نموذج الحملات الإعلانية"""
    __tablename__ = "campaigns"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False, index=True)
    message_text = Column(Text, nullable=False)
    image_path = Column(String(500), nullable=True)
    status = Column(String(50), default="pending")  # pending, running, completed, failed
    total_contacts = Column(Integer, default=0)
    sent_count = Column(Integer, default=0)
    delivered_count = Column(Integer, default=0)
    failed_count = Column(Integer, default=0)
    scheduled_time = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # العلاقات
    messages = relationship("Message", back_populates="campaign")
    contacts = relationship("Contact", back_populates="campaign")

class Contact(Base):
    """نموذج العملاء/جهات الاتصال"""
    __tablename__ = "contacts"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    phone_number = Column(String(20), nullable=False, index=True)
    email = Column(String(255), nullable=True)
    campaign_id = Column(Integer, ForeignKey("campaigns.id"), nullable=True)
    custom_fields = Column(JSON, nullable=True)  # لحفظ بيانات إضافية من Excel
    created_at = Column(DateTime, default=func.now())
    
    # العلاقات
    campaign = relationship("Campaign", back_populates="contacts")
    messages = relationship("Message", back_populates="contact")

class Message(Base):
    """نموذج الرسائل المرسلة"""
    __tablename__ = "messages"
    
    id = Column(Integer, primary_key=True, index=True)
    campaign_id = Column(Integer, ForeignKey("campaigns.id"), nullable=True)
    contact_id = Column(Integer, ForeignKey("contacts.id"), nullable=False)
    message_text = Column(Text, nullable=False)
    message_type = Column(String(50), default="campaign")  # campaign, auto_reply, manual
    status = Column(String(50), default="pending")  # pending, sent, delivered, failed, read
    whatsapp_message_id = Column(String(255), nullable=True)
    error_message = Column(Text, nullable=True)
    sent_at = Column(DateTime, nullable=True)
    delivered_at = Column(DateTime, nullable=True)
    read_at = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=func.now())
    
    # العلاقات
    campaign = relationship("Campaign", back_populates="messages")
    contact = relationship("Contact", back_populates="messages")

class AIResponse(Base):
    """نموذج الردود التلقائية بالذكاء الاصطناعي"""
    __tablename__ = "ai_responses"
    
    id = Column(Integer, primary_key=True, index=True)
    contact_phone = Column(String(20), nullable=False, index=True)
    incoming_message = Column(Text, nullable=False)
    ai_response = Column(Text, nullable=False)
    gpt_model_used = Column(String(50), default="gpt-4o-mini")
    response_time_ms = Column(Integer, nullable=True)  # وقت الاستجابة بالميلي ثانية
    confidence_score = Column(Float, nullable=True)  # درجة الثقة في الرد
    created_at = Column(DateTime, default=func.now())

class Settings(Base):
    """نموذج إعدادات النظام"""
    __tablename__ = "settings"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # إعدادات WhatsApp Web
    whatsapp_web_headless = Column(Boolean, default=False)
    whatsapp_session_timeout = Column(Integer, default=1800)  # 30 دقيقة
    
    # إعدادات OpenAI
    openai_api_key = Column(String(500), nullable=True)
    gpt_model = Column(String(50), default="gpt-4o-mini")
    gpt_prompt = Column(Text, default="""أنت مساعد ذكاء اصطناعي متخصص في تحليل محادثات العملاء وفهم السياق. مهمتك:

1. تحليل محتوى الرسائل المتبادلة بين العميل ومساعد المبيعات
2. فهم السياق من الرسائل السابقة لتقديم إجابات دقيقة
3. تحديد هوية العميل من خلال تحليل المحادثة
4. استخدام اللغة العربية بشكل طبيعي ومفهوم

إرشادات مهمة:
- ركز على فهم محتوى المحادثة والسياق
- استخدم الرسائل السابقة لفهم طبيعة الاستفسار الحالي
- عند سؤال العميل عن هويته، راجع المحادثة السابقة لاكتشاف الاسم
- عند سؤال العميل عن معلومات تم مناقشتها سابقاً، استعرض المحادثة لاكتشاف التفاصيل
- كن دقيقاً في تحليل المحتوى وتجنب التخمين

مثال على تنسيق المحادثة التي سيتم تحليلها:
العميل: مرحبا انا احمد
ai bot: مرحبا أحمد، كيف يمكنني مساعدتك اليوم؟
العميل: من انا هل تعرف اسمي
ai bot: اسمك أحمد كما ذكرته في رسالتك الأولى""")
    
    # إعدادات الرد التلقائي
    auto_reply_enabled = Column(Boolean, default=False)
    response_delay = Column(Integer, default=40)  # تأخير الرد بالثواني (للردود الذكية - لن يستخدم)
    max_daily_responses = Column(Integer, default=1000)

    # إعدادات الحملات الإعلانية
    campaign_response_delay = Column(Integer, default=40)  # تأخير الرد للحملات الإعلانية بالثواني
    
    # إعدادات عامة
    company_name = Column(String(255), default="شركتي")
    support_phone = Column(String(20), nullable=True)
    support_email = Column(String(255), nullable=True)
    
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())