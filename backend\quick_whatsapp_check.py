#!/usr/bin/env python3
"""
فحص سريع لحالة WhatsApp Web
"""

import sys
import os
sys.path.insert(0, os.getcwd())

try:
    from whatsapp_selenium import whatsapp_manager
    
    print("🔍 فحص حالة WhatsApp Web...")
    print("=" * 40)
    
    status = whatsapp_manager.get_status()
    
    print(f"📊 الجلسة نشطة: {status.get('session_active', False)}")
    print(f"🔐 مسجل دخول: {status.get('logged_in', False)}")
    print(f"💬 الرسالة: {status.get('message', 'غير متوفرة')}")
    
    if status.get('session_active') and status.get('logged_in'):
        print("\n✅ WhatsApp Web جاهز للإرسال!")
    elif status.get('session_active') and not status.get('logged_in'):
        print("\n⚠️ WhatsApp Web مفتوح لكن يحتاج تسجيل دخول")
        print("💡 اذهب للإعدادات وسجل دخول")
    else:
        print("\n❌ WhatsApp Web غير مفتوح")
        print("💡 اذهب للإعدادات وابدأ جلسة جديدة")
    
    print("\n🌐 الإعدادات: http://localhost:5173/settings")
    
except Exception as e:
    print(f"❌ خطأ: {e}")
    print("💡 تأكد من تشغيل الباك إند وتثبيت المكتبات المطلوبة")
