from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime
import time

from database import get_db
from models import AIResponse, Settings
from schemas import (
    AIResponseCreate, AIResponseResponse,
    SettingsUpdate, SettingsResponse,
    TestAIResponse, TestAIResponseResult
)
from services.ai_service import AIService

router = APIRouter(prefix="/api/ai-responses", tags=["ai-responses"])

@router.get("/", response_model=List[AIResponseResponse])
async def get_ai_responses(
    skip: int = 0,
    limit: int = 100,
    contact_phone: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """الحصول على قائمة الردود التلقائية"""
    query = db.query(AIResponse)
    
    if contact_phone:
        query = query.filter(AIResponse.contact_phone == contact_phone)
    
    responses = query.order_by(AIResponse.created_at.desc()).offset(skip).limit(limit).all()
    return responses

@router.get("/{response_id}", response_model=AIResponseResponse)
async def get_ai_response(response_id: int, db: Session = Depends(get_db)):
    """الحصول على رد تلقائي محدد"""
    response = db.query(AIResponse).filter(AIResponse.id == response_id).first()
    if not response:
        raise HTTPException(status_code=404, detail="الرد التلقائي غير موجود")
    return response

@router.post("/", response_model=AIResponseResponse)
async def create_ai_response(
    ai_response: AIResponseCreate,
    db: Session = Depends(get_db)
):
    """إنشاء رد تلقائي جديد"""
    db_response = AIResponse(
        contact_phone=ai_response.contact_phone,
        incoming_message=ai_response.incoming_message,
        ai_response=ai_response.ai_response,
        gpt_model_used=ai_response.gpt_model_used,
        response_time_ms=ai_response.response_time_ms,
        confidence_score=ai_response.confidence_score
    )
    
    db.add(db_response)
    db.commit()
    db.refresh(db_response)
    
    return db_response

@router.post("/test", response_model=TestAIResponseResult)
async def test_ai_response(
    test_request: TestAIResponse,
    db: Session = Depends(get_db)
):
    """اختبار الرد التلقائي"""
    try:
        # الحصول على الإعدادات
        settings = db.query(Settings).first()
        if not settings:
            raise HTTPException(
                status_code=404,
                detail="لم يتم العثور على إعدادات النظام"
            )
        
        if not settings.openai_api_key:
            raise HTTPException(
                status_code=400,
                detail="لم يتم تكوين مفتاح OpenAI API"
            )
        
        # إنشاء خدمة الذكاء الاصطناعي
        ai_service = AIService(
            api_key=settings.openai_api_key,
            model=settings.gpt_model,
            system_prompt=settings.gpt_prompt
        )
        
        # قياس وقت الاستجابة
        start_time = time.time()
        
        # الحصول على الرد من GPT
        ai_response = await ai_service.generate_response(test_request.test_message)
        
        end_time = time.time()
        response_time_ms = int((end_time - start_time) * 1000)
        
        return TestAIResponseResult(
            original_message=test_request.test_message,
            ai_response=ai_response,
            response_time_ms=response_time_ms,
            model_used=settings.gpt_model
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"خطأ في اختبار الرد التلقائي: {str(e)}"
        )

@router.get("/settings/current", response_model=SettingsResponse)
async def get_ai_settings(db: Session = Depends(get_db)):
    """الحصول على إعدادات الذكاء الاصطناعي الحالية"""
    settings = db.query(Settings).first()

    # إنشاء إعدادات افتراضية إذا لم توجد
    if not settings:
        settings = Settings(
            gpt_model="gpt-3.5-turbo",
            gpt_prompt="أنت مساعد خدمة عملاء ودود ومهني. مهمتك:\n\n1. الرد على استفسارات العملاء بطريقة مهذبة وسريعة\n2. تقديم معلومات مفيدة حول المنتجات والخدمات\n3. توجيه العملاء للحصول على المساعدة المناسبة\n4. استخدام اللغة العربية بشكل طبيعي ومفهوم\n\nتذكر أن تكون دائماً مساعداً ومتعاوناً.",
            response_delay=2,
            max_daily_responses=1000,
            auto_reply_enabled=False,
            company_name="شركتي",
            support_phone="+966xxxxxxxxx",
            support_email="<EMAIL>",
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        db.add(settings)
        db.commit()
        db.refresh(settings)

    return settings

@router.put("/settings", response_model=SettingsResponse)
async def update_ai_settings(
    settings_update: SettingsUpdate,
    db: Session = Depends(get_db)
):
    """تحديث إعدادات الذكاء الاصطناعي"""
    settings = db.query(Settings).first()

    # إنشاء إعدادات افتراضية إذا لم توجد
    if not settings:
        settings = Settings(
            gpt_model="gpt-3.5-turbo",
            gpt_prompt="أنت مساعد خدمة عملاء ودود ومهني.",
            response_delay=2,
            max_daily_responses=1000,
            auto_reply_enabled=False,
            company_name="شركتي",
            support_phone="+966xxxxxxxxx",
            support_email="<EMAIL>",
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        db.add(settings)
        db.commit()
        db.refresh(settings)

    # تحديث الإعدادات
    update_data = settings_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        if hasattr(settings, field):
            setattr(settings, field, value)

    settings.updated_at = datetime.now()
    db.commit()
    db.refresh(settings)

    return settings

@router.post("/toggle-auto-reply")
async def toggle_auto_reply(db: Session = Depends(get_db)):
    """تفعيل/إلغاء تفعيل الرد التلقائي"""

    settings = db.query(Settings).first()
    if not settings:
        raise HTTPException(status_code=404, detail="لم يتم العثور على الإعدادات")

    # التحقق من وجود مفتاح OpenAI
    if not settings.openai_api_key and not settings.auto_reply_enabled:
        raise HTTPException(
            status_code=400,
            detail="يجب تكوين مفتاح OpenAI API أولاً"
        )

    # تبديل حالة الرد التلقائي
    old_status = settings.auto_reply_enabled
    settings.auto_reply_enabled = not settings.auto_reply_enabled
    settings.updated_at = datetime.now()
    db.commit()

    # بدء أو إيقاف خدمة المراقبة حسب الحالة الجديدة
    service_status = "لم يتم تغيير حالة المراقبة"
    service_running = False

    try:
        # استيراد محلي لتجنب مشاكل الاستيراد الدائري
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.dirname(__file__)))

        from services.auto_reply_service import get_auto_reply_service
        from whatsapp_selenium import whatsapp_manager

        auto_reply = get_auto_reply_service(whatsapp_manager)

        if settings.auto_reply_enabled:
            # بدء الخدمة
            try:
                await auto_reply.start_auto_reply()
                service_status = "تم بدء المراقبة"
                service_running = auto_reply.is_running
            except Exception as start_error:
                service_status = f"فشل في بدء المراقبة: {str(start_error)}"
                service_running = False
        else:
            # إيقاف الخدمة
            try:
                await auto_reply.stop_auto_reply()
                service_status = "تم إيقاف المراقبة"
                service_running = auto_reply.is_running
            except Exception as stop_error:
                service_status = f"فشل في إيقاف المراقبة: {str(stop_error)}"
                service_running = False

    except Exception as e:
        # في حالة فشل بدء/إيقاف الخدمة، نعيد الإعداد للحالة السابقة
        settings.auto_reply_enabled = old_status
        settings.updated_at = datetime.now()
        db.commit()

        # لا نرفع خطأ، بل نعيد رسالة توضيحية
        return {
            "message": f"تم تغيير الإعداد لكن فشل في تشغيل المراقبة: {str(e)}",
            "auto_reply_enabled": settings.auto_reply_enabled,
            "service_running": False,
            "error": str(e)
        }

    status = "مفعل" if settings.auto_reply_enabled else "معطل"
    return {
        "message": f"تم {status} الرد التلقائي بنجاح - {service_status}",
        "auto_reply_enabled": settings.auto_reply_enabled,
        "service_running": service_running
    }

@router.get("/auto-reply/status")
async def get_auto_reply_status():
    """الحصول على حالة خدمة الردود التلقائية"""

    try:
        # استيراد محلي لتجنب مشاكل الاستيراد الدائري
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.dirname(__file__)))

        from services.auto_reply_service import get_auto_reply_service
        from whatsapp_selenium import whatsapp_manager

        auto_reply = get_auto_reply_service(whatsapp_manager)

        return {
            "is_running": auto_reply.is_running,
            "status": "running" if auto_reply.is_running else "stopped",
            "message": "خدمة الردود التلقائية نشطة" if auto_reply.is_running else "خدمة الردود التلقائية متوقفة",
            "monitor_task_active": auto_reply.monitor_task and not auto_reply.monitor_task.done() if auto_reply.monitor_task else False
        }

    except Exception as e:
        # في حالة الخطأ، نعيد حالة افتراضية
        return {
            "is_running": False,
            "status": "error",
            "message": f"خطأ في فحص الحالة: {str(e)}",
            "monitor_task_active": False
        }

@router.post("/auto-reply/force-start")
async def force_start_auto_reply():
    """إجبار بدء خدمة الردود التلقائية"""

    try:
        # استيراد محلي لتجنب مشاكل الاستيراد الدائري
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.dirname(__file__)))

        from services.auto_reply_service import get_auto_reply_service
        from whatsapp_selenium import whatsapp_manager

        # إعادة تعيين الخدمة
        global auto_reply_service
        auto_reply_service = None

        # إنشاء خدمة جديدة
        auto_reply = get_auto_reply_service(whatsapp_manager)

        # إجبار البدء
        auto_reply.is_running = True
        auto_reply.monitor_task = asyncio.create_task(auto_reply._monitor_messages_wrapper())

        return {
            "success": True,
            "message": "تم إجبار بدء الخدمة",
            "is_running": auto_reply.is_running
        }

    except Exception as e:
        return {
            "success": False,
            "message": f"فشل في إجبار البدء: {str(e)}",
            "is_running": False
        }

@router.get("/stats/daily")
async def get_daily_ai_stats(db: Session = Depends(get_db)):
    """الحصول على إحصائيات الردود التلقائية اليومية"""
    from datetime import datetime, timedelta
    
    today = datetime.now().date()
    yesterday = today - timedelta(days=1)
    
    # إحصائيات اليوم
    today_responses = db.query(AIResponse).filter(
        AIResponse.created_at >= today
    ).count()
    
    # إحصائيات أمس
    yesterday_responses = db.query(AIResponse).filter(
        AIResponse.created_at >= yesterday,
        AIResponse.created_at < today
    ).count()
    
    # متوسط وقت الاستجابة اليوم
    avg_response_time = db.query(AIResponse).filter(
        AIResponse.created_at >= today,
        AIResponse.response_time_ms.isnot(None)
    ).with_entities(AIResponse.response_time_ms).all()
    
    avg_time_ms = 0
    if avg_response_time:
        total_time = sum([r[0] for r in avg_response_time if r[0]])
        avg_time_ms = total_time / len(avg_response_time) if avg_response_time else 0
    
    # الحصول على الإعدادات
    settings = db.query(Settings).first()
    
    return {
        "today_responses": today_responses,
        "yesterday_responses": yesterday_responses,
        "change_percentage": ((today_responses - yesterday_responses) / yesterday_responses * 100) if yesterday_responses > 0 else 0,
        "avg_response_time_ms": round(avg_time_ms, 2),
        "auto_reply_enabled": settings.auto_reply_enabled if settings else False,
        "daily_limit": settings.max_daily_responses if settings else 1000,
        "remaining_today": max(0, (settings.max_daily_responses if settings else 1000) - today_responses)
    }

@router.delete("/{response_id}")
async def delete_ai_response(response_id: int, db: Session = Depends(get_db)):
    """حذف رد تلقائي"""
    response = db.query(AIResponse).filter(AIResponse.id == response_id).first()
    if not response:
        raise HTTPException(status_code=404, detail="الرد التلقائي غير موجود")
    
    db.delete(response)
    db.commit()
    
    return {"message": "تم حذف الرد التلقائي بنجاح"}

@router.get("/contact/{phone_number}", response_model=List[AIResponseResponse])
async def get_contact_ai_responses(
    phone_number: str,
    skip: int = 0,
    limit: int = 50,
    db: Session = Depends(get_db)
):
    """الحصول على تاريخ الردود التلقائية لعميل محدد"""
    responses = db.query(AIResponse).filter(
        AIResponse.contact_phone == phone_number
    ).order_by(AIResponse.created_at.desc()).offset(skip).limit(limit).all()
    
    return responses
