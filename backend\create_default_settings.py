"""
إنشاء الإعدادات الافتراضية في قاعدة البيانات
"""

from sqlalchemy.orm import Session
from datetime import datetime
from database import get_db, init_db
from models import Settings

def create_default_settings():
    """إنشاء إعدادات افتراضية"""
    
    # تهيئة قاعدة البيانات
    init_db()
    
    # الحصول على جلسة قاعدة البيانات
    db = next(get_db())
    
    try:
        # التحقق من وجود إعدادات
        existing_settings = db.query(Settings).first()
        
        if existing_settings:
            print("✅ الإعدادات موجودة بالفعل")
            print(f"   - اسم الشركة: {existing_settings.company_name}")
            print(f"   - نموذج GPT: {existing_settings.gpt_model}")
            print(f"   - الرد التلقائي: {'مفعل' if existing_settings.auto_reply_enabled else 'معطل'}")
            return existing_settings
        
        # إنشاء إعدادات افتراضية
        default_settings = Settings(
            # إعدادات OpenAI
            gpt_model="gpt-4o-mini",
            gpt_prompt="""أنت مساعد خدمة عملاء ودود ومهني. مهمتك:

1. الرد على استفسارات العملاء بطريقة مهذبة وسريعة
2. تقديم معلومات مفيدة حول المنتجات والخدمات
3. توجيه العملاء للحصول على المساعدة المناسبة
4. استخدام اللغة العربية بشكل طبيعي ومفهوم
5. الحفاظ على طابع ودود ومساعد في جميع الردود

إرشادات مهمة:
- اجعل ردودك قصيرة ومفيدة (أقل من 200 كلمة)
- استخدم الرموز التعبيرية بشكل مناسب
- إذا لم تعرف الإجابة، اطلب من العميل التواصل مع فريق الدعم
- تجنب تقديم معلومات تقنية معقدة
- كن إيجابياً ومتعاوناً دائماً
- استخدم سياق المحادثة السابق لفهم طبيعة الاستفسار بشكل أفضل

تذكر أن تكون دائماً مساعداً ومتعاوناً.""",
            response_delay=2,
            max_daily_responses=1000,
            auto_reply_enabled=False,
            
            # معلومات الشركة
            company_name="شركتي",
            support_phone="+966xxxxxxxxx",
            support_email="<EMAIL>",
            
            # إعدادات WhatsApp Web
            whatsapp_web_headless=False,
            whatsapp_session_timeout=1800,
            
            # التواريخ
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        db.add(default_settings)
        db.commit()
        db.refresh(default_settings)
        
        print("🎉 تم إنشاء الإعدادات الافتراضية بنجاح!")
        print(f"   - ID: {default_settings.id}")
        print(f"   - اسم الشركة: {default_settings.company_name}")
        print(f"   - نموذج GPT: {default_settings.gpt_model}")
        print(f"   - تأخير الرد: {default_settings.response_delay} ثانية")
        print(f"   - الحد الأقصى للردود: {default_settings.max_daily_responses}")
        
        return default_settings
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الإعدادات: {e}")
        db.rollback()
        return None
    finally:
        db.close()

def update_existing_settings():
    """تحديث الإعدادات الموجودة لإضافة الحقول الجديدة"""
    
    db = next(get_db())
    
    try:
        settings = db.query(Settings).first()
        
        if not settings:
            print("⚠️ لا توجد إعدادات لتحديثها")
            return create_default_settings()
        
        # تحديث الحقول المفقودة
        updated = False
        
        if not hasattr(settings, 'whatsapp_web_headless') or settings.whatsapp_web_headless is None:
            settings.whatsapp_web_headless = False
            updated = True
        
        if not hasattr(settings, 'whatsapp_session_timeout') or settings.whatsapp_session_timeout is None:
            settings.whatsapp_session_timeout = 1800
            updated = True
            
        if not settings.company_name:
            settings.company_name = "شركتي"
            updated = True
        
        if not settings.support_phone:
            settings.support_phone = "+966xxxxxxxxx"
            updated = True
        
        if not settings.support_email:
            settings.support_email = "<EMAIL>"
            updated = True
        
        if not settings.gpt_model or settings.gpt_model == "gpt-3.5-turbo":
            settings.gpt_model = "gpt-4o-mini"
            updated = True
        
        if not settings.gpt_prompt or "تحليل محتوى الرسائل المتبادلة" not in settings.gpt_prompt:
            settings.gpt_prompt = """أنت مساعد ذكاء اصطناعي متخصص في تحليل محادثات العملاء وفهم السياق. مهمتك:

1. تحليل محتوى الرسائل المتبادلة بين العميل ومساعد المبيعات
2. فهم السياق من الرسائل السابقة لتقديم إجابات دقيقة
3. تحديد هوية العميل من خلال تحليل المحادثة
4. استخدام اللغة العربية بشكل طبيعي ومفهوم

إرشادات مهمة:
- ركز على فهم محتوى المحادثة والسياق
- استخدم الرسائل السابقة لفهم طبيعة الاستفسار الحالي
- عند سؤال العميل عن هويته، راجع المحادثة السابقة لاكتشاف الاسم
- عند سؤال العميل عن معلومات تم مناقشتها سابقاً، استعرض المحادثة لاكتشاف التفاصيل
- كن دقيقاً في تحليل المحتوى وتجنب التخمين

مثال على تنسيق المحادثة التي سيتم تحليلها:
العميل: مرحبا انا احمد
ai bot: مرحبا أحمد، كيف يمكنني مساعدتك اليوم؟
العميل: من انا هل تعرف اسمي
ai bot: اسمك أحمد كما ذكرته في رسالتك الأولى"""
            updated = True
        
        if settings.response_delay is None:
            settings.response_delay = 2
            updated = True
        
        if settings.max_daily_responses is None:
            settings.max_daily_responses = 1000
            updated = True
        
        if settings.auto_reply_enabled is None:
            settings.auto_reply_enabled = False
            updated = True
        
        if updated:
            settings.updated_at = datetime.now()
            db.commit()
            db.refresh(settings)
            print("✅ تم تحديث الإعدادات الموجودة")
        else:
            print("✅ الإعدادات محدثة بالفعل")
        
        return settings
        
    except Exception as e:
        print(f"❌ خطأ في تحديث الإعدادات: {e}")
        db.rollback()
        return None
    finally:
        db.close()

if __name__ == "__main__":
    print("🔧 إنشاء/تحديث الإعدادات الافتراضية...")
    print("=" * 50)
    
    # محاولة تحديث الإعدادات الموجودة أولاً
    settings = update_existing_settings()
    
    if not settings:
        # إنشاء إعدادات جديدة إذا فشل التحديث
        settings = create_default_settings()
    
    if settings:
        print("\n✅ الإعدادات جاهزة للاستخدام!")
        print("🚀 يمكنك الآن تشغيل المشروع: start.bat")
    else:
        print("\n❌ فشل في إعداد الإعدادات")
    
    print("=" * 50)
    input("اضغط Enter للخروج...")