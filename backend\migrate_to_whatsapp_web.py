"""
Migration script to update database for WhatsApp Web integration
سكريبت لتحديث قاعدة البيانات للتكامل مع WhatsApp Web
"""

from sqlalchemy import create_engine, text
from database import DATABASE_URL
import logging

logger = logging.getLogger(__name__)

def migrate_database():
    """تحديث قاعدة البيانات لدعم WhatsApp Web"""
    
    engine = create_engine(DATABASE_URL)
    
    try:
        with engine.connect() as connection:
            # إضافة أعمدة WhatsApp Web الجديدة
            try:
                connection.execute(text("""
                    ALTER TABLE settings 
                    ADD COLUMN whatsapp_web_headless BOOLEAN DEFAULT FALSE
                """))
                print("✅ تم إضافة عمود whatsapp_web_headless")
            except Exception as e:
                if "duplicate column name" in str(e).lower() or "already exists" in str(e).lower():
                    print("⚠️ عمود whatsapp_web_headless موجود بالفعل")
                else:
                    print(f"❌ خطأ في إضافة whatsapp_web_headless: {e}")
            
            try:
                connection.execute(text("""
                    ALTER TABLE settings 
                    ADD COLUMN whatsapp_session_timeout INTEGER DEFAULT 1800
                """))
                print("✅ تم إضافة عمود whatsapp_session_timeout")
            except Exception as e:
                if "duplicate column name" in str(e).lower() or "already exists" in str(e).lower():
                    print("⚠️ عمود whatsapp_session_timeout موجود بالفعل")
                else:
                    print(f"❌ خطأ في إضافة whatsapp_session_timeout: {e}")
            
            # تحديث الإعدادات الموجودة
            try:
                connection.execute(text("""
                    UPDATE settings 
                    SET whatsapp_web_headless = FALSE,
                        whatsapp_session_timeout = 1800
                    WHERE whatsapp_web_headless IS NULL 
                       OR whatsapp_session_timeout IS NULL
                """))
                print("✅ تم تحديث الإعدادات الافتراضية")
            except Exception as e:
                print(f"❌ خطأ في تحديث الإعدادات: {e}")
            
            connection.commit()
            print("🎉 تم تحديث قاعدة البيانات بنجاح!")
            
    except Exception as e:
        logger.error(f"خطأ في تحديث قاعدة البيانات: {e}")
        print(f"❌ فشل في تحديث قاعدة البيانات: {e}")

if __name__ == "__main__":
    print("🔄 بدء تحديث قاعدة البيانات لدعم WhatsApp Web...")
    migrate_database()
