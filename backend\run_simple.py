#!/usr/bin/env python3
"""
ملف تشغيل مبسط للباك إند بدون pandas
"""

import sys
import os

print("🚀 بدء تشغيل Smart WhatsApp Campaigner Backend")
print(f"📍 Python: {sys.version}")
print(f"📍 المجلد: {os.getcwd()}")
print("-" * 50)

# تحميل متغيرات البيئة
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ تم تحميل متغيرات البيئة")
except ImportError:
    print("⚠️ python-dotenv غير متوفر")
except Exception as e:
    print(f"⚠️ خطأ في تحميل متغيرات البيئة: {e}")

# تحقق من وجود المكتبات الأساسية
print("\n🔍 فحص المكتبات...")
missing_libs = []

try:
    import fastapi
    print("✅ fastapi متوفر")
except ImportError:
    print("❌ fastapi غير متوفر")
    missing_libs.append("fastapi")

try:
    import uvicorn
    print("✅ uvicorn متوفر")
except ImportError:
    print("❌ uvicorn غير متوفر")
    missing_libs.append("uvicorn")

try:
    import sqlalchemy
    print("✅ sqlalchemy متوفر")
except ImportError:
    print("❌ sqlalchemy غير متوفر")
    missing_libs.append("sqlalchemy")

try:
    import pydantic
    print("✅ pydantic متوفر")
except ImportError:
    print("❌ pydantic غير متوفر")
    missing_libs.append("pydantic")

if missing_libs:
    print(f"\n❌ مكتبات مفقودة: {', '.join(missing_libs)}")
    print("يرجى تشغيل:")
    print(f"pip install {' '.join(missing_libs)}")
    input("اضغط Enter للخروج...")
    exit(1)

print("✅ جميع المكتبات الأساسية متوفرة")

# تحقق من pandas (اختياري)
print("\n🔍 فحص pandas...")
try:
    import pandas
    print("✅ pandas متوفر")
    PANDAS_AVAILABLE = True
except ImportError:
    print("⚠️ pandas غير متوفر - سيتم تعطيل ميزات رفع الملفات")
    PANDAS_AVAILABLE = False

# تحقق من وجود ملف main.py
if not os.path.exists("main.py"):
    print("\n❌ ملف main.py غير موجود في المجلد الحالي")
    print(f"📍 المجلد الحالي: {os.getcwd()}")
    print("تأكد من وجودك في مجلد backend")
    input("اضغط Enter للخروج...")
    exit(1)

if __name__ == "__main__":
    try:
        # إعدادات الخادم
        host = os.getenv("HOST", "0.0.0.0")
        port = int(os.getenv("PORT", 8000))
        debug = os.getenv("DEBUG", "True").lower() == "true"

        print("\n" + "=" * 50)
        print("🚀 بدء تشغيل Smart WhatsApp Campaigner Backend")
        print(f"📡 الخادم: http://{host}:{port}")
        print(f"📚 التوثيق: http://{host}:{port}/docs")
        print(f"🔧 وضع التطوير: {'مفعل' if debug else 'معطل'}")

        if not PANDAS_AVAILABLE:
            print("⚠️ تحذير: ميزات رفع الملفات معطلة (pandas غير متوفر)")

        print("=" * 50)
        print("\n🔧 بدء الخادم...")
        print("للإيقاف: اضغط Ctrl+C")
        print("-" * 30)

        # تشغيل الخادم
        uvicorn.run(
            "main:app",
            host=host,
            port=port,
            reload=debug,
            log_level="info" if not debug else "debug"
        )

    except KeyboardInterrupt:
        print("\n\n🔚 تم إيقاف الخادم بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل الخادم: {e}")
        print("\nتفاصيل الخطأ:")
        import traceback
        traceback.print_exc()
        input("\nاضغط Enter للخروج...")
    finally:
        print("🔚 انتهى تشغيل الخادم")
