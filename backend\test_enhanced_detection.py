#!/usr/bin/env python3
"""
اختبار النظام المحسن لاكتشاف الرسائل
"""

import asyncio
import sys
import os
from pathlib import Path
import requests
import json
import time

# إضافة مسار المشروع
sys.path.append(str(Path(__file__).parent))

import logging

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

BASE_URL = "http://localhost:8000"

def test_enhanced_system():
    """اختبار النظام المحسن"""
    
    print("🚀 اختبار النظام المحسن لاكتشاف الرسائل")
    print("=" * 70)
    
    try:
        # 1. فحص حالة الخادم
        print("1️⃣ فحص حالة الخادم...")
        try:
            response = requests.get(f"{BASE_URL}/api/ai-responses/auto-reply/status", timeout=5)
            if response.status_code == 200:
                status = response.json()
                print(f"   ✅ الخادم يعمل")
                print(f"   📊 حالة الردود الذكية: {'تعمل' if status.get('is_running', False) else 'متوقفة'}")
                
                if status.get('is_running', False):
                    print("   💡 الردود الذكية تعمل بالفعل - سنوقفها أولاً")
                    # إيقاف الردود الذكية
                    stop_response = requests.post(
                        f"{BASE_URL}/api/ai-responses/toggle-auto-reply",
                        json={"enabled": False},
                        timeout=10
                    )
                    if stop_response.status_code == 200:
                        print("   ✅ تم إيقاف الردود الذكية")
                        time.sleep(2)
                    
            else:
                print(f"   ❌ خطأ في الخادم: {response.status_code}")
                return False
        except Exception as e:
            print(f"   ❌ لا يمكن الوصول للخادم: {e}")
            return False
        
        # 2. فحص WhatsApp Web
        print("\n2️⃣ فحص WhatsApp Web...")
        try:
            response = requests.get(f"{BASE_URL}/api/whatsapp/status", timeout=5)
            if response.status_code == 200:
                whatsapp_status = response.json()
                print(f"   ✅ WhatsApp Web API متاح")
                print(f"   📱 الجلسة نشطة: {whatsapp_status.get('session_active', False)}")
                print(f"   🔐 مسجل دخول: {whatsapp_status.get('logged_in', False)}")
                
                if not whatsapp_status.get('session_active', False):
                    print("   💡 يرجى تسجيل الدخول في WhatsApp Web من صفحة الإعدادات")
                    return False
            else:
                print(f"   ❌ خطأ في WhatsApp Web: {response.status_code}")
                return False
        except Exception as e:
            print(f"   ❌ خطأ في فحص WhatsApp Web: {e}")
            return False
        
        # 3. تشغيل النظام المحسن
        print("\n3️⃣ تشغيل النظام المحسن...")
        try:
            response = requests.post(
                f"{BASE_URL}/api/ai-responses/toggle-auto-reply",
                json={"enabled": True},
                timeout=15
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"   ✅ استجابة API ناجحة")
                print(f"   📊 النتيجة: {result.get('message', 'غير محدد')}")
                print(f"   🔄 الحالة: {'تعمل' if result.get('is_running', False) else 'متوقفة'}")
                
                if result.get('is_running', False):
                    print("   🎉 تم تشغيل النظام المحسن بنجاح!")
                    
                    # مراقبة النظام لمدة 30 ثانية
                    print("\n4️⃣ مراقبة النظام لمدة 30 ثانية...")
                    print("   💡 الآن يمكنك إرسال رسالة اختبار من هاتف آخر")
                    
                    for i in range(6):  # 6 فحوصات كل 5 ثوانٍ
                        time.sleep(5)
                        
                        # فحص الحالة
                        status_response = requests.get(f"{BASE_URL}/api/ai-responses/auto-reply/status", timeout=5)
                        if status_response.status_code == 200:
                            status = status_response.json()
                            is_running = status.get('is_running', False)
                            print(f"   📊 الفحص {i+1}/6: {'✅ يعمل' if is_running else '❌ متوقف'}")
                            
                            if not is_running:
                                print("   ⚠️ النظام توقف - فحص السجلات للأخطاء")
                                break
                        else:
                            print(f"   ❌ خطأ في فحص الحالة: {status_response.status_code}")
                    
                    print("\n5️⃣ إيقاف النظام...")
                    stop_response = requests.post(
                        f"{BASE_URL}/api/ai-responses/toggle-auto-reply",
                        json={"enabled": False},
                        timeout=10
                    )
                    if stop_response.status_code == 200:
                        print("   ✅ تم إيقاف النظام بنجاح")
                    
                    return True
                else:
                    print("   ❌ فشل في تشغيل النظام المحسن")
                    print(f"   💡 السبب: {result.get('message', 'غير محدد')}")
                    return False
            else:
                print(f"   ❌ خطأ في API: {response.status_code}")
                try:
                    error_detail = response.json()
                    print(f"   📝 التفاصيل: {error_detail}")
                except:
                    print(f"   📝 النص: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في تشغيل النظام: {e}")
            return False
        
    except Exception as e:
        print(f"❌ خطأ عام في الاختبار: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🔧 اختبار التحسينات الجديدة")
    print("=" * 70)
    print("📋 التحسينات المطبقة:")
    print("   ✅ بحث محسن عن المحادثات")
    print("   ✅ فحص شامل للرسائل الواردة")
    print("   ✅ تتبع الرسائل المُعالجة")
    print("   ✅ طرق متعددة للنقر والقراءة")
    print("   ✅ معالجة أخطاء محسنة")
    print()
    
    success = test_enhanced_system()
    
    print("\n" + "=" * 70)
    print("📊 نتائج الاختبار:")
    print(f"   🎯 النظام المحسن: {'✅ نجح' if success else '❌ فشل'}")
    
    if success:
        print("\n🎉 النظام المحسن يعمل بنجاح!")
        print("💡 الآن النظام:")
        print("   ✅ يبحث عن جميع المحادثات")
        print("   ✅ يفحص الرسائل الواردة بدقة")
        print("   ✅ يتجنب معالجة الرسائل المكررة")
        print("   ✅ يستخدم طرق متعددة للتفاعل")
        print("\n📱 لاختبار النظام:")
        print("   1. شغل الردود الذكية من الواجهة")
        print("   2. أرسل رسالة من هاتف آخر")
        print("   3. راقب السجلات في الخادم")
    else:
        print("\n⚠️ ما زالت هناك مشاكل:")
        print("   - تأكد من تسجيل الدخول في WhatsApp Web")
        print("   - تأكد من إعداد OpenAI API Key")
        print("   - فحص السجلات للأخطاء")

if __name__ == "__main__":
    main()
