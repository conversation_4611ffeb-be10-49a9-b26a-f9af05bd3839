#!/usr/bin/env python3
"""
تشخيص حالة WhatsApp Web
"""

import sys
import os
sys.path.insert(0, os.getcwd())

try:
    from whatsapp_selenium import whatsapp_manager
    
    print("🔍 فحص حالة WhatsApp Web...")
    print("=" * 50)
    
    status = whatsapp_manager.get_status()
    
    print(f"📊 الجلسة نشطة: {status.get('session_active', False)}")
    print(f"🔐 مسجل دخول: {status.get('logged_in', False)}")
    print(f"👁️ وضع الخلفية: {status.get('headless', False)}")
    print(f"💬 الرسالة: {status.get('message', 'غير متوفرة')}")
    
    if status.get('session_active') and status.get('logged_in'):
        print("\n✅ WhatsApp Web جاهز للإرسال!")
        
        # اختبار الاتصال
        try:
            from services.whatsapp_web_service import WhatsAppWebService
            service = WhatsAppWebService(whatsapp_manager)
            
            if service._ensure_logged_in():
                print("✅ خدمة WhatsApp Web تعمل بشكل صحيح")
            else:
                print("❌ خدمة WhatsApp Web لا تعمل")
                
        except Exception as e:
            print(f"❌ خطأ في اختبار الخدمة: {e}")
            
    elif status.get('session_active') and not status.get('logged_in'):
        print("\n⚠️ WhatsApp Web مفتوح لكن يحتاج تسجيل دخول")
        print("💡 اذهب للإعدادات وسجل دخول")
    else:
        print("\n❌ WhatsApp Web غير مفتوح")
        print("💡 اذهب للإعدادات وابدأ جلسة جديدة")
    
    print(f"\n🌐 الإعدادات: http://localhost:5173/settings")
    print(f"📢 الحملات: http://localhost:5173/campaigns")
    
except Exception as e:
    print(f"❌ خطأ: {e}")
    print("💡 تأكد من تشغيل الباك إند وتثبيت المكتبات المطلوبة")
