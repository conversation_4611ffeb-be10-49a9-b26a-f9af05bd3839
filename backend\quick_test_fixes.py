#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع للإصلاحات
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_text_cleaning():
    """اختبار تنظيف النص من الأحرف غير المدعومة"""
    print("🧪 [اختبار] تنظيف النص من الأحرف غير المدعومة...")
    
    # محاكاة دالة التنظيف
    def clean_text_for_chrome(text: str) -> str:
        """تنظيف النص من الأحرف غير المدعومة في ChromeDriver"""
        try:
            cleaned_chars = []
            for char in text:
                if ord(char) <= 0xFFFF:
                    cleaned_chars.append(char)
                else:
                    # استبدال الإيموجي بنص بديل
                    if ord(char) >= 0x1F600 and ord(char) <= 0x1F64F:
                        cleaned_chars.append('😊')
                    elif ord(char) >= 0x1F300 and ord(char) <= 0x1F5FF:
                        cleaned_chars.append('🔸')
                    elif ord(char) >= 0x1F680 and ord(char) <= 0x1F6FF:
                        cleaned_chars.append('🚀')
                    elif ord(char) >= 0x2600 and ord(char) <= 0x26FF:
                        cleaned_chars.append('⭐')
            return ''.join(cleaned_chars)
        except Exception as e:
            print(f"خطأ في التنظيف: {e}")
            return text
    
    # نصوص اختبار
    test_texts = [
        "مرحبا 😊 كيف حالك؟",
        "أهلاً وسهلاً 🙌\nأول شي، بحب أعرّفك على نفسي…",
        "نص عادي بدون إيموجي",
        "🚀🔸⭐ رموز متنوعة"
    ]
    
    for i, text in enumerate(test_texts):
        print(f"  📝 [نص {i+1}] الأصلي: {text}")
        cleaned = clean_text_for_chrome(text)
        print(f"  🧹 [نظيف {i+1}] المنظف: {cleaned}")
        
        # فحص الأمان
        all_safe = all(ord(char) <= 0xFFFF for char in cleaned)
        print(f"  {'✅' if all_safe else '❌'} [آمان {i+1}] جميع الأحرف آمنة: {all_safe}")
        print()
    
    print("✅ اكتمل اختبار تنظيف النص\n")

def test_context_cleaning():
    """اختبار تنظيف السياق"""
    print("🧪 [اختبار] تنظيف السياق...")
    
    # محاكاة دالة تنظيف السياق
    def clean_context_message(text: str) -> str:
        """تنظيف رسالة السياق من العناصر غير المرغوبة"""
        import re
        
        if not text:
            return ""
        
        # إزالة الأوقات والتواريخ
        text = re.sub(r'\d{1,2}:\d{2}\s*[صم]?\s*\d{0,4}', '', text)
        text = re.sub(r'\d{4}/\d{1,2}/\d{1,2}', '', text)
        text = re.sub(r'\d{1,2}/\d{1,2}', '', text)
        text = re.sub(r'[،,]\s*$', '', text)
        text = ' '.join(text.split())
        
        return text.strip()
    
    # نصوص سياق اختبار
    test_contexts = [
        "العميل: مرحبا 10:43 م",
        "ai bot: أهلاً وسهلاً، كيف يمكنني مساعدتك؟ 10:44 م",
        "العميل: أريد معلومات عن المنتج 2024/08/06",
        "نص عادي بدون وقت أو تاريخ"
    ]
    
    for i, context in enumerate(test_contexts):
        print(f"  📝 [سياق {i+1}] الأصلي: {context}")
        cleaned = clean_context_message(context)
        print(f"  🧹 [نظيف {i+1}] المنظف: {cleaned}")
        print()
    
    print("✅ اكتمل اختبار تنظيف السياق\n")

def test_imports():
    """اختبار استيراد الوحدات"""
    print("🧪 [اختبار] استيراد الوحدات...")
    
    try:
        from services.auto_reply_service import AutoReplyService
        print("  ✅ تم استيراد AutoReplyService")
    except Exception as e:
        print(f"  ❌ خطأ في استيراد AutoReplyService: {e}")
    
    try:
        from services.whatsapp_web_service import WhatsAppWebService
        print("  ✅ تم استيراد WhatsAppWebService")
    except Exception as e:
        print(f"  ❌ خطأ في استيراد WhatsAppWebService: {e}")
    
    try:
        from services.ai_service import AIService
        print("  ✅ تم استيراد AIService")
    except Exception as e:
        print(f"  ❌ خطأ في استيراد AIService: {e}")
    
    print("✅ اكتمل اختبار الاستيراد\n")

def test_method_signatures():
    """اختبار توقيعات الدوال"""
    print("🧪 [اختبار] توقيعات الدوال...")
    
    try:
        from services.auto_reply_service import AutoReplyService
        import asyncio
        
        service = AutoReplyService()
        
        # فحص الدوال التي يجب أن تكون sync
        sync_methods = ['_is_outgoing_message', '_is_incoming_message', '_extract_message_text_simple']
        
        for method_name in sync_methods:
            if hasattr(service, method_name):
                method = getattr(service, method_name)
                is_async = asyncio.iscoroutinefunction(method)
                print(f"  {'❌' if is_async else '✅'} [{method_name}] async: {is_async}")
            else:
                print(f"  ⚠️ [{method_name}] الدالة غير موجودة")
        
        print("✅ اكتمل اختبار توقيعات الدوال\n")
        
    except Exception as e:
        print(f"  ❌ خطأ في اختبار توقيعات الدوال: {e}\n")

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء الاختبار السريع للإصلاحات...")
    print("=" * 50)
    
    # تشغيل الاختبارات
    test_text_cleaning()
    test_context_cleaning()
    test_imports()
    test_method_signatures()
    
    print("=" * 50)
    print("🎉 اكتمل الاختبار السريع!")

if __name__ == "__main__":
    main()
