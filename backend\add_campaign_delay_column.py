#!/usr/bin/env python3
"""
إضافة عمود campaign_response_delay إلى جدول settings
"""

import sqlite3
import os
from pathlib import Path

def add_campaign_delay_column():
    """إضافة عمود campaign_response_delay إلى جدول settings"""
    
    # مسار قاعدة البيانات
    db_path = Path(__file__).parent / "smart_whatsapp.db"
    
    if not db_path.exists():
        print(f"❌ قاعدة البيانات غير موجودة: {db_path}")
        return False
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # التحقق من وجود العمود
        cursor.execute("PRAGMA table_info(settings)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'campaign_response_delay' in columns:
            print("✅ العمود campaign_response_delay موجود بالفعل")
            return True
        
        # إضافة العمود الجديد
        print("📝 إضافة عمود campaign_response_delay...")
        cursor.execute("""
            ALTER TABLE settings 
            ADD COLUMN campaign_response_delay INTEGER DEFAULT 40
        """)
        
        # تحديث القيم الموجودة
        print("🔄 تحديث القيم الموجودة...")
        cursor.execute("""
            UPDATE settings 
            SET campaign_response_delay = COALESCE(response_delay, 40)
            WHERE campaign_response_delay IS NULL
        """)
        
        # حفظ التغييرات
        conn.commit()
        
        # التحقق من النجاح
        cursor.execute("SELECT campaign_response_delay FROM settings LIMIT 1")
        result = cursor.fetchone()
        
        if result:
            print(f"✅ تم إضافة العمود بنجاح. القيمة الافتراضية: {result[0]}")
        else:
            print("✅ تم إضافة العمود بنجاح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة العمود: {e}")
        return False
        
    finally:
        if 'conn' in locals():
            conn.close()

def main():
    """الدالة الرئيسية"""
    print("🔧 إضافة عمود campaign_response_delay إلى جدول settings")
    print("=" * 60)
    
    success = add_campaign_delay_column()
    
    if success:
        print("\n🎉 تم إنجاز المهمة بنجاح!")
        print("💡 الآن الحملات الإعلانية تستخدم campaign_response_delay")
        print("💡 والردود الذكية تعمل بدون تأخير")
    else:
        print("\n❌ فشل في إنجاز المهمة")

if __name__ == "__main__":
    main()
