"""
اختبار الاستيرادات للتأكد من عمل جميع المكونات
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_imports():
    """اختبار جميع الاستيرادات المطلوبة"""
    
    print("🧪 اختبار الاستيرادات...")
    
    try:
        print("1. اختبار FastAPI...")
        from fastapi import FastAPI
        print("   ✅ FastAPI")
        
        print("2. اختبار قاعدة البيانات...")
        from database import get_db, init_db
        print("   ✅ Database")
        
        print("3. اختبار النماذج...")
        from models import Campaign, Contact, Message, Settings
        print("   ✅ Models")
        
        print("4. اختبار WhatsApp Selenium...")
        from whatsapp_selenium import WhatsAppWebManager, whatsapp_manager
        print("   ✅ WhatsApp Selenium")
        
        print("5. اختبار WhatsApp Routes...")
        from whatsapp_routes import router
        print("   ✅ WhatsApp Routes")
        
        print("6. اختبار WhatsApp Web Service...")
        from services.whatsapp_web_service import WhatsAppWebService
        print("   ✅ WhatsApp Web Service")
        
        print("7. اختبار Campaign Service...")
        from services.campaign_service import CampaignService
        print("   ✅ Campaign Service")
        
        print("8. اختبار التطبيق الرئيسي...")
        from main import app
        print("   ✅ Main App")
        
        print("\n🎉 جميع الاستيرادات تعمل بنجاح!")
        return True
        
    except ImportError as e:
        print(f"\n❌ خطأ في الاستيراد: {e}")
        return False
    except Exception as e:
        print(f"\n❌ خطأ عام: {e}")
        return False

def test_selenium():
    """اختبار Selenium"""
    
    print("\n🧪 اختبار Selenium...")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from webdriver_manager.chrome import ChromeDriverManager
        from selenium.webdriver.chrome.service import Service
        
        print("   ✅ Selenium imports")
        
        # اختبار تحميل ChromeDriver
        service = Service(ChromeDriverManager().install())
        print("   ✅ ChromeDriver download")
        
        print("\n🎉 Selenium جاهز للاستخدام!")
        return True
        
    except Exception as e:
        print(f"\n❌ خطأ في Selenium: {e}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("   اختبار مكونات WhatsApp Web System")
    print("=" * 50)
    
    # اختبار الاستيرادات
    imports_ok = test_imports()
    
    # اختبار Selenium
    selenium_ok = test_selenium()
    
    print("\n" + "=" * 50)
    if imports_ok and selenium_ok:
        print("🎉 جميع المكونات جاهزة! يمكنك تشغيل المشروع الآن.")
        print("\nللتشغيل:")
        print("   start.bat")
    else:
        print("❌ هناك مشاكل تحتاج إلى إصلاح.")
        print("\nللإصلاح:")
        print("   setup_whatsapp_web.bat")
    
    print("=" * 50)
    input("\nاضغط Enter للخروج...")
