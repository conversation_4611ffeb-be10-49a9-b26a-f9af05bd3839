"""
اختبار رفع ملفات العملاء
"""

import os
import sys
import pandas as pd
from sqlalchemy.orm import Session

# إضافة مسار المشروع
sys.path.insert(0, os.getcwd())

from database import get_db, init_db
from models import Campaign, Contact
from services.file_service import FileService

def test_csv_file():
    """اختبار ملف CSV"""
    
    csv_file = '../templates/نموذج_استيراد_العملاء.csv'
    
    print("🧪 اختبار ملف CSV...")
    print("=" * 50)
    
    # التحقق من وجود الملف
    if not os.path.exists(csv_file):
        print("❌ ملف CSV غير موجود")
        return False
    
    print(f"✅ الملف موجود: {csv_file}")
    
    try:
        # قراءة الملف
        df = pd.read_csv(csv_file)
        print(f"📊 عدد الصفوف: {len(df)}")
        print(f"📋 الأعمدة: {list(df.columns)}")
        
        if len(df) > 0:
            print("\n📄 البيانات:")
            for i, row in df.iterrows():
                name = row.get('الاسم', 'غير محدد')
                phone = row.get('رقم الهاتف', 'غير محدد')
                print(f"  {i+1}. {name} - {phone}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في قراءة الملف: {e}")
        return False

def test_file_service():
    """اختبار FileService"""
    
    print("\n🔧 اختبار FileService...")
    print("=" * 50)
    
    try:
        file_service = FileService()
        csv_file = '../templates/نموذج_استيراد_العملاء.csv'
        
        if not os.path.exists(csv_file):
            print("❌ ملف CSV غير موجود")
            return False
        
        # قراءة الملف
        df = file_service.read_excel_file(csv_file)
        print(f"✅ تم قراءة الملف بواسطة FileService")
        print(f"📊 عدد الصفوف: {len(df)}")
        
        # التحقق من صحة البيانات
        validation_result = file_service.validate_contacts_data(df)
        print(f"📋 نتيجة التحقق: {validation_result}")
        
        if validation_result["valid"]:
            print("✅ البيانات صحيحة")
            print(f"📝 الأعمدة المطابقة: {validation_result['normalized_columns']}")
        else:
            print("❌ البيانات غير صحيحة")
            print(f"🚫 الأخطاء: {validation_result['errors']}")
        
        return validation_result["valid"]
        
    except Exception as e:
        print(f"❌ خطأ في FileService: {e}")
        return False

def test_database_upload():
    """اختبار رفع البيانات لقاعدة البيانات"""
    
    print("\n💾 اختبار رفع البيانات لقاعدة البيانات...")
    print("=" * 50)
    
    try:
        # تهيئة قاعدة البيانات
        init_db()
        db = next(get_db())
        
        # البحث عن حملة للاختبار
        campaign = db.query(Campaign).first()
        
        if not campaign:
            print("❌ لا توجد حملات للاختبار")
            print("🔧 إنشاء حملة تجريبية...")
            
            from datetime import datetime
            
            test_campaign = Campaign(
                name='حملة اختبار رفع الملفات',
                message_text='رسالة تجريبية لاختبار رفع الملفات',
                status='pending',
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            db.add(test_campaign)
            db.commit()
            db.refresh(test_campaign)
            
            campaign = test_campaign
            print(f"✅ تم إنشاء حملة تجريبية (ID: {campaign.id})")
        
        print(f"🎯 اختبار الحملة: {campaign.name} (ID: {campaign.id})")
        
        # حذف جهات الاتصال الموجودة للاختبار
        existing_contacts = db.query(Contact).filter(Contact.campaign_id == campaign.id).all()
        for contact in existing_contacts:
            db.delete(contact)
        db.commit()
        
        print(f"🧹 تم حذف {len(existing_contacts)} جهة اتصال موجودة")
        
        # اختبار رفع الملف
        file_service = FileService()
        csv_file = '../templates/نموذج_استيراد_العملاء.csv'
        
        if not os.path.exists(csv_file):
            print("❌ ملف CSV غير موجود")
            return False
        
        # معالجة الملف
        contacts_added = await file_service.process_contacts_file(
            csv_file, campaign.id, db
        )
        
        print(f"✅ تم إضافة {contacts_added} جهة اتصال")
        
        # التحقق من النتائج
        final_contacts = db.query(Contact).filter(Contact.campaign_id == campaign.id).all()
        print(f"📞 إجمالي جهات الاتصال في الحملة: {len(final_contacts)}")
        
        if len(final_contacts) > 0:
            print("📋 جهات الاتصال المضافة:")
            for contact in final_contacts:
                print(f"  - {contact.name}: {contact.phone_number}")
        
        db.close()
        return len(final_contacts) > 0
        
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {e}")
        return False

async def run_all_tests():
    """تشغيل جميع الاختبارات"""
    
    print("🧪 اختبار شامل لرفع ملفات العملاء")
    print("=" * 60)
    
    results = []
    
    # اختبار 1: ملف CSV
    results.append(test_csv_file())
    
    # اختبار 2: FileService
    results.append(test_file_service())
    
    # اختبار 3: قاعدة البيانات
    results.append(await test_database_upload())
    
    print("\n📊 نتائج الاختبارات:")
    print("=" * 60)
    
    test_names = [
        "قراءة ملف CSV",
        "FileService",
        "رفع لقاعدة البيانات"
    ]
    
    all_passed = True
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{i+1}. {name}: {status}")
        if not result:
            all_passed = False
    
    print("\n🎯 النتيجة النهائية:")
    if all_passed:
        print("✅ جميع الاختبارات نجحت!")
        print("💡 رفع الملفات يعمل بشكل صحيح")
    else:
        print("❌ بعض الاختبارات فشلت")
        print("💡 هناك مشكلة في رفع الملفات")
    
    return all_passed

if __name__ == "__main__":
    import asyncio
    
    print("🔧 اختبار رفع ملفات العملاء...")
    print("=" * 60)
    
    # تشغيل الاختبارات
    result = asyncio.run(run_all_tests())
    
    if result:
        print("\n🎉 النظام جاهز لرفع الملفات!")
    else:
        print("\n⚠️ يحتاج النظام لإصلاحات")
    
    print("=" * 60)
    input("اضغط Enter للخروج...")
