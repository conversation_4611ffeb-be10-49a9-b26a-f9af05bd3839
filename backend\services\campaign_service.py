import asyncio
import logging
import random
from typing import Dict, List, Any, Optional
from sqlalchemy.orm import Session
from datetime import datetime
import re

from models import Campaign, Contact, Message, Settings
from services.whatsapp_web_service import WhatsAppWebService
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from whatsapp_selenium import whatsapp_manager

logger = logging.getLogger(__name__)

class CampaignService:
    """خدمة إدارة وإرسال الحملات"""
    
    def __init__(self, db: Session):
        self.db = db
        self.whatsapp_service = WhatsAppWebService(whatsapp_manager)

    def _get_delay_between_messages(self) -> float:
        """الحصول على تأخير عشوائي بين الرسائل للحملات الإعلانية"""
        # الحصول على إعدادات التأخير من قاعدة البيانات
        settings = self.db.query(Settings).first()
        base_delay = settings.campaign_response_delay if settings and settings.campaign_response_delay else 40

        # إضافة تأخير عشوائي بين 30-50 ثانية
        random_delay = random.uniform(30, 50)

        # استخدام الأكبر بين التأخير المُعين والتأخير العشوائي
        total_delay = max(base_delay, random_delay)

        logger.info(f"⏰ تأخير بين الرسائل: {total_delay:.1f} ثانية (إعدادات: {base_delay}، عشوائي: {random_delay:.1f})")
        return total_delay
    
    def _personalize_message(self, message_template: str, contact: Contact) -> str:
        """تخصيص الرسالة للعميل"""
        personalized_message = message_template
        
        # استبدال المتغيرات الأساسية
        personalized_message = personalized_message.replace("{الاسم}", contact.name)
        personalized_message = personalized_message.replace("{name}", contact.name)
        personalized_message = personalized_message.replace("{الهاتف}", contact.phone_number)
        personalized_message = personalized_message.replace("{phone}", contact.phone_number)
        
        if contact.email:
            personalized_message = personalized_message.replace("{الايميل}", contact.email)
            personalized_message = personalized_message.replace("{email}", contact.email)
        
        # استبدال المتغيرات المخصصة من ملف Excel
        if contact.custom_fields:
            for key, value in contact.custom_fields.items():
                personalized_message = personalized_message.replace(f"{{{key}}}", str(value))
        
        return personalized_message
    
    async def send_campaign(self, campaign_id: int) -> Dict[str, Any]:
        """إرسال حملة كاملة"""

        logger.info(f"🚀 بدء إرسال الحملة {campaign_id}")

        # الحصول على الحملة
        campaign = self.db.query(Campaign).filter(Campaign.id == campaign_id).first()
        if not campaign:
            logger.error(f"❌ الحملة {campaign_id} غير موجودة")
            raise ValueError("الحملة غير موجودة")

        logger.info(f"📋 تفاصيل الحملة: {campaign.name} - الحالة: {campaign.status}")

        if campaign.status != "pending":
            logger.error(f"❌ لا يمكن إرسال الحملة. الحالة الحالية: {campaign.status}")
            raise ValueError(f"لا يمكن إرسال الحملة. الحالة الحالية: {campaign.status}")

        # التحقق من خدمة WhatsApp
        if not self.whatsapp_service:
            logger.error("❌ خدمة WhatsApp غير مكونة بشكل صحيح")
            raise ValueError("خدمة WhatsApp غير مكونة بشكل صحيح")

        logger.info("✅ خدمة WhatsApp متوفرة")

        # التحقق من حالة WhatsApp Web
        logger.info("🔍 فحص حالة WhatsApp Web...")
        whatsapp_status = whatsapp_manager.get_status()
        logger.info(f"📊 حالة WhatsApp Web: session_active={whatsapp_status.get('session_active')}, logged_in={whatsapp_status.get('logged_in')}")

        # إذا لم تكن الجلسة نشطة، ابدأ جلسة جديدة
        if not whatsapp_status.get('session_active', False):
            logger.info("🔄 بدء جلسة WhatsApp Web جديدة...")
            try:
                session_result = whatsapp_manager.start_session(headless=False)
                logger.info(f"📊 نتيجة بدء الجلسة: {session_result}")

                if session_result.get('success', False):
                    # إذا كانت تحتاج QR Code، انتظر قليلاً
                    if session_result.get('requires_qr', False):
                        logger.info("⏳ انتظار تسجيل الدخول عبر QR Code...")
                        # إعطاء المستخدم وقت لمسح QR Code
                        import time
                        time.sleep(10)

                        # فحص الحالة مرة أخرى
                        whatsapp_status = whatsapp_manager.get_status()
                        if not whatsapp_status.get('logged_in', False):
                            raise ValueError("يرجى تسجيل الدخول إلى WhatsApp Web من الإعدادات أولاً (مسح QR Code مطلوب)")
                else:
                    raise ValueError("فشل في بدء جلسة WhatsApp Web")
            except Exception as e:
                logger.error(f"❌ خطأ في بدء جلسة WhatsApp Web: {e}")
                raise ValueError("فشل في بدء جلسة WhatsApp Web. يرجى تسجيل الدخول من الإعدادات أولاً.")

        # التحقق النهائي من تسجيل الدخول
        final_status = whatsapp_manager.get_status()
        if not final_status.get('logged_in', False):
            logger.error("❌ WhatsApp Web غير مُسجل دخول")
            raise ValueError("WhatsApp Web غير مُسجل دخول. يرجى تسجيل الدخول من الإعدادات أولاً.")

        logger.info("✅ WhatsApp Web جاهز للإرسال")

        # الحصول على العملاء
        logger.info("🔍 جلب جهات الاتصال...")
        contacts = self.db.query(Contact).filter(Contact.campaign_id == campaign_id).all()
        logger.info(f"📊 عدد جهات الاتصال: {len(contacts)}")

        if not contacts:
            logger.error("❌ لا توجد جهات اتصال للحملة")
            raise ValueError("لا توجد جهات اتصال للحملة")
        
        # تحديث حالة الحملة
        campaign.status = "running"
        campaign.updated_at = datetime.now()
        self.db.commit()
        
        # إعداد الرسائل للإرسال
        messages_to_send = []
        for contact in contacts:
            personalized_message = self._personalize_message(campaign.message_text, contact)
            
            message_data = {
                "type": "text",
                "to_phone": contact.phone_number,
                "message": personalized_message
            }
            
            # إضافة صورة إذا كانت متوفرة
            if campaign.image_path:
                message_data = {
                    "type": "image",
                    "to_phone": contact.phone_number,
                    "image_url": campaign.image_path,
                    "caption": personalized_message
                }
            
            messages_to_send.append({
                "contact": contact,
                "message_data": message_data,
                "personalized_message": personalized_message
            })
        
        # إرسال الرسائل
        sent_count = 0
        delivered_count = 0
        failed_count = 0

        logger.info(f"📤 بدء إرسال {len(messages_to_send)} رسالة...")

        try:
            # إرسال الرسائل بشكل متتالي مع تأخير
            for i, item in enumerate(messages_to_send, 1):
                contact = item["contact"]
                message_data = item["message_data"]
                personalized_message = item["personalized_message"]

                logger.info(f"📱 إرسال رسالة {i}/{len(messages_to_send)} إلى {contact.name} ({contact.phone_number})")
                
                try:
                    # إرسال الرسالة (WhatsApp Web يدعم النص فقط حالياً)
                    if message_data["type"] == "text":
                        result = await self.whatsapp_service.send_text_message(
                            message_data["to_phone"],
                            message_data["message"]
                        )
                    elif message_data["type"] == "image":
                        # إرسال النص مع تنبيه عن الصورة
                        caption = message_data.get("caption", "")
                        full_message = f"{caption}\n\n[ملاحظة: إرسال الصور غير مدعوم حالياً عبر WhatsApp Web]"
                        result = await self.whatsapp_service.send_text_message(
                            message_data["to_phone"],
                            full_message
                        )
                    
                    # حفظ الرسالة في قاعدة البيانات
                    message_record = Message(
                        campaign_id=campaign_id,
                        contact_id=contact.id,
                        message_text=personalized_message,
                        message_type="campaign"
                    )
                    
                    if result.get("success", False):
                        # نجح الإرسال
                        message_record.status = "sent"
                        message_record.whatsapp_message_id = result.get("message_id")
                        message_record.sent_at = datetime.now()
                        sent_count += 1
                        delivered_count += 1  # افتراض التسليم للآن

                        logger.info(f"✅ تم إرسال رسالة بنجاح إلى {contact.phone_number}")
                    else:
                        # فشل الإرسال
                        message_record.status = "failed"
                        error_msg = result.get("error", "خطأ غير معروف")
                        message_record.error_message = str(error_msg)
                        failed_count += 1

                        logger.error(f"❌ فشل إرسال رسالة إلى {contact.phone_number}: {error_msg}")
                    
                    self.db.add(message_record)

                    # تأخير ذكي بين الرسائل (إذا لم تكن الرسالة الأخيرة)
                    if i < len(messages_to_send):
                        delay = self._get_delay_between_messages()
                        logger.info(f"⏳ انتظار {delay:.1f} ثانية قبل الرسالة التالية...")
                        await asyncio.sleep(delay)
                    
                except Exception as e:
                    logger.error(f"خطأ في إرسال رسالة للعميل {contact.phone_number}: {str(e)}")
                    
                    # حفظ الرسالة الفاشلة
                    message_record = Message(
                        campaign_id=campaign_id,
                        contact_id=contact.id,
                        message_text=personalized_message,
                        message_type="campaign",
                        status="failed",
                        error_message=str(e)
                    )
                    self.db.add(message_record)
                    failed_count += 1
            
            # تحديث إحصائيات الحملة
            campaign.sent_count = sent_count
            campaign.delivered_count = delivered_count
            campaign.failed_count = failed_count
            campaign.status = "completed"
            campaign.updated_at = datetime.now()
            
            self.db.commit()
            
            return {
                "campaign_id": campaign_id,
                "total_contacts": len(contacts),
                "sent_count": sent_count,
                "delivered_count": delivered_count,
                "failed_count": failed_count,
                "success_rate": (delivered_count / len(contacts) * 100) if contacts else 0
            }
            
        except Exception as e:
            # تحديث حالة الحملة في حالة الخطأ
            campaign.status = "failed"
            campaign.updated_at = datetime.now()
            self.db.commit()
            
            logger.error(f"خطأ في إرسال الحملة {campaign_id}: {str(e)}")
            raise e
    
    async def send_single_message(
        self, 
        contact_id: int, 
        message_text: str,
        message_type: str = "manual"
    ) -> Dict[str, Any]:
        """إرسال رسالة واحدة"""
        
        # الحصول على العميل
        contact = self.db.query(Contact).filter(Contact.id == contact_id).first()
        if not contact:
            raise ValueError("العميل غير موجود")
        
        # التحقق من خدمة WhatsApp
        if not self.whatsapp_service:
            raise ValueError("خدمة WhatsApp غير مكونة بشكل صحيح")
        
        try:
            # إرسال الرسالة
            result = await self.whatsapp_service.send_text_message(
                contact.phone_number,
                message_text
            )
            
            # حفظ الرسالة في قاعدة البيانات
            message_record = Message(
                contact_id=contact_id,
                message_text=message_text,
                message_type=message_type
            )
            
            if result.get("success", False):
                message_record.status = "sent"
                message_record.whatsapp_message_id = result.get("message_id")
                message_record.sent_at = datetime.now()
            else:
                message_record.status = "failed"
                message_record.error_message = str(result.get("error", "خطأ غير معروف"))
            
            self.db.add(message_record)
            self.db.commit()
            
            return {
                "success": result.get("success", False),
                "message_id": message_record.id,
                "whatsapp_message_id": message_record.whatsapp_message_id,
                "result": result
            }

        except Exception as e:
            logger.error(f"خطأ في إرسال رسالة للعميل {contact.phone_number}: {str(e)}")
            raise e

    async def retry_failed_messages(self, campaign_id: int) -> Dict[str, Any]:
        """إعادة إرسال الرسائل الفاشلة للحملة"""

        # الحصول على الحملة
        campaign = self.db.query(Campaign).filter(Campaign.id == campaign_id).first()
        if not campaign:
            raise ValueError("الحملة غير موجودة")

        # التحقق من خدمة WhatsApp
        if not self.whatsapp_service:
            raise ValueError("خدمة WhatsApp غير مكونة بشكل صحيح")

        # التحقق من حالة WhatsApp Web
        whatsapp_status = whatsapp_manager.get_status()
        if not whatsapp_status.get('session_active', False) or not whatsapp_status.get('logged_in', False):
            raise ValueError("WhatsApp Web غير مُسجل دخول. يرجى تسجيل الدخول من الإعدادات أولاً.")

        # الحصول على الرسائل الفاشلة
        failed_messages = self.db.query(Message).filter(
            Message.campaign_id == campaign_id,
            Message.status == "failed"
        ).all()

        if not failed_messages:
            raise ValueError("لا توجد رسائل فاشلة لإعادة إرسالها")

        logger.info(f"بدء إعادة إرسال {len(failed_messages)} رسالة فاشلة للحملة {campaign_id}")

        # إحصائيات إعادة الإرسال
        retry_sent_count = 0
        retry_failed_count = 0

        try:
            for message in failed_messages:
                try:
                    # الحصول على جهة الاتصال
                    contact = self.db.query(Contact).filter(Contact.id == message.contact_id).first()
                    if not contact:
                        logger.warning(f"جهة الاتصال {message.contact_id} غير موجودة")
                        continue

                    # تخصيص الرسالة
                    personalized_message = self._personalize_message(campaign.message_text, contact)

                    # إرسال الرسالة
                    if campaign.image_path:
                        # رسالة مع صورة
                        result = await self.whatsapp_service.send_image_message(
                            contact.phone_number,
                            campaign.image_path,
                            personalized_message
                        )
                    else:
                        # رسالة نصية
                        result = await self.whatsapp_service.send_text_message(
                            contact.phone_number,
                            personalized_message
                        )

                    # تحديث حالة الرسالة
                    if result.get("success", False):
                        # نجح الإرسال
                        message.status = "sent"
                        message.whatsapp_message_id = result.get("message_id")
                        message.sent_at = datetime.now()
                        message.error_message = None  # مسح رسالة الخطأ السابقة
                        retry_sent_count += 1

                        logger.info(f"تم إعادة إرسال رسالة بنجاح إلى {contact.phone_number}")
                    else:
                        # فشل الإرسال مرة أخرى
                        error_msg = result.get('error', 'خطأ غير معروف')
                        message.error_message = f"إعادة المحاولة فشلت: {error_msg}"
                        retry_failed_count += 1

                        logger.error(f"فشل في إعادة إرسال رسالة إلى {contact.phone_number}: {error_msg}")

                    # حفظ التحديثات
                    self.db.commit()

                    # تأخير ذكي بين الرسائل
                    delay = self._get_delay_between_messages()
                    logger.info(f"⏳ انتظار {delay:.1f} ثانية قبل إعادة إرسال الرسالة التالية...")
                    await asyncio.sleep(delay)

                except Exception as e:
                    logger.error(f"خطأ في إعادة إرسال رسالة للعميل {contact.phone_number}: {str(e)}")

                    # تحديث رسالة الخطأ
                    message.error_message = f"خطأ في إعادة المحاولة: {str(e)}"
                    self.db.commit()
                    retry_failed_count += 1

            # تحديث إحصائيات الحملة
            campaign.sent_count = campaign.sent_count + retry_sent_count
            campaign.failed_count = campaign.failed_count - retry_sent_count + retry_failed_count
            campaign.delivered_count = campaign.delivered_count + retry_sent_count
            campaign.updated_at = datetime.now()

            self.db.commit()

            logger.info(f"انتهت إعادة الإرسال للحملة {campaign_id}: نجح {retry_sent_count}, فشل {retry_failed_count}")

            return {
                "campaign_id": campaign_id,
                "total_retry_attempts": len(failed_messages),
                "retry_sent_count": retry_sent_count,
                "retry_failed_count": retry_failed_count,
                "success_rate": (retry_sent_count / len(failed_messages) * 100) if failed_messages else 0
            }

        except Exception as e:
            logger.error(f"خطأ في إعادة إرسال الرسائل الفاشلة للحملة {campaign_id}: {str(e)}")
            raise e
    
    def get_campaign_progress(self, campaign_id: int) -> Dict[str, Any]:
        """الحصول على تقدم الحملة"""
        
        campaign = self.db.query(Campaign).filter(Campaign.id == campaign_id).first()
        if not campaign:
            raise ValueError("الحملة غير موجودة")
        
        # إحصائيات الرسائل
        total_messages = self.db.query(Message).filter(Message.campaign_id == campaign_id).count()
        sent_messages = self.db.query(Message).filter(
            Message.campaign_id == campaign_id,
            Message.status == "sent"
        ).count()
        delivered_messages = self.db.query(Message).filter(
            Message.campaign_id == campaign_id,
            Message.status == "delivered"
        ).count()
        failed_messages = self.db.query(Message).filter(
            Message.campaign_id == campaign_id,
            Message.status == "failed"
        ).count()
        
        progress_percentage = 0
        if campaign.total_contacts > 0:
            progress_percentage = (total_messages / campaign.total_contacts) * 100
        
        return {
            "campaign_id": campaign_id,
            "campaign_name": campaign.name,
            "status": campaign.status,
            "total_contacts": campaign.total_contacts,
            "progress_percentage": round(progress_percentage, 2),
            "messages_sent": sent_messages,
            "messages_delivered": delivered_messages,
            "messages_failed": failed_messages,
            "success_rate": round(
                (delivered_messages / total_messages * 100), 2
            ) if total_messages > 0 else 0
        }
    
    async def test_whatsapp_connection(self) -> Dict[str, Any]:
        """اختبار الاتصال مع WhatsApp"""
        if not self.whatsapp_service:
            return {
                "status": "error",
                "message": "خدمة WhatsApp غير مكونة"
            }
        
        return await self.whatsapp_service.test_connection()
