{"NewTabPage": {"PrevNavigationTime": "*****************"}, "accessibility": {"captions": {"headless_caption_enabled": false, "live_caption_language": "en-US"}}, "account_tracker_service_last_update": "*****************", "alternate_error_pages": {"backup": false}, "autocomplete": {"retention_policy_last_version": 138}, "autofill": {"last_version_deduped": 138}, "bookmark": {"storage_computation_last_update": "*****************"}, "browser": {"check_default_browser": false, "window_placement": {"bottom": 824, "left": 608, "maximized": false, "right": 1544, "top": 0, "work_area_bottom": 816, "work_area_left": 0, "work_area_right": 1536, "work_area_top": 0}}, "commerce_daily_metrics_last_update_time": "*****************", "countryid_at_install": 21586, "default_search_provider": {"guid": ""}, "distribution": {"import_bookmarks": false, "import_history": false, "import_search_engine": false, "make_chrome_default_for_user": false, "skip_first_run_ui": true}, "dns_prefetching": {"enabled": false}, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "enterprise_profile_guid": "d32773c4-61e4-43ce-8056-8463d2a36676", "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}}, "gaia_cookie": {"changed_time": **********.512688, "hash": "2jmj7l5rSw0yVb/vlWAYkK/YBwk=", "last_list_accounts_data": "[\"gaia.l.a.r\",[]]"}, "gcm": {"product_category_for_subtypes": "com.chrome.windows"}, "google": {"services": {"signin_scoped_device_id": "3e19a41b-c012-4b55-9f83-a90646f3aeb7"}}, "in_product_help": {"new_badge": {"Compose": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeProactiveNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "LensOverlay": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "PasswordManualFallbackAvailable": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}}, "recent_session_enabled_time": "*****************", "recent_session_start_times": ["*****************", "*****************", "*****************"], "session_last_active_time": "*****************", "session_start_time": "*****************", "snoozed_feature": {"IPH_LiveCaption": {"first_show_time": "13398980886649246", "is_dismissed": false, "last_dismissed_by": 3, "last_show_time": "13398980886649246", "last_snooze_time": "0", "promo_index": 0, "show_count": 1, "shown_for_apps": [], "snooze_count": 0}}}, "intl": {"selected_languages": "ar,en-US,en"}, "invalidation": {"per_sender_topics_to_handler": {"1013309121859": {}}}, "language_model_counters": {"ar": 17}, "media": {"engagement": {"schema_version": 5}}, "media_router": {"receiver_id_hash_token": "uUrht5nZdRC9wwfY2mgxSlVtkrr3cwCvpbnP5HqrgfWD1tibK7nKbSebFBVjPfBffP377CSA+C+dxVaqoourmw=="}, "migrated_user_scripts_toggle": true, "ntp": {"num_personal_suggestions": 3}, "optimization_guide": {"hintsfetcher": {"hosts_successfully_fetched": {}}, "predictionmodelfetcher": {"last_fetch_attempt": "13398984698216226", "last_fetch_success": "13398984698667352"}, "previous_optimization_types_with_filter": {"AMERICAN_EXPRESS_CREDIT_CARD_FLIGHT_BENEFITS": true, "AMERICAN_EXPRESS_CREDIT_CARD_SUBSCRIPTION_BENEFITS": true, "AUTOFILL_ABLATION_SITES_LIST1": true, "AUTOFILL_ABLATION_SITES_LIST2": true, "AUTOFILL_ABLATION_SITES_LIST3": true, "AUTOFILL_ABLATION_SITES_LIST4": true, "AUTOFILL_ABLATION_SITES_LIST5": true, "AUTOFILL_PREDICTION_IMPROVEMENTS_ALLOWLIST": true, "BMO_CREDIT_CARD_AIR_MILES_PARTNER_BENEFITS": true, "BMO_CREDIT_CARD_ALCOHOL_STORE_BENEFITS": true, "BMO_CREDIT_CARD_DINING_BENEFITS": true, "BMO_CREDIT_CARD_DRUGSTORE_BENEFITS": true, "BMO_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "BMO_CREDIT_CARD_GROCERY_BENEFITS": true, "BMO_CREDIT_CARD_OFFICE_SUPPLY_BENEFITS": true, "BMO_CREDIT_CARD_RECURRING_BILL_BENEFITS": true, "BMO_CREDIT_CARD_TRANSIT_BENEFITS": true, "BMO_CREDIT_CARD_TRAVEL_BENEFITS": true, "BMO_CREDIT_CARD_WHOLESALE_CLUB_BENEFITS": true, "BUY_NOW_PAY_LATER_ALLOWLIST_AFFIRM": true, "BUY_NOW_PAY_LATER_ALLOWLIST_ZIP": true, "CAPITAL_ONE_CREDIT_CARD_BENEFITS_BLOCKED": true, "CAPITAL_ONE_CREDIT_CARD_DINING_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_GROCERY_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_STREAMING_BENEFITS": true, "DIGITAL_CREDENTIALS_LOW_FRICTION": true, "EWALLET_MERCHANT_ALLOWLIST": true, "GLIC_ACTION_PAGE_BLOCK": true, "HISTORY_CLUSTERS": true, "HISTORY_EMBEDDINGS": true, "IBAN_AUTOFILL_BLOCKED": true, "PIX_MERCHANT_ORIGINS_ALLOWLIST": true, "PIX_PAYMENT_MERCHANT_ALLOWLIST": true, "SHARED_CREDIT_CARD_DINING_BENEFITS": true, "SHARED_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "SHARED_CREDIT_CARD_FLIGHT_BENEFITS": true, "SHARED_CREDIT_CARD_GROCERY_BENEFITS": true, "SHARED_CREDIT_CARD_STREAMING_BENEFITS": true, "SHARED_CREDIT_CARD_SUBSCRIPTION_BENEFITS": true, "SHOPPING_PAGE_PREDICTOR": true, "TEXT_CLASSIFIER_ENTITY_DETECTION": true, "VCN_MERCHANT_OPT_OUT_DISCOVER": true, "VCN_MERCHANT_OPT_OUT_MASTERCARD": true, "VCN_MERCHANT_OPT_OUT_VISA": true}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "LOADING_PREDICTOR": true, "MERCHANT_TRUST_SIGNALS_V2": true, "PAGE_ENTITIES": true, "PRICE_TRACKING": true, "SAVED_TAB_GROUP": true, "V8_COMPILE_HINTS": true}, "store_file_paths_to_delete": {}}, "password_manager": {"account_store_migrated_to_os_crypt_async": true, "autofillable_credentials_account_store_login_database": false, "autofillable_credentials_profile_store_login_database": false, "profile_store_migrated_to_os_crypt_async": true}, "privacy_sandbox": {"fake_notice": {"prompt_shown_time": "*****************", "prompt_shown_time_sync": "*****************"}, "first_party_sets_data_access_allowed_initialized": true}, "profile": {"avatar_index": 26, "background_password_check": {"check_fri_weight": 9, "check_interval": "*************", "check_mon_weight": 6, "check_sat_weight": 6, "check_sun_weight": 6, "check_thu_weight": 9, "check_tue_weight": 9, "check_wed_weight": 9, "next_check_time": "*****************"}, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {"https://web.whatsapp.com:443,*": {"last_modified": "*****************", "setting": {"https://web.whatsapp.com/": {"couldShowBannerEvents": 1.************407e+16, "next_install_text_animation": {"delay": "345600000000", "last_shown": "13398971754122813"}}}}}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "client_hints": {"https://web.whatsapp.com:443,*": {"last_modified": "*****************", "setting": {"client_hints": [1, 3, 11, 14, 15, 23]}}}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {"https://[*.]whatsapp.com,*": {"last_modified": "*****************", "setting": {}}}, "cookies": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {"https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"chosen-objects": [{"idp-origin": "https://accounts.google.com", "idp-signin-status": false}]}}}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "initialized_translations": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "local_network_access": {}, "media_engagement": {"https://web.whatsapp.com:443,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 10}}}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "ondevice_languages_downloaded": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {"https://web.whatsapp.com:443,*": {"last_modified": "13398976304792963", "setting": {"Notifications": {"dismiss_count": 1}}}}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protected_media_identifier": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"chrome://newtab/,*": {"last_modified": "13398984688590702", "setting": {"lastEngagementTime": 1.3398984688590684e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 15.0, "rawScore": 51.4482738722006}}, "https://web.whatsapp.com:443,*": {"last_modified": "13398986967035735", "setting": {"lastEngagementTime": 1.3398986967035716e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 15.0, "rawScore": 53.9605498025039}}}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "suspicious_notification_ids": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "pattern_pairs": {"https://*,*": {"media-stream": {"audio": "<PERSON><PERSON><PERSON>", "video": "<PERSON><PERSON><PERSON>"}}}, "permission_actions": {"notifications": [{"action": 2, "prompt_disposition": 12, "time": "13398976304792941"}]}, "pref_version": 1}, "creation_time": "13398031789098893", "default_content_setting_values": {"geolocation": 1}, "default_content_settings": {"geolocation": 1, "mouselock": 1, "notifications": 1, "popups": 1, "ppapi-broker": 1}, "exit_type": "SessionEnded", "family_member_role": "not_in_family", "last_engagement_time": "13398986967035715", "last_time_obsolete_http_credentials_removed": 1753558249.895083, "last_time_password_store_metrics_reported": 1754498167.853589, "managed": {"locally_parent_approved_extensions": {}, "locally_parent_approved_extensions_migration_state": 1}, "managed_user_id": "", "name": "‏بياناتك على Chrome", "password_hash_data_list": [], "password_manager_enabled": false, "were_old_google_logins_removed": true}, "safebrowsing": {"enabled": false, "event_timestamps": {}, "metrics_last_log_time": "13398971737", "scout_reporting_enabled_when_deprecated": false}, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "saved_tab_groups": {"did_enable_shared_tab_groups_in_last_session": false, "specifics_to_data_migration": true}, "search": {"suggest_enabled": false}, "segmentation_platform": {"client_result_prefs": "CuUCChFjcm9zc19kZXZpY2VfdXNlchLPAgrDAg0AAIA/EOm/l7mzyeYXGrACCqcCGqQCChkNAACAPxISTm9Dcm9zc0RldmljZVVzYWdlChgNAAAAQBIRQ3Jvc3NEZXZpY2VNb2JpbGUKGQ0AAEBAEhJDcm9zc0RldmljZURlc2t0b3AKGA0AAIBAEhFDcm9zc0RldmljZVRhYmxldAoiDQAAoEASG0Nyb3NzRGV2aWNlTW9iaWxlQW5kRGVza3RvcAohDQAAwEASGkNyb3NzRGV2aWNlTW9iaWxlQW5kVGFibGV0CiINAADgQBIbQ3Jvc3NEZXZpY2VEZXNrdG9wQW5kVGFibGV0CiANAAAAQRIZQ3Jvc3NEZXZpY2VBbGxEZXZpY2VUeXBlcwoXDQAAEEESEENyb3NzRGV2aWNlT3RoZXISEk5vQ3Jvc3NEZXZpY2VVc2FnZRIEEAcYBCACEI7Bl7mzyeYXCmQKC3NlYXJjaF91c2VyElUKSg0AAAAAEPDGl7mzyeYXGjgKMBouCgoNAACAPxIDTG93Cg0NAACgQBIGTWVkaXVtCgsNAACwQRIESGlnaBIETm9uZRIEEAcYBCACEITIl7mzyeYXCnMKFXBhc3N3b3JkX21hbmFnZXJfdXNlchJaCk8NAAAAABDiwZe5s8nmFxo9CjUKMw0AAAA/EhNQYXNzd29yZE1hbmFnZXJVc2VyGhdOb3RfUGFzc3dvcmRNYW5hZ2VyVXNlchIEEAcYBCABEOTHl7mzyeYXClIKDXNob3BwaW5nX3VzZXISQQo2DQAAAAAQp7mXubPJ5hcaJAocChoNAAAAPxIMU2hvcHBpbmdVc2VyGgVPdGhlchIEEAIYBCADEO+9l7mzyeYXCmAKEXJlc3VtZV9oZWF2eV91c2VyEksKQA0AAAAAENnAl7mzyeYXGi4KJgokDQAAAD8SFlJlc3VtZUhlYXZ5VXNlclNlZ21lbnQaBU90aGVyEgQQDhgEIAIQvcGXubPJ5hc=", "device_switcher_util": {"result": {"labels": ["NotSynced"]}}, "last_db_compaction_time": "13398825599000000", "uma_in_sql_start_time": "13398031789896559"}, "sessions": {"event_log": [{"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13398143920174434", "type": 2, "window_count": 1}, {"crashed": false, "time": "13398971737830659", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13398973497828528", "type": 2, "window_count": 1}, {"crashed": false, "time": "13398974601160001", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13398975054686558", "type": 2, "window_count": 1}, {"crashed": false, "time": "13398975065334534", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13398975349724198", "type": 2, "window_count": 1}, {"crashed": false, "time": "13398976105568540", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13398976320353337", "type": 2, "window_count": 1}, {"crashed": false, "time": "13398980761076380", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13398982247514042", "type": 2, "window_count": 1}, {"crashed": false, "time": "13398982531536376", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13398982821349417", "type": 2, "window_count": 1}, {"crashed": false, "time": "13398982982850099", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13398983423162134", "type": 2, "window_count": 1}, {"crashed": false, "time": "13398984029418114", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "*****************", "type": 2, "window_count": 1}, {"crashed": false, "time": "*****************", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "*****************", "type": 2, "window_count": 1}], "session_data_status": 1}, "settings": {"force_google_safesearch": false}, "signin": {"allowed": true, "cookie_clear_on_exit_migration_notice_complete": true}, "spellcheck": {"dictionaries": ["en-US"], "dictionary": ""}, "sync": {"passwords_per_account_pref_migration_done": true}, "syncing_theme_prefs_migrated_to_non_syncing": true, "toolbar": {"pinned_cast_migration_complete": true, "pinned_chrome_labs_migration_complete": true}, "total_passwords_available_for_account": 0, "total_passwords_available_for_profile": 0, "translate": {"enabled": false}, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "web_apps": {"daily_metrics": {"https://web.whatsapp.com/": {"background_duration_sec": 0, "captures_links": false, "effective_display_mode": 3, "foreground_duration_sec": 0, "installed": false, "num_sessions": 0, "promotable": true}}, "daily_metrics_date": "*****************", "did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "138"}}