#!/usr/bin/env python3
"""
اختبار endpoint تفعيل الردود التلقائية
"""

import requests
import json

def test_toggle_endpoint():
    """اختبار endpoint toggle-auto-reply"""
    
    base_url = "http://localhost:8000"
    
    print("🧪 اختبار endpoint تفعيل الردود التلقائية")
    print("=" * 50)
    
    # 1. فحص الحالة الحالية
    print("1️⃣ فحص الحالة الحالية...")
    try:
        response = requests.get(f"{base_url}/api/ai-responses/settings/current")
        if response.status_code == 200:
            settings = response.json()
            current_status = settings.get('auto_reply_enabled', False)
            print(f"   📊 الحالة الحالية: {'مفعل' if current_status else 'معطل'}")
        else:
            print(f"   ❌ خطأ في جلب الإعدادات: {response.status_code}")
            return
    except Exception as e:
        print(f"   ❌ خطأ في الاتصال: {e}")
        return
    
    # 2. اختبار تبديل الحالة
    print("\n2️⃣ اختبار تبديل الحالة...")
    try:
        response = requests.post(f"{base_url}/api/ai-responses/toggle-auto-reply")
        print(f"   📊 كود الاستجابة: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("   ✅ تم تبديل الحالة بنجاح")
            print(f"   📝 الرسالة: {data.get('message', 'غير متوفرة')}")
            print(f"   🔄 الحالة الجديدة: {'مفعل' if data.get('auto_reply_enabled') else 'معطل'}")
            print(f"   🤖 حالة الخدمة: {'تعمل' if data.get('service_running') else 'متوقفة'}")
            
            if 'error' in data:
                print(f"   ⚠️ تحذير: {data['error']}")
                
        else:
            print(f"   ❌ فشل في تبديل الحالة")
            try:
                error_data = response.json()
                print(f"   📝 تفاصيل الخطأ: {error_data}")
            except:
                print(f"   📝 نص الخطأ: {response.text}")
                
    except Exception as e:
        print(f"   ❌ خطأ في الاتصال: {e}")
    
    # 3. فحص حالة الخدمة
    print("\n3️⃣ فحص حالة الخدمة...")
    try:
        response = requests.get(f"{base_url}/api/ai-responses/auto-reply/status")
        print(f"   📊 كود الاستجابة: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("   ✅ تم جلب حالة الخدمة")
            print(f"   🔄 حالة المراقبة: {'تعمل' if data.get('is_running') else 'متوقفة'}")
            print(f"   📝 الرسالة: {data.get('message', 'غير متوفرة')}")
            print(f"   🎯 حالة المهمة: {'نشطة' if data.get('monitor_task_active') else 'غير نشطة'}")
        else:
            print(f"   ❌ فشل في جلب حالة الخدمة: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ خطأ في فحص حالة الخدمة: {e}")
    
    print(f"\n" + "=" * 50)
    print("انتهى اختبار endpoint")

if __name__ == "__main__":
    test_toggle_endpoint()
