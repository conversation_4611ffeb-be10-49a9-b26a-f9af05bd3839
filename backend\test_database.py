#!/usr/bin/env python3
"""
اختبار الاتصال بقاعدة البيانات
"""

from database import SessionLocal, get_db
from models import Campaign, Contact, Message, Settings, AIResponse
from sqlalchemy import text

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات وجلب البيانات"""
    
    print("🔍 اختبار الاتصال بقاعدة البيانات")
    print("=" * 50)
    
    try:
        db = SessionLocal()
        
        # 1. اختبار الاتصال الأساسي
        print("1️⃣ اختبار الاتصال الأساسي...")
        result = db.execute(text("SELECT 1")).fetchone()
        if result:
            print("   ✅ الاتصال بقاعدة البيانات يعمل")
        
        # 2. اختبار جلب الإعدادات
        print("\n2️⃣ اختبار جلب الإعدادات...")
        settings = db.query(Settings).first()
        if settings:
            print(f"   ✅ تم جلب الإعدادات - ID: {settings.id}")
            print(f"   📊 نموذج GPT: {settings.gpt_model}")
            print(f"   🔄 الرد التلقائي: {'مفعل' if settings.auto_reply_enabled else 'معطل'}")
        else:
            print("   ⚠️ لا توجد إعدادات في قاعدة البيانات")
        
        # 3. اختبار جلب الحملات
        print("\n3️⃣ اختبار جلب الحملات...")
        campaigns = db.query(Campaign).all()
        print(f"   📊 عدد الحملات: {len(campaigns)}")
        for campaign in campaigns[:3]:  # أول 3 حملات
            print(f"   📋 حملة: {campaign.name} - الحالة: {campaign.status}")
        
        # 4. اختبار جلب جهات الاتصال
        print("\n4️⃣ اختبار جلب جهات الاتصال...")
        contacts = db.query(Contact).all()
        print(f"   📊 عدد جهات الاتصال: {len(contacts)}")
        for contact in contacts[:3]:  # أول 3 جهات اتصال
            print(f"   👤 جهة اتصال: {contact.name} - {contact.phone_number}")
        
        # 5. اختبار جلب الرسائل
        print("\n5️⃣ اختبار جلب الرسائل...")
        messages = db.query(Message).all()
        print(f"   📊 عدد الرسائل: {len(messages)}")
        for message in messages[:3]:  # أول 3 رسائل
            print(f"   💬 رسالة: {message.content[:50]}... - الحالة: {message.status}")
        
        # 6. اختبار جلب الردود التلقائية
        print("\n6️⃣ اختبار جلب الردود التلقائية...")
        ai_responses = db.query(AIResponse).all()
        print(f"   📊 عدد الردود التلقائية: {len(ai_responses)}")
        for response in ai_responses[:3]:  # أول 3 ردود
            print(f"   🤖 رد: {response.ai_response[:50]}... - الهاتف: {response.contact_phone}")
        
        db.close()
        
        print(f"\n" + "=" * 50)
        print("✅ اختبار قاعدة البيانات مكتمل بنجاح")
        
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_database_connection()
