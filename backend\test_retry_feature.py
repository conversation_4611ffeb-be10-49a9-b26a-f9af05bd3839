#!/usr/bin/env python3
"""
اختبار ميزة إعادة الإرسال للرسائل الفاشلة
"""

import sys
import os
import asyncio
from datetime import datetime

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.getcwd())

from database import get_db, SessionLocal
from models import Campaign, Contact, Message
from services.campaign_service import CampaignService
from whatsapp_selenium import whatsapp_manager

def create_test_campaign_with_failed_messages():
    """إنشاء حملة تجريبية مع رسائل فاشلة"""
    
    print("🧪 إنشاء حملة تجريبية مع رسائل فاشلة...")
    
    db = SessionLocal()
    try:
        # إنشاء حملة تجريبية
        campaign = Campaign(
            name="حملة اختبار إعادة الإرسال",
            message_text="مرحباً {الاسم}، هذه رسالة اختبار",
            status="completed",
            total_contacts=3,
            sent_count=1,
            delivered_count=1,
            failed_count=2
        )
        db.add(campaign)
        db.commit()
        db.refresh(campaign)
        
        print(f"✅ تم إنشاء الحملة: {campaign.id}")
        
        # إنشاء جهات اتصال تجريبية
        contacts = [
            Contact(
                name="أحمد محمد",
                phone_number="+966501234567",
                campaign_id=campaign.id
            ),
            Contact(
                name="فاطمة علي", 
                phone_number="+966509876543",
                campaign_id=campaign.id
            ),
            Contact(
                name="محمد سالم",
                phone_number="+966507654321", 
                campaign_id=campaign.id
            )
        ]
        
        for contact in contacts:
            db.add(contact)
        db.commit()
        
        print(f"✅ تم إنشاء {len(contacts)} جهة اتصال")
        
        # إنشاء رسائل (واحدة ناجحة واثنتان فاشلتان)
        messages = [
            Message(
                campaign_id=campaign.id,
                contact_id=contacts[0].id,
                message_text="مرحباً أحمد محمد، هذه رسالة اختبار",
                status="sent",
                sent_at=datetime.now()
            ),
            Message(
                campaign_id=campaign.id,
                contact_id=contacts[1].id,
                message_text="مرحباً فاطمة علي، هذه رسالة اختبار",
                status="failed",
                error_message="رقم الهاتف غير صحيح"
            ),
            Message(
                campaign_id=campaign.id,
                contact_id=contacts[2].id,
                message_text="مرحباً محمد سالم، هذه رسالة اختبار",
                status="failed",
                error_message="فشل في الاتصال بـ WhatsApp"
            )
        ]
        
        for message in messages:
            db.add(message)
        db.commit()
        
        print(f"✅ تم إنشاء {len(messages)} رسالة (1 ناجحة، 2 فاشلة)")
        print(f"🎯 معرف الحملة: {campaign.id}")
        
        return campaign.id
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء البيانات التجريبية: {e}")
        db.rollback()
        return None
    finally:
        db.close()

async def test_retry_functionality(campaign_id):
    """اختبار وظيفة إعادة الإرسال"""
    
    print(f"\n🔄 اختبار إعادة الإرسال للحملة {campaign_id}...")
    
    db = SessionLocal()
    try:
        # إنشاء خدمة الحملة
        campaign_service = CampaignService(db)
        
        # محاولة إعادة الإرسال
        print("📤 بدء إعادة الإرسال...")
        result = await campaign_service.retry_failed_messages(campaign_id)
        
        print("✅ نتيجة إعادة الإرسال:")
        print(f"   📊 إجمالي المحاولات: {result['total_retry_attempts']}")
        print(f"   ✅ نجح: {result['retry_sent_count']}")
        print(f"   ❌ فشل: {result['retry_failed_count']}")
        print(f"   📈 معدل النجاح: {result['success_rate']:.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار إعادة الإرسال: {e}")
        return False
    finally:
        db.close()

def test_api_endpoints(campaign_id):
    """اختبار API endpoints الجديدة"""
    
    print(f"\n🌐 اختبار API endpoints للحملة {campaign_id}...")
    
    try:
        import requests
        
        base_url = "http://localhost:8000"
        
        # اختبار جلب جهات الاتصال الفاشلة
        print("1. اختبار جلب جهات الاتصال الفاشلة...")
        response = requests.get(f"{base_url}/api/campaigns/{campaign_id}/failed-contacts")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ نجح: {data['total_failed']} جهة اتصال فاشلة")
            for contact in data['failed_contacts']:
                print(f"     - {contact['name']}: {contact['error_message']}")
        else:
            print(f"   ❌ فشل: {response.status_code}")
        
        # اختبار إعادة الإرسال (محاكاة)
        print("\n2. اختبار endpoint إعادة الإرسال...")
        print("   ⚠️ تم تخطي الاختبار الفعلي لتجنب إرسال رسائل حقيقية")
        print("   💡 يمكنك اختبار هذا من الواجهة الأمامية")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار API: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚀 اختبار ميزة إعادة الإرسال للرسائل الفاشلة")
    print("=" * 60)
    
    # إنشاء بيانات تجريبية
    campaign_id = create_test_campaign_with_failed_messages()
    
    if not campaign_id:
        print("❌ فشل في إنشاء البيانات التجريبية")
        return
    
    # اختبار API endpoints
    test_api_endpoints(campaign_id)
    
    # اختبار وظيفة إعادة الإرسال (محاكاة)
    print(f"\n🔄 محاكاة إعادة الإرسال...")
    print("   ⚠️ تم تخطي الاختبار الفعلي لتجنب إرسال رسائل حقيقية")
    print("   💡 يمكنك اختبار هذا من الواجهة الأمامية")
    
    print("\n" + "=" * 60)
    print("🎉 انتهى الاختبار!")
    print(f"🎯 معرف الحملة التجريبية: {campaign_id}")
    print("💡 اذهب إلى الواجهة الأمامية لاختبار زر 'إعادة الإرسال'")
    print("🌐 http://localhost:5173/campaigns")

if __name__ == "__main__":
    main()
