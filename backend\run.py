#!/usr/bin/env python3
"""
ملف تشغيل سريع للباك إند
"""

import uvicorn
import os
from dotenv import load_dotenv

# تحميل متغيرات البيئة
load_dotenv()

if __name__ == "__main__":
    # إعدادات الخادم
    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", 8000))
    debug = os.getenv("DEBUG", "True").lower() == "true"
    
    print("🚀 بدء تشغيل Smart WhatsApp Campaigner Backend")
    print(f"📡 الخادم: http://{host}:{port}")
    print(f"📚 التوثيق: http://{host}:{port}/docs")
    print(f"🔧 وضع التطوير: {'مفعل' if debug else 'معطل'}")
    print("-" * 50)
    
    # تشغيل الخادم
    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        reload=debug,
        log_level="info" if not debug else "debug"
    )
