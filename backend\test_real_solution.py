#!/usr/bin/env python3
"""
اختبار الحل الحقيقي للردود الذكية
"""

import asyncio
import sys
import os
from pathlib import Path
import requests
import json

# إضافة مسار المشروع
sys.path.append(str(Path(__file__).parent))

import logging

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

BASE_URL = "http://localhost:8000"

def test_api_endpoints():
    """اختبار endpoints الخاصة بالردود الذكية"""
    
    print("🔗 اختبار API endpoints")
    print("=" * 70)
    
    try:
        # 1. فحص حالة الخادم
        print("1️⃣ فحص حالة الخادم...")
        try:
            response = requests.get(f"{BASE_URL}/api/ai-responses/auto-reply/status", timeout=5)
            if response.status_code == 200:
                status = response.json()
                print(f"   ✅ الخادم يعمل")
                print(f"   📊 حالة الردود الذكية: {'تعمل' if status.get('is_running', False) else 'متوقفة'}")
            else:
                print(f"   ❌ خطأ في الخادم: {response.status_code}")
                return False
        except Exception as e:
            print(f"   ❌ لا يمكن الوصول للخادم: {e}")
            return False
        
        # 2. فحص إعدادات الردود الذكية
        print("\n2️⃣ فحص إعدادات الردود الذكية...")
        try:
            response = requests.get(f"{BASE_URL}/api/ai-responses/settings/current", timeout=5)
            if response.status_code == 200:
                settings = response.json()
                print(f"   ✅ الإعدادات متاحة")
                print(f"   🤖 OpenAI API: {'مُعد' if settings.get('openai_api_key') else 'غير مُعد'}")
                print(f"   🧠 نموذج GPT: {settings.get('gpt_model', 'غير محدد')}")
                print(f"   📝 Prompt: {'مُعد' if settings.get('gpt_prompt') else 'غير مُعد'}")
            else:
                print(f"   ❌ خطأ في الإعدادات: {response.status_code}")
        except Exception as e:
            print(f"   ❌ خطأ في جلب الإعدادات: {e}")
        
        # 3. فحص حالة WhatsApp Web
        print("\n3️⃣ فحص حالة WhatsApp Web...")
        try:
            response = requests.get(f"{BASE_URL}/api/whatsapp/status", timeout=5)
            if response.status_code == 200:
                whatsapp_status = response.json()
                print(f"   ✅ WhatsApp Web API متاح")
                print(f"   📱 الجلسة نشطة: {whatsapp_status.get('session_active', False)}")
                print(f"   🔐 مسجل دخول: {whatsapp_status.get('logged_in', False)}")
                print(f"   🌐 المتصفح: {'متاح' if whatsapp_status.get('driver_available', False) else 'غير متاح'}")
                
                if not whatsapp_status.get('session_active', False):
                    print("   💡 يرجى تسجيل الدخول في WhatsApp Web من صفحة الإعدادات")
                    return False
            else:
                print(f"   ❌ خطأ في WhatsApp Web: {response.status_code}")
                return False
        except Exception as e:
            print(f"   ❌ خطأ في فحص WhatsApp Web: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار API: {e}")
        return False

def test_auto_reply_toggle():
    """اختبار تشغيل/إيقاف الردود الذكية"""
    
    print("\n🔄 اختبار تشغيل/إيقاف الردود الذكية")
    print("=" * 70)
    
    try:
        # 1. محاولة تشغيل الردود الذكية
        print("1️⃣ محاولة تشغيل الردود الذكية...")
        
        response = requests.post(
            f"{BASE_URL}/api/ai-responses/toggle-auto-reply",
            json={"enabled": True},
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ استجابة API ناجحة")
            print(f"   📊 النتيجة: {result.get('message', 'غير محدد')}")
            print(f"   🔄 الحالة: {'تعمل' if result.get('is_running', False) else 'متوقفة'}")
            
            if result.get('is_running', False):
                print("   🎉 تم تشغيل الردود الذكية بنجاح!")
                
                # انتظار قليل ثم فحص الحالة
                print("\n2️⃣ فحص الحالة بعد التشغيل...")
                import time
                time.sleep(3)
                
                status_response = requests.get(f"{BASE_URL}/api/ai-responses/auto-reply/status", timeout=5)
                if status_response.status_code == 200:
                    status = status_response.json()
                    print(f"   📊 الحالة الحالية: {'تعمل' if status.get('is_running', False) else 'متوقفة'}")
                    
                    if status.get('is_running', False):
                        print("   ✅ الردود الذكية تعمل بنجاح!")
                        return True
                    else:
                        print("   ❌ الردود الذكية توقفت بعد التشغيل")
                        return False
                else:
                    print(f"   ❌ خطأ في فحص الحالة: {status_response.status_code}")
                    return False
            else:
                print("   ❌ فشل في تشغيل الردود الذكية")
                print(f"   💡 السبب: {result.get('message', 'غير محدد')}")
                return False
        else:
            print(f"   ❌ خطأ في API: {response.status_code}")
            try:
                error_detail = response.json()
                print(f"   📝 التفاصيل: {error_detail}")
            except:
                print(f"   📝 النص: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار التشغيل: {e}")
        return False

def test_message_detection():
    """اختبار اكتشاف الرسائل"""
    
    print("\n📨 اختبار اكتشاف الرسائل")
    print("=" * 70)
    
    try:
        # فحص إذا كانت الردود الذكية تعمل
        response = requests.get(f"{BASE_URL}/api/ai-responses/auto-reply/status", timeout=5)
        if response.status_code == 200:
            status = response.json()
            if status.get('is_running', False):
                print("   ✅ الردود الذكية تعمل")
                print("   💡 الآن يمكنك إرسال رسالة اختبار من هاتف آخر")
                print("   📱 ستتم معالجة الرسالة تلقائياً وإرسال رد ذكي")
                return True
            else:
                print("   ❌ الردود الذكية لا تعمل")
                return False
        else:
            print(f"   ❌ خطأ في فحص الحالة: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار اكتشاف الرسائل: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚀 اختبار الحل الحقيقي للردود الذكية")
    print("=" * 70)
    
    # اختبار API endpoints
    api_ok = test_api_endpoints()
    
    if not api_ok:
        print("\n❌ فشل في اختبار API - تأكد من:")
        print("   - تشغيل الخادم")
        print("   - تسجيل الدخول في WhatsApp Web")
        print("   - إعداد OpenAI API Key")
        return
    
    # اختبار تشغيل الردود الذكية
    toggle_ok = test_auto_reply_toggle()
    
    # اختبار اكتشاف الرسائل
    detection_ok = test_message_detection()
    
    print("\n" + "=" * 70)
    print("📊 نتائج الاختبار الحقيقي:")
    print(f"   🔗 API Endpoints: {'✅ نجح' if api_ok else '❌ فشل'}")
    print(f"   🔄 تشغيل الردود: {'✅ نجح' if toggle_ok else '❌ فشل'}")
    print(f"   📨 اكتشاف الرسائل: {'✅ جاهز' if detection_ok else '❌ غير جاهز'}")
    
    if api_ok and toggle_ok and detection_ok:
        print("\n🎉 الحل الجذري يعمل بنجاح!")
        print("💡 الآن يمكنك:")
        print("   1. إرسال رسالة من هاتف آخر")
        print("   2. مراقبة الردود التلقائية")
        print("   3. فحص السجلات في الخادم")
        print("\n🔧 التحسينات المطبقة:")
        print("   ✅ خدمة WhatsApp Web موحدة")
        print("   ✅ نفس منطق الحملات الإعلانية")
        print("   ✅ معالجة أخطاء محسنة")
        print("   ✅ فحص شامل للنظام")
    else:
        print("\n⚠️ ما زالت هناك مشاكل:")
        if not api_ok:
            print("   - مشكلة في API أو WhatsApp Web")
        if not toggle_ok:
            print("   - مشكلة في تشغيل الردود الذكية")
        if not detection_ok:
            print("   - مشكلة في اكتشاف الرسائل")

if __name__ == "__main__":
    main()
