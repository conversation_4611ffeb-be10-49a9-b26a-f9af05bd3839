"""
تشخيص مشكلة أعمدة ملف CSV
"""

import os
import sys
import pandas as pd

# إضافة مسار المشروع
sys.path.insert(0, os.getcwd())

from services.file_service import FileService

def debug_csv_file():
    """تشخيص ملف CSV"""
    
    csv_file = '../templates/نموذج_استيراد_العملاء.csv'
    
    print("🔍 تشخيص ملف CSV...")
    print("=" * 50)
    
    # التحقق من وجود الملف
    if not os.path.exists(csv_file):
        print("❌ ملف CSV غير موجود")
        return
    
    print(f"✅ الملف موجود: {csv_file}")
    
    try:
        # قراءة الملف مباشرة
        print("\n📄 قراءة مباشرة بـ pandas:")
        df = pd.read_csv(csv_file)
        print(f"📊 عدد الصفوف: {len(df)}")
        print(f"📋 الأعمدة: {list(df.columns)}")
        print(f"📝 أسماء الأعمدة الدقيقة:")
        for i, col in enumerate(df.columns):
            print(f"  {i+1}. '{col}' (طول: {len(col)})")
        
        print(f"\n📄 البيانات:")
        for i, row in df.iterrows():
            print(f"  الصف {i+1}:")
            for col in df.columns:
                value = row[col]
                print(f"    {col}: '{value}'")
        
        # اختبار FileService
        print(f"\n🔧 اختبار FileService:")
        file_service = FileService()
        
        # قراءة بـ FileService
        df_service = file_service.read_excel_file(csv_file)
        print(f"📊 FileService - عدد الصفوف: {len(df_service)}")
        print(f"📋 FileService - الأعمدة: {list(df_service.columns)}")
        
        # اختبار التحقق من صحة البيانات
        validation_result = file_service.validate_contacts_data(df_service)
        print(f"\n✅ نتيجة التحقق:")
        print(f"   صحيح: {validation_result['valid']}")
        if validation_result['valid']:
            print(f"   الأعمدة المطابقة: {validation_result['normalized_columns']}")
        else:
            print(f"   الأخطاء: {validation_result['errors']}")
            print(f"   التحذيرات: {validation_result.get('warnings', [])}")
        
        # اختبار البحث عن الأعمدة يدوياً
        print(f"\n🔍 اختبار البحث عن الأعمدة:")
        
        alternative_columns = {
            'name': ['الاسم', 'اسم', 'Name', 'الاسم الكامل', 'name', 'NAME'],
            'phone': ['الهاتف', 'هاتف', 'Phone', 'رقم الهاتف', 'phone_number', 'PHONE', 'رقم_الهاتف', 'phone', 'mobile', 'موبايل']
        }
        
        found_columns = {}
        
        for col in df.columns:
            col_lower = col.lower().strip()
            col_clean = col.strip()
            
            print(f"  فحص العمود: '{col}' (منظف: '{col_clean}', صغير: '{col_lower}')")
            
            # البحث عن الاسم
            for alt in alternative_columns['name']:
                if alt.lower() == col_lower or alt in col_clean:
                    found_columns['name'] = col
                    print(f"    ✅ وُجد عمود الاسم: '{alt}' يطابق '{col}'")
                    break
            
            # البحث عن الهاتف
            for alt in alternative_columns['phone']:
                if alt.lower() == col_lower or alt in col_clean:
                    found_columns['phone'] = col
                    print(f"    ✅ وُجد عمود الهاتف: '{alt}' يطابق '{col}'")
                    break
        
        print(f"\n📋 الأعمدة الموجودة يدوياً: {found_columns}")
        
        # اختبار تطابق دقيق
        print(f"\n🎯 اختبار تطابق دقيق:")
        for col in df.columns:
            if col == 'الاسم':
                print(f"  ✅ عمود 'الاسم' موجود")
            elif col == 'رقم الهاتف':
                print(f"  ✅ عمود 'رقم الهاتف' موجود")
            elif 'اسم' in col:
                print(f"  🔍 عمود يحتوي على 'اسم': '{col}'")
            elif 'هاتف' in col:
                print(f"  🔍 عمود يحتوي على 'هاتف': '{col}'")
        
    except Exception as e:
        print(f"❌ خطأ في تشخيص الملف: {e}")
        import traceback
        traceback.print_exc()

def create_test_csv():
    """إنشاء ملف CSV تجريبي"""
    
    print("\n🔧 إنشاء ملف CSV تجريبي...")
    
    # إنشاء مجلد templates إذا لم يكن موجوداً
    templates_dir = '../templates'
    if not os.path.exists(templates_dir):
        os.makedirs(templates_dir)
    
    # إنشاء ملف CSV جديد
    csv_content = """الاسم,رقم الهاتف,البريد الإلكتروني,الشركة,المدينة,ملاحظات
أحمد محمد,+966501234567,<EMAIL>,شركة التقنية,الرياض,عميل VIP
فاطمة علي,0502345678,<EMAIL>,مؤسسة الإبداع,جدة,عميلة جديدة
محمد سالم,966503456789,<EMAIL>,شركة النجاح,الدمام,عميل حالي"""
    
    csv_file = '../templates/نموذج_استيراد_العملاء_جديد.csv'
    
    with open(csv_file, 'w', encoding='utf-8') as f:
        f.write(csv_content)
    
    print(f"✅ تم إنشاء ملف جديد: {csv_file}")
    
    # اختبار الملف الجديد
    print(f"\n🧪 اختبار الملف الجديد:")
    
    try:
        df = pd.read_csv(csv_file)
        print(f"📊 عدد الصفوف: {len(df)}")
        print(f"📋 الأعمدة: {list(df.columns)}")
        
        file_service = FileService()
        validation_result = file_service.validate_contacts_data(df)
        
        print(f"✅ نتيجة التحقق:")
        print(f"   صحيح: {validation_result['valid']}")
        if validation_result['valid']:
            print(f"   الأعمدة المطابقة: {validation_result['normalized_columns']}")
        else:
            print(f"   الأخطاء: {validation_result['errors']}")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الملف الجديد: {e}")

if __name__ == "__main__":
    print("🔍 تشخيص مشكلة أعمدة ملف CSV")
    print("=" * 60)
    
    # تشخيص الملف الحالي
    debug_csv_file()
    
    # إنشاء ملف تجريبي
    create_test_csv()
    
    print("\n💡 الحلول المقترحة:")
    print("1. تأكد من أن أسماء الأعمدة صحيحة")
    print("2. استخدم 'الاسم' و 'رقم الهاتف' كأسماء أعمدة")
    print("3. تأكد من ترميز UTF-8 للملف")
    print("4. تجنب المسافات الإضافية في أسماء الأعمدة")
    
    print("=" * 60)
    input("اضغط Enter للخروج...")
