#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الإصلاحات الجديدة للنظام
"""

import asyncio
import sys
import os
import logging
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.auto_reply_service import AutoReplyService
from services.whatsapp_web_service import WhatsAppWebService
from database import SessionLocal
from models import Settings

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_fixes.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class SystemTester:
    def __init__(self):
        self.auto_reply_service = None
        self.whatsapp_service = None
        
    async def setup(self):
        """إعداد الخدمات للاختبار"""
        try:
            print("🔧 [الإعداد] بدء إعداد الخدمات...")
            
            # إنشاء خدمة WhatsApp Web
            self.whatsapp_service = WhatsAppWebService()
            
            # إنشاء خدمة الرد التلقائي
            self.auto_reply_service = AutoReplyService()
            
            print("✅ [الإعداد] تم إعداد الخدمات بنجاح")
            return True
            
        except Exception as e:
            print(f"❌ [خطأ الإعداد] {e}")
            logger.error(f"خطأ في إعداد الخدمات: {e}")
            return False

    def test_text_cleaning(self):
        """اختبار تنظيف النص من الأحرف غير المدعومة"""
        print("\n🧪 [اختبار 1] اختبار تنظيف النص...")
        
        try:
            # نصوص اختبار تحتوي على أحرف مشكلة
            test_texts = [
                "مرحبا 😊 كيف حالك؟ 🌟",
                "أهلاً وسهلاً 🙌\nأول شي، بحب أعرّفك على نفسي…",
                "نص عادي بدون إيموجي",
                "🚀🔸⭐ رموز متنوعة",
                "نص مختلط عربي وإنجليزي مع إيموجي 😊"
            ]
            
            for i, text in enumerate(test_texts):
                print(f"    📝 [نص {i+1}] الأصلي: {text[:50]}...")
                
                if self.auto_reply_service:
                    cleaned = self.auto_reply_service._clean_text_for_chrome(text)
                    print(f"    🧹 [نظيف {i+1}] المنظف: {cleaned[:50]}...")
                    
                    # فحص إذا كانت جميع الأحرف في نطاق BMP
                    all_safe = all(ord(char) <= 0xFFFF for char in cleaned)
                    print(f"    {'✅' if all_safe else '❌'} [آمان {i+1}] جميع الأحرف آمنة: {all_safe}")
                else:
                    print("    ⚠️ [تحذير] خدمة الرد التلقائي غير متاحة")
            
            print("✅ [اختبار 1] اكتمل اختبار تنظيف النص")
            return True
            
        except Exception as e:
            print(f"❌ [اختبار 1] خطأ في اختبار تنظيف النص: {e}")
            return False

    def test_message_type_detection(self):
        """اختبار فحص نوع الرسالة (صادرة/واردة)"""
        print("\n🧪 [اختبار 2] اختبار فحص نوع الرسالة...")
        
        try:
            # هذا الاختبار يحتاج إلى عناصر DOM حقيقية
            # لذا سنختبر فقط أن الدوال لا تحتاج await
            
            if self.auto_reply_service:
                # فحص أن الدوال لا تحتاج await
                print("    🔍 [فحص] التأكد من أن الدوال لا تحتاج await...")
                
                # هذه الدوال يجب أن تكون sync الآن
                method_names = ['_is_outgoing_message', '_is_incoming_message', '_extract_message_text_simple']
                
                for method_name in method_names:
                    if hasattr(self.auto_reply_service, method_name):
                        method = getattr(self.auto_reply_service, method_name)
                        is_async = asyncio.iscoroutinefunction(method)
                        print(f"    {'❌' if is_async else '✅'} [{method_name}] async: {is_async}")
                    else:
                        print(f"    ⚠️ [{method_name}] الدالة غير موجودة")
                
                print("✅ [اختبار 2] اكتمل اختبار فحص نوع الرسالة")
                return True
            else:
                print("    ⚠️ [تحذير] خدمة الرد التلقائي غير متاحة")
                return False
                
        except Exception as e:
            print(f"❌ [اختبار 2] خطأ في اختبار فحص نوع الرسالة: {e}")
            return False

    def test_context_cleaning(self):
        """اختبار تنظيف السياق"""
        print("\n🧪 [اختبار 3] اختبار تنظيف السياق...")
        
        try:
            # نصوص سياق اختبار
            test_contexts = [
                "العميل: مرحبا 10:43 م",
                "ai bot: أهلاً وسهلاً، كيف يمكنني مساعدتك؟ 10:44 م",
                "العميل: أريد معلومات عن المنتج 2024/08/06",
                "نص عادي بدون وقت أو تاريخ"
            ]
            
            for i, context in enumerate(test_contexts):
                print(f"    📝 [سياق {i+1}] الأصلي: {context}")
                
                if self.auto_reply_service:
                    cleaned = self.auto_reply_service._clean_context_message(context)
                    print(f"    🧹 [نظيف {i+1}] المنظف: {cleaned}")
                else:
                    print("    ⚠️ [تحذير] خدمة الرد التلقائي غير متاحة")
            
            print("✅ [اختبار 3] اكتمل اختبار تنظيف السياق")
            return True
            
        except Exception as e:
            print(f"❌ [اختبار 3] خطأ في اختبار تنظيف السياق: {e}")
            return False

    async def test_whatsapp_connection(self):
        """اختبار الاتصال بـ WhatsApp Web"""
        print("\n🧪 [اختبار 4] اختبار الاتصال بـ WhatsApp Web...")
        
        try:
            if self.whatsapp_service:
                # فحص حالة الخدمة
                status = self.whatsapp_service.get_status()
                print(f"    📊 [الحالة] {status}")
                
                # فحص إذا كان المتصفح يعمل
                if status.get('driver_active'):
                    print("    ✅ [المتصفح] المتصفح يعمل")
                else:
                    print("    ❌ [المتصفح] المتصفح لا يعمل")
                
                # فحص تسجيل الدخول
                if status.get('logged_in'):
                    print("    ✅ [تسجيل الدخول] مُسجل دخول")
                else:
                    print("    ⚠️ [تسجيل الدخول] غير مُسجل دخول")
                
                print("✅ [اختبار 4] اكتمل اختبار الاتصال")
                return True
            else:
                print("    ⚠️ [تحذير] خدمة WhatsApp غير متاحة")
                return False
                
        except Exception as e:
            print(f"❌ [اختبار 4] خطأ في اختبار الاتصال: {e}")
            return False

    async def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        print("🚀 [بدء الاختبارات] بدء اختبار جميع الإصلاحات...")
        print("=" * 60)
        
        # إعداد الخدمات
        setup_success = await self.setup()
        if not setup_success:
            print("❌ [فشل] فشل في إعداد الخدمات")
            return False
        
        # تشغيل الاختبارات
        tests = [
            ("تنظيف النص", self.test_text_cleaning),
            ("فحص نوع الرسالة", self.test_message_type_detection),
            ("تنظيف السياق", self.test_context_cleaning),
            ("الاتصال بـ WhatsApp", self.test_whatsapp_connection)
        ]
        
        results = []
        for test_name, test_func in tests:
            try:
                if asyncio.iscoroutinefunction(test_func):
                    result = await test_func()
                else:
                    result = test_func()
                results.append((test_name, result))
            except Exception as e:
                print(f"❌ [خطأ] خطأ في اختبار {test_name}: {e}")
                results.append((test_name, False))
        
        # عرض النتائج
        print("\n" + "=" * 60)
        print("📊 [النتائج] ملخص نتائج الاختبارات:")
        
        passed = 0
        total = len(results)
        
        for test_name, result in results:
            status = "✅ نجح" if result else "❌ فشل"
            print(f"    {status} {test_name}")
            if result:
                passed += 1
        
        print(f"\n🎯 [الإجمالي] {passed}/{total} اختبارات نجحت")
        
        if passed == total:
            print("🎉 [نجح] جميع الاختبارات نجحت!")
            return True
        else:
            print("⚠️ [تحذير] بعض الاختبارات فشلت")
            return False

async def main():
    """الدالة الرئيسية"""
    tester = SystemTester()
    success = await tester.run_all_tests()
    
    if success:
        print("\n✅ [اكتمل] تم اختبار جميع الإصلاحات بنجاح")
        return 0
    else:
        print("\n❌ [فشل] فشل في بعض الاختبارات")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⚠️ [إيقاف] تم إيقاف الاختبار بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ [خطأ عام] {e}")
        logger.error(f"خطأ عام في الاختبار: {e}")
        sys.exit(1)
