import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import {
  Settings as SettingsIcon,
  Key,
  Globe,
  Moon,
  Sun,
  Bell,
  Shield,
  Save,
  RefreshCw,
  Eye,
  EyeOff,
  CheckCircle,
  AlertCircle,
  Bot,
  MessageSquare
} from "lucide-react";
import { useApi, useApiMutation } from "@/hooks/useApi";
import {
  getAISettings,
  updateAISettings,
  testAIResponse,
  loginWhatsApp,
  getWhatsAppStatus,
  logoutWhatsApp,
  toggleWhatsAppHeadless,
  waitForWhatsAppLogin,
  clearWhatsAppSession,
  WhatsAppStatus
} from "@/lib/api";

const Settings = () => {
  // إعدادات التطبيق
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [language, setLanguage] = useState("ar");
  const [notifications, setNotifications] = useState(true);
  const [autoSave, setAutoSave] = useState(true);
  const [showApiKey, setShowApiKey] = useState(false);

  // إعدادات OpenAI
  const [openaiApiKey, setOpenaiApiKey] = useState("");
  const [gptModel, setGptModel] = useState("gpt-3.5-turbo");
  const [gptPrompt, setGptPrompt] = useState("");
  const [responseDelay, setResponseDelay] = useState(2);
  const [maxDailyResponses, setMaxDailyResponses] = useState(1000);
  const [autoReplyEnabled, setAutoReplyEnabled] = useState(false);



  // إعدادات الشركة
  const [companyName, setCompanyName] = useState("");
  const [supportPhone, setSupportPhone] = useState("");
  const [supportEmail, setSupportEmail] = useState("");

  // حالات UI
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [testResult, setTestResult] = useState<string | null>(null);

  // حالات WhatsApp Web
  const [whatsappHeadless, setWhatsappHeadless] = useState(false);
  const [whatsappLoginInProgress, setWhatsappLoginInProgress] = useState(false);
  const [whatsappWaitingForQR, setWhatsappWaitingForQR] = useState(false);

  // جلب الإعدادات الحالية
  const { data: settings, loading: settingsLoading, error: settingsError, refetch: refetchSettings } = useApi(getAISettings);

  // جلب حالة WhatsApp Web
  const { data: whatsappStatus, loading: whatsappStatusLoading, refetch: refetchWhatsAppStatus } = useApi<WhatsAppStatus>(getWhatsAppStatus);

  // العمليات
  const { mutate: updateSettings, loading: updating } = useApiMutation(updateAISettings);
  const { mutate: testConnection, loading: testing } = useApiMutation(testAIResponse);
  const { mutate: loginWhatsAppMutation, loading: loggingIn } = useApiMutation(loginWhatsApp);
  const { mutate: logoutWhatsAppMutation, loading: loggingOut } = useApiMutation(logoutWhatsApp);
  const { mutate: toggleHeadlessMutation, loading: toggling } = useApiMutation(toggleWhatsAppHeadless);
  const { mutate: clearSessionMutation, loading: clearing } = useApiMutation(clearWhatsAppSession);



  // تحديث الحقول عند تحميل الإعدادات
  useEffect(() => {
    if (settings) {
      setOpenaiApiKey(settings.openai_api_key || "");
      setGptModel(settings.gpt_model);
      setGptPrompt(settings.gpt_prompt || "");
      setResponseDelay(settings.response_delay);
      setMaxDailyResponses(settings.max_daily_responses);
      setAutoReplyEnabled(settings.auto_reply_enabled);
      setCompanyName(settings.company_name);
      setSupportPhone(settings.support_phone || "");
      setSupportEmail(settings.support_email || "");
    }
  }, [settings]);

  const handleSaveSettings = async () => {
    try {
      const settingsData = {
        openai_api_key: openaiApiKey,
        gpt_model: gptModel,
        gpt_prompt: gptPrompt,
        response_delay: responseDelay,
        max_daily_responses: maxDailyResponses,
        auto_reply_enabled: autoReplyEnabled,
        company_name: companyName,
        support_phone: supportPhone,
        support_email: supportEmail
      };

      console.log('حفظ الإعدادات:', settingsData);

      const result = await updateSettings(settingsData);
      if (result) {
        setSaveSuccess(true);
        setTimeout(() => setSaveSuccess(false), 3000);
        refetchSettings();
        alert("✅ تم حفظ الإعدادات بنجاح!");
      } else {
        alert("❌ فشل في حفظ الإعدادات. يرجى المحاولة مرة أخرى.");
      }
    } catch (error) {
      console.error('خطأ في حفظ الإعدادات:', error);
      alert("❌ خطأ في حفظ الإعدادات: " + error);
    }
  };

  const handleTestConnection = async () => {
    if (!openaiApiKey.trim()) {
      setTestResult("يرجى إدخال مفتاح OpenAI API أولاً");
      return;
    }

    const result = await testConnection("مرحباً، هذا اختبار للاتصال");
    if (result) {
      setTestResult("✅ تم الاتصال بنجاح! الرد: " + result.ai_response);
    } else {
      setTestResult("❌ فشل في الاتصال. تحقق من صحة المفتاح");
    }

    setTimeout(() => setTestResult(null), 5000);
  };

  // دوال WhatsApp Web
  const handleWhatsAppLogin = async () => {
    setWhatsappLoginInProgress(true);
    setWhatsappWaitingForQR(false);

    try {
      const result = await loginWhatsAppMutation({
        headless: whatsappHeadless,
        timeout: 120 // زيادة المهلة إلى دقيقتين
      });

      if (result) {
        if (result.requires_qr) {
          setWhatsappWaitingForQR(true);
          alert("تم فتح المتصفح. يرجى مسح QR Code من هاتفك خلال دقيقتين.");

          // انتظار تسجيل الدخول مع مهلة أطول
          const waitResult = await waitForWhatsAppLogin(120);
          if (waitResult?.success) {
            refetchWhatsAppStatus();
            alert("🎉 تم تسجيل الدخول بنجاح! يمكنك الآن إرسال الرسائل.");
          } else {
            alert("⏰ انتهت مهلة انتظار تسجيل الدخول. يرجى المحاولة مرة أخرى والتأكد من مسح QR Code بسرعة.");
          }
        } else {
          refetchWhatsAppStatus();
          alert("✅ تم تسجيل الدخول تلقائياً باستخدام الجلسة المحفوظة!");
        }
      }
    } catch (error) {
      alert("❌ خطأ في تسجيل الدخول: " + error);
    } finally {
      setWhatsappLoginInProgress(false);
      setWhatsappWaitingForQR(false);
    }
  };

  const handleWhatsAppLogout = async () => {
    if (confirm("هل أنت متأكد من تسجيل الخروج؟ ستحتاج لمسح QR Code مرة أخرى.")) {
      const result = await logoutWhatsAppMutation({});
      if (result?.success) {
        refetchWhatsAppStatus();
        alert("تم تسجيل الخروج بنجاح");
      }
    }
  };

  const handleToggleHeadless = async () => {
    const newHeadless = !whatsappHeadless;
    setWhatsappHeadless(newHeadless);

    const result = await toggleHeadlessMutation(newHeadless);
    if (result?.success) {
      refetchWhatsAppStatus();
      alert(`تم تغيير الوضع إلى ${newHeadless ? 'الخلفية' : 'ظاهر'}`);
    }
  };

  const handleClearSession = async () => {
    if (confirm("هل أنت متأكد من حذف جميع بيانات الجلسة؟ ستحتاج لتسجيل الدخول مرة أخرى.")) {
      const result = await clearSessionMutation({});
      if (result?.success) {
        refetchWhatsAppStatus();
        alert("تم حذف بيانات الجلسة بنجاح");
      }
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="max-w-4xl mx-auto p-6">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-foreground mb-2">
            الإعدادات
          </h1>
          <p className="text-muted-foreground text-lg">
            خصص إعدادات التطبيق والـ API حسب احتياجاتك
          </p>
        </div>

        <div className="space-y-6">
          {/* Loading State */}
          {settingsLoading && (
            <Card className="p-6">
              <div className="animate-pulse space-y-4">
                <div className="h-6 bg-muted rounded w-1/3"></div>
                <div className="space-y-3">
                  <div className="h-4 bg-muted rounded"></div>
                  <div className="h-10 bg-muted rounded"></div>
                </div>
              </div>
            </Card>
          )}

          {/* Error State */}
          {settingsError && (
            <Card className="p-6 border-destructive/50 bg-destructive/5">
              <div className="flex items-center gap-3">
                <AlertCircle className="h-5 w-5 text-destructive" />
                <div>
                  <h3 className="font-semibold text-destructive">خطأ في تحميل الإعدادات</h3>
                  <p className="text-sm text-muted-foreground mt-1">{settingsError}</p>
                </div>
              </div>
            </Card>
          )}

          {/* OpenAI Configuration */}
          <Card className="p-6">
            <div className="flex items-center gap-2 mb-6">
              <Bot className="h-5 w-5 text-primary" />
              <h2 className="text-xl font-semibold text-foreground">
                إعدادات OpenAI
              </h2>
            </div>

            <div className="space-y-6">
              {/* API Key */}
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  مفتاح OpenAI API
                </label>
                <div className="relative">
                  <Input
                    type={showApiKey ? "text" : "password"}
                    value={openaiApiKey}
                    onChange={(e) => setOpenaiApiKey(e.target.value)}
                    placeholder="sk-..."
                    className="pr-10"
                  />
                  <button
                    type="button"
                    onClick={() => setShowApiKey(!showApiKey)}
                    className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
                  >
                    {showApiKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </button>
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  احصل على مفتاح API من منصة OpenAI
                </p>
              </div>

              {/* Model and Settings */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    نموذج GPT
                  </label>
                  <select
                    value={gptModel}
                    onChange={(e) => setGptModel(e.target.value)}
                    className="w-full p-2 border rounded-md"
                  >
                    <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                    <option value="gpt-4">GPT-4</option>
                    <option value="gpt-4-turbo">GPT-4 Turbo</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    تأخير الرد (ثانية)
                  </label>
                  <Input
                    type="number"
                    min="1"
                    max="60"
                    value={responseDelay}
                    onChange={(e) => setResponseDelay(Number(e.target.value))}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    الحد الأقصى للردود اليومية
                  </label>
                  <Input
                    type="number"
                    min="1"
                    value={maxDailyResponses}
                    onChange={(e) => setMaxDailyResponses(Number(e.target.value))}
                  />
                </div>
              </div>

              {/* GPT Prompt */}
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  برومبت الذكاء الاصطناعي
                </label>
                <Textarea
                  value={gptPrompt}
                  onChange={(e) => setGptPrompt(e.target.value)}
                  rows={6}
                  className="resize-none"
                  placeholder="أنت مساعد خدمة عملاء ودود ومهني..."
                />
                <p className="text-xs text-muted-foreground mt-1">
                  التعليمات التي سيتبعها الذكاء الاصطناعي في الردود
                </p>
              </div>

              {/* معلومات الردود التلقائية */}
              <div className="p-4 bg-blue-50 dark:bg-blue-950/30 rounded-lg border border-blue-200 dark:border-blue-800">
                <div className="flex items-center gap-2 mb-2">
                  <Bot className="h-5 w-5 text-blue-600" />
                  <label className="text-sm font-medium text-foreground">
                    الردود التلقائية الذكية
                  </label>
                </div>
                <p className="text-xs text-muted-foreground">
                  يمكنك التحكم في الردود التلقائية من صفحة "الردود الذكية"
                </p>
                <div className="mt-3">
                  <Button
                    onClick={() => window.location.href = '/ai-responses'}
                    variant="outline"
                    size="sm"
                    className="text-blue-600 border-blue-200 hover:bg-blue-50"
                  >
                    <MessageSquare className="h-4 w-4 mr-2" />
                    إدارة الردود الذكية
                  </Button>
                </div>
              </div>

              {/* Test Connection */}
              <div className="space-y-3">
                <div className="flex gap-3">
                  <Button
                    onClick={handleTestConnection}
                    variant="outline"
                    size="sm"
                    className="gap-2"
                    disabled={testing || !openaiApiKey.trim()}
                  >
                    <RefreshCw className={`h-4 w-4 ${testing ? 'animate-spin' : ''}`} />
                    {testing ? "جاري الاختبار..." : "اختبار الاتصال"}
                  </Button>
                </div>

                {testResult && (
                  <div className={`p-3 rounded-lg text-sm ${
                    testResult.includes('✅')
                      ? 'bg-success/10 text-success border border-success/20'
                      : 'bg-destructive/10 text-destructive border border-destructive/20'
                  }`}>
                    {testResult}
                  </div>
                )}
              </div>
            </div>
          </Card>

          {/* WhatsApp Web Integration */}
          <Card className="p-6">
            <div className="flex items-center gap-2 mb-6">
              <MessageSquare className="h-5 w-5 text-primary" />
              <h2 className="text-xl font-semibold text-foreground">
                WhatsApp Web
              </h2>
            </div>

            <div className="space-y-6">
              {/* Status */}
              <div className="p-4 bg-accent/30 rounded-lg">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="font-medium text-foreground">حالة الاتصال</h3>
                  <Button
                    onClick={refetchWhatsAppStatus}
                    variant="outline"
                    size="sm"
                    disabled={whatsappStatusLoading}
                  >
                    <RefreshCw className={`h-4 w-4 ${whatsappStatusLoading ? 'animate-spin' : ''}`} />
                  </Button>
                </div>

                {whatsappStatusLoading ? (
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-muted rounded-full animate-pulse"></div>
                    <span className="text-sm text-muted-foreground">جاري التحقق...</span>
                  </div>
                ) : whatsappStatus ? (
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <div className={`w-3 h-3 rounded-full ${
                        whatsappStatus.logged_in ? 'bg-success' : 'bg-destructive'
                      }`}></div>
                      <span className="text-sm font-medium">
                        {whatsappStatus.logged_in ? 'متصل' : 'غير متصل'}
                      </span>
                    </div>
                    <p className="text-xs text-muted-foreground">{whatsappStatus.message}</p>
                    {whatsappStatus.headless !== undefined && (
                      <p className="text-xs text-muted-foreground">
                        الوضع: {whatsappStatus.headless ? 'الخلفية' : 'ظاهر'}
                      </p>
                    )}
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-muted rounded-full"></div>
                    <span className="text-sm text-muted-foreground">غير معروف</span>
                  </div>
                )}
              </div>

              {/* Controls */}
              <div className="space-y-4">
                {/* Browser Mode Toggle */}
                <div className="flex items-center justify-between p-4 bg-accent/30 rounded-lg">
                  <div>
                    <label className="text-sm font-medium text-foreground">
                      تشغيل في الخلفية
                    </label>
                    <p className="text-xs text-muted-foreground">
                      تشغيل المتصفح بدون واجهة مرئية
                    </p>
                  </div>
                  <Switch
                    checked={whatsappHeadless}
                    onCheckedChange={handleToggleHeadless}
                    disabled={toggling}
                  />
                </div>

                {/* Login/Logout Buttons */}
                <div className="flex gap-3">
                  {whatsappStatus?.logged_in ? (
                    <Button
                      onClick={handleWhatsAppLogout}
                      variant="destructive"
                      className="gap-2"
                      disabled={loggingOut}
                    >
                      <Shield className="h-4 w-4" />
                      {loggingOut ? "جاري تسجيل الخروج..." : "تسجيل خروج"}
                    </Button>
                  ) : (
                    <Button
                      onClick={handleWhatsAppLogin}
                      variant="whatsapp"
                      className="gap-2"
                      disabled={whatsappLoginInProgress}
                    >
                      <MessageSquare className="h-4 w-4" />
                      {whatsappLoginInProgress ? "جاري الاتصال..." : "تسجيل دخول"}
                    </Button>
                  )}

                  <Button
                    onClick={handleClearSession}
                    variant="outline"
                    className="gap-2"
                    disabled={clearing}
                  >
                    <RefreshCw className="h-4 w-4" />
                    {clearing ? "جاري الحذف..." : "حذف الجلسة"}
                  </Button>
                </div>

                {/* QR Code Waiting */}
                {whatsappWaitingForQR && (
                  <div className="p-4 bg-blue/10 border border-blue/20 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="w-3 h-3 bg-blue rounded-full animate-pulse"></div>
                      <div className="flex-1">
                        <h4 className="font-medium text-blue mb-2">🔍 في انتظار مسح QR Code</h4>
                        <div className="text-sm text-muted-foreground space-y-1">
                          <p>📱 <strong>الخطوة 1:</strong> افتح WhatsApp على هاتفك</p>
                          <p>⚙️ <strong>الخطوة 2:</strong> اذهب إلى الإعدادات → الأجهزة المرتبطة</p>
                          <p>📷 <strong>الخطوة 3:</strong> اضغط "ربط جهاز" وامسح الرمز من المتصفح</p>
                          <p className="text-warning font-medium mt-2">⏰ المهلة: دقيقتان</p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Troubleshooting Help */}
                {!whatsappWaitingForQR && (
                  <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div className="flex items-start gap-3">
                      <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5" />
                      <div className="flex-1">
                        <h4 className="font-medium text-yellow-800 mb-2">💡 مشكلة في تسجيل الدخول؟</h4>
                        <div className="text-sm text-yellow-700 space-y-1">
                          <p><strong>إذا قال "تم تسجيل الدخول" قبل مسح QR Code:</strong></p>
                          <p>1. اضغط <strong>"حذف الجلسة"</strong> أولاً</p>
                          <p>2. ثم اضغط <strong>"تسجيل دخول"</strong> مرة أخرى</p>
                          <p>3. امسح QR Code من هاتفك خلال دقيقتين</p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </Card>

          {/* Company Information */}
          <Card className="p-6">
            <div className="flex items-center gap-2 mb-6">
              <SettingsIcon className="h-5 w-5 text-primary" />
              <h2 className="text-xl font-semibold text-foreground">
                معلومات الشركة
              </h2>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  اسم الشركة
                </label>
                <Input
                  value={companyName}
                  onChange={(e) => setCompanyName(e.target.value)}
                  placeholder="اسم شركتك"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  هاتف الدعم
                </label>
                <Input
                  value={supportPhone}
                  onChange={(e) => setSupportPhone(e.target.value)}
                  placeholder="+966xxxxxxxxx"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  بريد الدعم
                </label>
                <Input
                  type="email"
                  value={supportEmail}
                  onChange={(e) => setSupportEmail(e.target.value)}
                  placeholder="<EMAIL>"
                />
              </div>
            </div>
          </Card>

          {/* App Preferences */}
          <Card className="p-6">
            <div className="flex items-center gap-2 mb-6">
              <SettingsIcon className="h-5 w-5 text-primary" />
              <h2 className="text-xl font-semibold text-foreground">
                تفضيلات التطبيق
              </h2>
            </div>
            
            <div className="space-y-6">
              {/* Theme */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  {isDarkMode ? (
                    <Moon className="h-5 w-5 text-primary" />
                  ) : (
                    <Sun className="h-5 w-5 text-primary" />
                  )}
                  <div>
                    <label className="text-sm font-medium text-foreground">
                      الوضع الليلي
                    </label>
                    <p className="text-xs text-muted-foreground">
                      تفعيل الثيم الداكن للتطبيق
                    </p>
                  </div>
                </div>
                <Switch
                  checked={isDarkMode}
                  onCheckedChange={setIsDarkMode}
                />
              </div>

              {/* Language */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Globe className="h-5 w-5 text-primary" />
                  <div>
                    <label className="text-sm font-medium text-foreground">
                      اللغة
                    </label>
                    <p className="text-xs text-muted-foreground">
                      لغة واجهة التطبيق
                    </p>
                  </div>
                </div>
                <select 
                  value={language}
                  onChange={(e) => setLanguage(e.target.value)}
                  className="bg-background border border-border rounded-lg px-3 py-2 text-sm"
                >
                  <option value="ar">العربية</option>
                  <option value="en">English</option>
                </select>
              </div>

              {/* Notifications */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Bell className="h-5 w-5 text-primary" />
                  <div>
                    <label className="text-sm font-medium text-foreground">
                      الإشعارات
                    </label>
                    <p className="text-xs text-muted-foreground">
                      تلقي إشعارات عن الرسائل والحملات
                    </p>
                  </div>
                </div>
                <Switch
                  checked={notifications}
                  onCheckedChange={setNotifications}
                />
              </div>

              {/* Auto Save */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Save className="h-5 w-5 text-primary" />
                  <div>
                    <label className="text-sm font-medium text-foreground">
                      الحفظ التلقائي
                    </label>
                    <p className="text-xs text-muted-foreground">
                      حفظ التغييرات تلقائياً
                    </p>
                  </div>
                </div>
                <Switch
                  checked={autoSave}
                  onCheckedChange={setAutoSave}
                />
              </div>
            </div>
          </Card>



          {/* Save Button */}
          <div className="flex justify-between items-center">
            <Button
              onClick={refetchSettings}
              variant="outline"
              size="lg"
              className="gap-2"
              disabled={settingsLoading}
            >
              <RefreshCw className={`h-5 w-5 ${settingsLoading ? 'animate-spin' : ''}`} />
              تحديث الإعدادات
            </Button>

            <div className="flex items-center gap-3">
              {saveSuccess && (
                <div className="flex items-center gap-2 text-success">
                  <CheckCircle className="h-5 w-5" />
                  تم الحفظ بنجاح
                </div>
              )}
              <Button
                onClick={handleSaveSettings}
                size="lg"
                className="gap-2"
                disabled={updating}
              >
                <Save className="h-5 w-5" />
                {updating ? "جاري الحفظ..." : "حفظ جميع الإعدادات"}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Settings;