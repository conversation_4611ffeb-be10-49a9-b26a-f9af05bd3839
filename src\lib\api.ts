// خدمة API للاتصال بالباك إند
const API_BASE_URL = 'http://localhost:8000';

// دالة مساعدة لإرسال الطلبات
async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`;
  
  const defaultOptions: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
    },
  };

  const response = await fetch(url, {
    ...defaultOptions,
    ...options,
    headers: {
      ...defaultOptions.headers,
      ...options.headers,
    },
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return response.json();
}

// أنواع البيانات
export interface DashboardStats {
  total_messages_sent: number;
  auto_replies_count: number;
  active_customers: number;
  success_rate: number;
  recent_activity: Array<{
    action: string;
    target: string;
    time: string;
    status: string;
  }>;
}

export interface Campaign {
  id: number;
  name: string;
  message_text: string;
  image_path?: string;
  status: string;
  total_contacts: number;
  sent_count: number;
  delivered_count: number;
  failed_count: number;
  created_at: string;
  updated_at: string;
}

export interface Contact {
  id: number;
  name: string;
  phone_number: string;
  email?: string;
  campaign_id?: number;
  custom_fields?: Record<string, any>;
  created_at: string;
}

export interface AIResponse {
  id: number;
  contact_phone: string;
  incoming_message: string;
  ai_response: string;
  gpt_model_used: string;
  response_time_ms?: number;
  confidence_score?: number;
  created_at: string;
}

export interface Settings {
  id: number;
  whatsapp_api_url?: string;
  whatsapp_token?: string;
  whatsapp_phone_number_id?: string;
  openai_api_key?: string;
  gpt_model: string;
  gpt_prompt?: string;
  auto_reply_enabled: boolean;
  response_delay: number;
  max_daily_responses: number;
  company_name: string;
  support_phone?: string;
  support_email?: string;
  created_at: string;
  updated_at: string;
}

// API functions

// Dashboard
export const getDashboardStats = (): Promise<DashboardStats> =>
  apiRequest<DashboardStats>('/api/analytics/dashboard');

// Campaigns
export const getCampaigns = (): Promise<Campaign[]> =>
  apiRequest<Campaign[]>('/api/campaigns/');

export const getCampaign = (id: number): Promise<Campaign> =>
  apiRequest<Campaign>(`/api/campaigns/${id}`);

export const createCampaign = (campaign: Omit<Campaign, 'id' | 'created_at' | 'updated_at' | 'total_contacts' | 'sent_count' | 'delivered_count' | 'failed_count'>): Promise<Campaign> =>
  apiRequest<Campaign>('/api/campaigns/', {
    method: 'POST',
    body: JSON.stringify(campaign),
  });

export const updateCampaign = (id: number, campaign: Partial<Campaign>): Promise<Campaign> =>
  apiRequest<Campaign>(`/api/campaigns/${id}`, {
    method: 'PUT',
    body: JSON.stringify(campaign),
  });

export const deleteCampaign = (id: number): Promise<void> =>
  apiRequest<void>(`/api/campaigns/${id}`, {
    method: 'DELETE',
  });

export const sendCampaign = (id: number): Promise<any> =>
  apiRequest<any>(`/api/campaigns/${id}/send`, {
    method: 'POST',
  });

export const retryFailedMessages = (id: number): Promise<any> =>
  apiRequest<any>(`/api/campaigns/${id}/retry-failed`, {
    method: 'POST',
  });

export const getFailedContacts = (id: number): Promise<any> =>
  apiRequest<any>(`/api/campaigns/${id}/failed-contacts`);

export const uploadContactsFile = (campaignId: number, file: File): Promise<any> => {
  const formData = new FormData();
  formData.append('file', file);

  return fetch(`${API_BASE_URL}/api/campaigns/${campaignId}/upload-contacts`, {
    method: 'POST',
    body: formData,
    credentials: 'include',
    mode: 'cors',
  }).then(async response => {
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }
    return response.json();
  });
};

// Contacts
export const getContacts = (): Promise<Contact[]> =>
  apiRequest<Contact[]>('/api/campaigns/contacts');

export const getCampaignContacts = (campaignId: number): Promise<Contact[]> =>
  apiRequest<Contact[]>(`/api/campaigns/${campaignId}/contacts`);

export const getCampaignMessages = (campaignId: number): Promise<Message[]> =>
  apiRequest<Message[]>(`/api/campaigns/${campaignId}/messages`);

// AI Responses
export const getAIResponses = (): Promise<AIResponse[]> =>
  apiRequest<AIResponse[]>('/api/ai-responses/');

export const testAIResponse = (message: string): Promise<any> =>
  apiRequest<any>('/api/ai-responses/test', {
    method: 'POST',
    body: JSON.stringify({ test_message: message }),
  });

export const getAISettings = (): Promise<Settings> =>
  apiRequest<Settings>('/api/ai-responses/settings/current');

export const updateAISettings = (settings: Partial<Settings>): Promise<Settings> =>
  apiRequest<Settings>('/api/ai-responses/settings', {
    method: 'PUT',
    body: JSON.stringify(settings),
  });

export const toggleAutoReply = (): Promise<any> =>
  apiRequest<any>('/api/ai-responses/toggle-auto-reply', {
    method: 'POST',
  });

// Analytics
export const getCampaignsPerformance = (): Promise<any> =>
  apiRequest<any>('/api/analytics/campaigns/performance');

export const getMessagesTimeline = (days: number = 30): Promise<any> =>
  apiRequest<any>(`/api/analytics/messages/timeline?days=${days}`);

export const getAIResponsesAnalytics = (days: number = 30): Promise<any> =>
  apiRequest<any>(`/api/analytics/ai-responses/analytics?days=${days}`);

// Health check
export const healthCheck = (): Promise<any> =>
  apiRequest<any>('/health');

// WhatsApp Web Integration
export interface WhatsAppStatus {
  session_active: boolean;
  logged_in: boolean;
  headless?: boolean;
  message: string;
}

export interface WhatsAppLoginRequest {
  headless: boolean;
  timeout?: number;
}

export interface WhatsAppLoginResponse {
  success: boolean;
  message: string;
  requires_qr: boolean;
  session_active: boolean;
}

// WhatsApp Web functions
export const loginWhatsApp = (request: WhatsAppLoginRequest): Promise<WhatsAppLoginResponse> =>
  apiRequest<WhatsAppLoginResponse>('/api/whatsapp/login', {
    method: 'POST',
    body: JSON.stringify(request),
  });

export const waitForWhatsAppLogin = (timeout: number = 60): Promise<any> =>
  apiRequest<any>('/api/whatsapp/wait-login', {
    method: 'POST',
    body: JSON.stringify({ timeout }),
  });

export const getWhatsAppStatus = (): Promise<WhatsAppStatus> =>
  apiRequest<WhatsAppStatus>('/api/whatsapp/status');

export const logoutWhatsApp = (): Promise<any> =>
  apiRequest<any>('/api/whatsapp/logout', {
    method: 'POST',
  });

export const closeWhatsApp = (): Promise<any> =>
  apiRequest<any>('/api/whatsapp/close', {
    method: 'POST',
  });

export const toggleWhatsAppHeadless = (headless: boolean): Promise<any> =>
  apiRequest<any>('/api/whatsapp/toggle-headless', {
    method: 'POST',
    body: JSON.stringify({ headless }),
  });

export const getWhatsAppSessionInfo = (): Promise<any> =>
  apiRequest<any>('/api/whatsapp/session-info');

export const clearWhatsAppSession = (): Promise<any> =>
  apiRequest<any>('/api/whatsapp/clear-session', {
    method: 'DELETE',
  });
