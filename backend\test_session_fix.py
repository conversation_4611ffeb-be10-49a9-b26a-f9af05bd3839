#!/usr/bin/env python3
"""
اختبار إصلاح مشكلة الجلسة
"""

import asyncio
import sys
import os
from pathlib import Path

# إضافة مسار المشروع
sys.path.append(str(Path(__file__).parent))

from whatsapp_selenium import whatsapp_manager
from services.auto_reply_service import get_auto_reply_service
import logging

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_session_consistency():
    """اختبار تطابق الجلسات بين الحملات والردود الذكية"""
    
    print("🧪 اختبار تطابق الجلسات")
    print("=" * 60)
    
    try:
        # 1. فحص حالة whatsapp_manager العامة
        print("1️⃣ فحص whatsapp_manager العامة...")
        status = whatsapp_manager.get_status()
        print(f"   📊 الجلسة نشطة: {status.get('session_active', False)}")
        print(f"   🔐 مسجل دخول: {status.get('logged_in', False)}")
        print(f"   🌐 المتصفح: {'متاح' if whatsapp_manager.driver else 'غير متاح'}")
        
        if whatsapp_manager.driver:
            try:
                current_url = whatsapp_manager.driver.current_url
                print(f"   🔗 URL الحالي: {current_url}")
            except Exception as e:
                print(f"   ❌ خطأ في قراءة URL: {e}")
        
        # 2. إنشاء خدمة الردود الذكية
        print("\n2️⃣ إنشاء خدمة الردود الذكية...")
        auto_reply = get_auto_reply_service(whatsapp_manager)
        
        print(f"   📊 حالة الخدمة: {'تعمل' if auto_reply.is_running else 'متوقفة'}")
        print(f"   🧠 خدمة AI: {'جاهزة' if auto_reply.ai_service else 'غير جاهزة'}")
        print(f"   🌐 المتصفح: {'متاح' if auto_reply.driver else 'غير متاح'}")
        
        # 3. مقارنة المتصفحات
        print("\n3️⃣ مقارنة المتصفحات...")
        manager_driver = whatsapp_manager.driver
        service_driver = auto_reply.driver
        
        print(f"   🔍 نفس المتصفح: {manager_driver is service_driver}")
        
        if manager_driver and service_driver:
            try:
                manager_url = manager_driver.current_url
                service_url = service_driver.current_url
                print(f"   🔗 Manager URL: {manager_url}")
                print(f"   🔗 Service URL: {service_url}")
                print(f"   ✅ نفس URL: {manager_url == service_url}")
            except Exception as e:
                print(f"   ❌ خطأ في مقارنة URLs: {e}")
        
        # 4. اختبار بدء الخدمة
        print("\n4️⃣ اختبار بدء خدمة الردود الذكية...")
        
        if not status.get('session_active') or not status.get('logged_in'):
            print("   ⚠️ WhatsApp Web غير مُسجل دخول")
            print("   💡 يرجى تسجيل الدخول من الإعدادات أولاً")
            return False
        
        try:
            # محاولة بدء الخدمة
            await auto_reply.start_auto_reply()
            print("   ✅ تم بدء خدمة الردود الذكية بنجاح!")
            
            # فحص الحالة بعد البدء
            print(f"   📊 حالة الخدمة بعد البدء: {'تعمل' if auto_reply.is_running else 'متوقفة'}")
            print(f"   🎯 مهمة المراقبة: {'موجودة' if auto_reply.monitor_task else 'غير موجودة'}")
            
            if auto_reply.monitor_task:
                print(f"   🔄 حالة المهمة: {'تعمل' if not auto_reply.monitor_task.done() else 'متوقفة'}")
            
            # إيقاف الخدمة للاختبار
            await auto_reply.stop_auto_reply()
            print("   ✅ تم إيقاف الخدمة بنجاح")
            
            return True
            
        except Exception as e:
            print(f"   ❌ خطأ في بدء الخدمة: {e}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_browser_access():
    """اختبار الوصول للمتصفح"""
    
    print("\n🌐 اختبار الوصول للمتصفح")
    print("=" * 60)
    
    try:
        auto_reply = get_auto_reply_service(whatsapp_manager)
        
        if not auto_reply.driver:
            print("❌ لا يوجد متصفح متاح")
            return False
        
        # اختبار العمليات الأساسية
        print("🔍 اختبار العمليات الأساسية...")
        
        # 1. قراءة URL
        try:
            url = auto_reply.driver.current_url
            print(f"   ✅ URL: {url}")
        except Exception as e:
            print(f"   ❌ خطأ في قراءة URL: {e}")
            return False
        
        # 2. البحث عن عناصر
        try:
            from selenium.webdriver.common.by import By
            chats = auto_reply.driver.find_elements(By.CSS_SELECTOR, "div[data-testid='cell-frame-container']")
            print(f"   ✅ تم العثور على {len(chats)} محادثة")
        except Exception as e:
            print(f"   ❌ خطأ في البحث عن المحادثات: {e}")
            return False
        
        # 3. تنفيذ JavaScript
        try:
            result = auto_reply.driver.execute_script("return document.title;")
            print(f"   ✅ عنوان الصفحة: {result}")
        except Exception as e:
            print(f"   ❌ خطأ في تنفيذ JavaScript: {e}")
            return False
        
        print("✅ جميع اختبارات المتصفح نجحت!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار المتصفح: {e}")
        return False

async def main():
    """الدالة الرئيسية"""
    
    print("🚀 بدء اختبار إصلاح مشكلة الجلسة")
    print("=" * 60)
    
    # اختبار تطابق الجلسات
    session_ok = await test_session_consistency()
    
    # اختبار الوصول للمتصفح
    browser_ok = await test_browser_access()
    
    print("\n" + "=" * 60)
    print("📊 نتائج الاختبار:")
    print(f"   🔗 تطابق الجلسات: {'✅ نجح' if session_ok else '❌ فشل'}")
    print(f"   🌐 الوصول للمتصفح: {'✅ نجح' if browser_ok else '❌ فشل'}")
    
    if session_ok and browser_ok:
        print("\n🎉 تم إصلاح مشكلة الجلسة بنجاح!")
        print("💡 يمكنك الآن تشغيل الردود الذكية من الواجهة")
    else:
        print("\n⚠️ ما زالت هناك مشاكل تحتاج إلى إصلاح")
        print("💡 تحقق من:")
        print("   - تسجيل الدخول في WhatsApp Web")
        print("   - إعدادات OpenAI API")
        print("   - حالة المتصفح")

if __name__ == "__main__":
    asyncio.run(main())
