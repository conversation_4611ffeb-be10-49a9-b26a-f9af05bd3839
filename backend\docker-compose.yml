version: '3.8'

services:
  backend:
    build: .
    ports:
      - "8000:8000"
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
      - ./smart_whatsapp.db:/app/smart_whatsapp.db
    environment:
      - DATABASE_URL=sqlite:///./smart_whatsapp.db
      - HOST=0.0.0.0
      - PORT=8000
      - DEBUG=False
    env_file:
      - .env
    restart: unless-stopped
    
  # Redis للتخزين المؤقت (اختياري)
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    restart: unless-stopped
    
networks:
  default:
    name: whatsapp-campaigner
