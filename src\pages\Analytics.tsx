import { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  BarChart3,
  TrendingUp,
  Users,
  MessageSquare,
  Clock,
  Target,
  Download,
  Calendar,
  Filter,
  RefreshCw,
  AlertCircle
} from "lucide-react";
import { useApi } from "@/hooks/useApi";
import {
  getCampaignsPerformance,
  getMessagesTimeline,
  getAIResponsesAnalytics
} from "@/lib/api";

const Analytics = () => {
  const [selectedPeriod, setSelectedPeriod] = useState(30);

  // جلب البيانات الحقيقية
  const { data: campaignsData, loading: campaignsLoading, error: campaignsError, refetch: refetchCampaigns } = useApi(getCampaignsPerformance);
  const { data: timelineData, loading: timelineLoading, refetch: refetchTimeline } = useApi(() => getMessagesTimeline(selectedPeriod));
  const { data: aiAnalytics, loading: aiLoading, refetch: refetchAI } = useApi(() => getAIResponsesAnalytics(selectedPeriod));

  // إعداد المقاييس من البيانات الحقيقية
  const metrics = campaignsData ? [
    {
      title: "إجمالي الرسائل",
      value: campaignsData.summary?.total_messages_sent?.toLocaleString() || "0",
      change: "+15.2%", // يمكن حسابها من البيانات التاريخية
      icon: MessageSquare,
      color: "text-blue-600"
    },
    {
      title: "معدل التسليم",
      value: `${campaignsData.summary?.average_success_rate?.toFixed(1) || 0}%`,
      change: "+2.1%",
      icon: Target,
      color: "text-green-600"
    },
    {
      title: "إجمالي الحملات",
      value: campaignsData.summary?.total_campaigns?.toString() || "0",
      change: "+8.3%",
      icon: TrendingUp,
      color: "text-purple-600"
    },
    {
      title: "الردود التلقائية",
      value: aiAnalytics?.summary?.total_responses?.toLocaleString() || "0",
      change: "-12%",
      icon: Clock,
      color: "text-orange-600"
    }
  ] : [];

  // بيانات الرسم البياني من البيانات الحقيقية
  const chartData = timelineData?.timeline || [];

  const topPerformingCampaigns = [
    { name: "عروض الجمعة البيضاء", deliveryRate: 98.5, responseRate: 85.2 },
    { name: "إطلاق المنتج الجديد", deliveryRate: 97.8, responseRate: 79.6 },
    { name: "خصومات الصيف", deliveryRate: 96.2, responseRate: 72.3 },
    { name: "دعوة للفعالية", deliveryRate: 95.8, responseRate: 68.9 },
  ];

  const customerSegments = [
    { segment: "عملاء جدد", count: 1250, percentage: 35 },
    { segment: "عملاء متكررون", count: 1890, percentage: 52 },
    { segment: "عملاء مميزون", count: 470, percentage: 13 },
  ];

  return (
    <div className="min-h-screen bg-background">
      <div className="max-w-7xl mx-auto p-6">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-foreground mb-2">
              إحصائيات الأداء
            </h1>
            <p className="text-muted-foreground text-lg">
              تتبع أداء حملاتك ورسائلك بالتفصيل
            </p>
          </div>

          <div className="flex gap-2 mt-4 md:mt-0">
            <select
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(Number(e.target.value))}
              className="px-3 py-2 border rounded-md"
            >
              <option value={7}>آخر 7 أيام</option>
              <option value={30}>آخر 30 يوم</option>
              <option value={90}>آخر 3 أشهر</option>
            </select>
            <Button
              variant="outline"
              size="sm"
              className="gap-2"
              onClick={() => {
                refetchCampaigns();
                refetchTimeline();
                refetchAI();
              }}
              disabled={campaignsLoading || timelineLoading || aiLoading}
            >
              <RefreshCw className={`h-4 w-4 ${(campaignsLoading || timelineLoading || aiLoading) ? 'animate-spin' : ''}`} />
              تحديث
            </Button>
            <Button variant="outline" size="sm" className="gap-2">
              <Download className="h-4 w-4" />
              تصدير
            </Button>
          </div>
        </div>

        {/* Error State */}
        {campaignsError && (
          <Card className="p-6 mb-8 border-destructive/50 bg-destructive/5">
            <div className="flex items-center gap-3">
              <AlertCircle className="h-5 w-5 text-destructive" />
              <div>
                <h3 className="font-semibold text-destructive">خطأ في تحميل البيانات</h3>
                <p className="text-sm text-muted-foreground mt-1">{campaignsError}</p>
              </div>
            </div>
          </Card>
        )}

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {campaignsLoading ? (
            // Loading state
            [1, 2, 3, 4].map((i) => (
              <Card key={i} className="p-6">
                <div className="animate-pulse">
                  <div className="flex items-center justify-between mb-4">
                    <div className="w-10 h-10 bg-muted rounded-lg"></div>
                    <div className="w-12 h-4 bg-muted rounded"></div>
                  </div>
                  <div className="w-16 h-8 bg-muted rounded mb-2"></div>
                  <div className="w-24 h-4 bg-muted rounded"></div>
                </div>
              </Card>
            ))
          ) : (
            metrics.map((metric, index) => {
              const Icon = metric.icon;
              return (
                <Card key={index} className="p-6 hover:shadow-lg transition-all duration-300">
                  <div className="flex items-center justify-between mb-4">
                    <div className={`p-2 rounded-lg bg-accent ${metric.color}`}>
                      <Icon className="h-6 w-6" />
                    </div>
                    <span className={`text-sm font-medium ${
                    metric.change.startsWith('+') ? 'text-success' : 
                    metric.change.startsWith('-') ? 'text-destructive' : 'text-muted-foreground'
                  }`}>
                    {metric.change}
                  </span>
                </div>
                <h3 className="text-2xl font-bold text-foreground mb-1">
                  {metric.value}
                </h3>
                <p className="text-sm text-muted-foreground">
                  {metric.title}
                </p>
                </Card>
              );
            })
          )}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Performance Chart */}
          <div className="lg:col-span-2">
            <Card className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-foreground">
                  الأداء الأسبوعي
                </h2>
                <div className="flex gap-4 text-sm">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-primary rounded-full"></div>
                    <span className="text-muted-foreground">مرسلة</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-success rounded-full"></div>
                    <span className="text-muted-foreground">وصلت</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-warning rounded-full"></div>
                    <span className="text-muted-foreground">استجابات</span>
                  </div>
                </div>
              </div>
              
              {/* Simple Bar Chart Representation */}
              <div className="space-y-4">
                {chartData.map((data, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-foreground font-medium">{data.day}</span>
                      <span className="text-muted-foreground">{data.sent} رسالة</span>
                    </div>
                    <div className="flex gap-1 h-8">
                      <div 
                        className="bg-primary rounded"
                        style={{ width: `${(data.sent / 220) * 100}%` }}
                      ></div>
                      <div 
                        className="bg-success rounded"
                        style={{ width: `${(data.delivered / 220) * 100}%` }}
                      ></div>
                      <div 
                        className="bg-warning rounded"
                        style={{ width: `${(data.responses / 220) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          </div>

          {/* Sidebar Stats */}
          <div className="space-y-6">
            {/* Top Campaigns */}
            <Card className="p-6">
              <h3 className="text-lg font-semibold text-foreground mb-4">
                أفضل الحملات أداءً
              </h3>
              
              <div className="space-y-4">
                {topPerformingCampaigns.map((campaign, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex justify-between items-start">
                      <span className="text-sm font-medium text-foreground leading-tight">
                        {campaign.name}
                      </span>
                      <span className="text-xs text-success">
                        {campaign.responseRate}%
                      </span>
                    </div>
                    <div className="w-full bg-accent/30 rounded-full h-2">
                      <div 
                        className="bg-success h-2 rounded-full transition-all duration-300"
                        style={{ width: `${campaign.responseRate}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </Card>

            {/* Customer Segments */}
            <Card className="p-6">
              <h3 className="text-lg font-semibold text-foreground mb-4">
                تقسيم العملاء
              </h3>
              
              <div className="space-y-4">
                {customerSegments.map((segment, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div>
                      <div className="text-sm font-medium text-foreground">
                        {segment.segment}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {segment.count.toLocaleString()} عميل
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-foreground">
                        {segment.percentage}%
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </Card>

            {/* Quick Actions */}
            <Card className="p-6">
              <h3 className="text-lg font-semibold text-foreground mb-4">
                إجراءات سريعة
              </h3>
              
              <div className="space-y-3">
                <Button variant="outline" size="sm" className="w-full justify-start gap-2">
                  <BarChart3 className="h-4 w-4" />
                  تقرير مفصل
                </Button>
                <Button variant="outline" size="sm" className="w-full justify-start gap-2">
                  <Download className="h-4 w-4" />
                  تصدير البيانات
                </Button>
                <Button variant="outline" size="sm" className="w-full justify-start gap-2">
                  <Users className="h-4 w-4" />
                  تحليل العملاء
                </Button>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Analytics;