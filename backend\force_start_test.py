#!/usr/bin/env python3
"""
اختبار إجباري لبدء الردود التلقائية
"""

import asyncio
import requests
import json

def force_start_auto_reply():
    """إجبار بدء الردود التلقائية مع تشخيص مفصل"""
    
    print("🚀 إجبار بدء الردود التلقائية")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    # 1. فحص الحالة الحالية
    print("1️⃣ فحص الحالة الحالية...")
    try:
        response = requests.get(f"{base_url}/api/ai-responses/auto-reply/status")
        if response.status_code == 200:
            data = response.json()
            print(f"   📊 الحالة: {data}")
            current_running = data.get('is_running', False)
            print(f"   🔄 تعمل حالياً: {current_running}")
        else:
            print(f"   ❌ خطأ في فحص الحالة: {response.status_code}")
            return
    except Exception as e:
        print(f"   ❌ خطأ في الاتصال: {e}")
        return
    
    # 2. إجبار التفعيل عدة مرات
    print("\n2️⃣ إجبار التفعيل...")
    for attempt in range(3):
        print(f"\n   محاولة {attempt + 1}/3:")
        
        try:
            # تفعيل الردود التلقائية
            response = requests.post(f"{base_url}/api/ai-responses/toggle-auto-reply")
            print(f"   📊 كود الاستجابة: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"   📝 الرسالة: {data.get('message', 'غير متوفرة')}")
                print(f"   🔄 مفعل: {data.get('auto_reply_enabled', False)}")
                print(f"   🤖 يعمل: {data.get('service_running', False)}")
                
                if 'error' in data:
                    print(f"   ⚠️ خطأ: {data['error']}")
                
                # فحص الحالة بعد التفعيل
                print("   🔍 فحص الحالة بعد التفعيل...")
                response2 = requests.get(f"{base_url}/api/ai-responses/auto-reply/status")
                if response2.status_code == 200:
                    status_data = response2.json()
                    print(f"   📊 الحالة الجديدة: {status_data.get('is_running', False)}")
                    
                    if status_data.get('is_running'):
                        print("   🎉 نجح! الخدمة تعمل الآن")
                        return True
                    else:
                        print("   ❌ فشل! الخدمة ما زالت متوقفة")
                
            else:
                print(f"   ❌ فشل في التفعيل: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"   📝 تفاصيل الخطأ: {error_data}")
                except:
                    print(f"   📝 نص الخطأ: {response.text}")
                
        except Exception as e:
            print(f"   ❌ خطأ في المحاولة: {e}")
        
        # انتظار بين المحاولات
        if attempt < 2:
            print("   ⏳ انتظار 3 ثوانٍ...")
            import time
            time.sleep(3)
    
    print("\n❌ فشل في جميع المحاولات")
    return False

def check_prerequisites():
    """فحص المتطلبات الأساسية"""
    
    print("\n🔍 فحص المتطلبات الأساسية")
    print("-" * 30)
    
    base_url = "http://localhost:8000"
    
    # فحص WhatsApp Web
    try:
        response = requests.get(f"{base_url}/api/whatsapp/status")
        if response.status_code == 200:
            data = response.json()
            print(f"📱 WhatsApp Web: {'✅ متصل' if data.get('session_active') and data.get('logged_in') else '❌ غير متصل'}")
            if not data.get('session_active') or not data.get('logged_in'):
                print("   💡 يجب تسجيل الدخول لـ WhatsApp Web أولاً")
                return False
        else:
            print(f"📱 WhatsApp Web: ❌ خطأ ({response.status_code})")
            return False
    except Exception as e:
        print(f"📱 WhatsApp Web: ❌ خطأ ({e})")
        return False
    
    # فحص الإعدادات
    try:
        response = requests.get(f"{base_url}/api/ai-responses/settings/current")
        if response.status_code == 200:
            data = response.json()
            has_api_key = bool(data.get('openai_api_key'))
            auto_reply_enabled = data.get('auto_reply_enabled', False)
            
            print(f"🔑 مفتاح OpenAI: {'✅ موجود' if has_api_key else '❌ غير موجود'}")
            print(f"🔄 الرد التلقائي: {'✅ مفعل' if auto_reply_enabled else '❌ معطل'}")
            
            if not has_api_key:
                print("   💡 يجب إضافة مفتاح OpenAI API في الإعدادات")
                return False
                
            return True
        else:
            print(f"⚙️ الإعدادات: ❌ خطأ ({response.status_code})")
            return False
    except Exception as e:
        print(f"⚙️ الإعدادات: ❌ خطأ ({e})")
        return False

if __name__ == "__main__":
    print("🧪 اختبار إجباري لبدء الردود التلقائية")
    print("=" * 60)
    
    # فحص المتطلبات أولاً
    if not check_prerequisites():
        print("\n❌ المتطلبات الأساسية غير متوفرة!")
        print("🔧 أصلح المشاكل أعلاه ثم أعد المحاولة")
        exit(1)
    
    # محاولة بدء الخدمة
    success = force_start_auto_reply()
    
    if success:
        print("\n🎉 تم بدء الردود التلقائية بنجاح!")
        print("💡 جرب إرسال رسالة لنفسك من هاتف آخر")
        print("⏳ انتظر 30-90 ثانية للحصول على رد ذكي")
    else:
        print("\n❌ فشل في بدء الردود التلقائية!")
        print("🔧 تحقق من logs الخادم للمزيد من التفاصيل")
        print("💡 جرب إعادة تشغيل الخادم")
