#!/usr/bin/env python3
"""
تشغيل نظام الردود الذكية
"""

import asyncio
import sys
import os
from pathlib import Path

# إضافة مسار المشروع
sys.path.append(str(Path(__file__).parent))

from whatsapp_selenium import whatsapp_manager
from services.auto_reply_service import get_auto_reply_service
import logging

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def main():
    """تشغيل نظام الردود الذكية"""
    
    print("🚀 بدء تشغيل نظام الردود الذكية")
    print("=" * 60)
    
    try:
        # إنشاء خدمة الردود التلقائية
        auto_reply = get_auto_reply_service(whatsapp_manager)
        
        # التحقق من حالة WhatsApp Web
        print("🔍 فحص حالة WhatsApp Web...")
        status = whatsapp_manager.get_status()
        
        if not status.get('session_active') or not status.get('logged_in'):
            print("❌ WhatsApp Web غير مُسجل دخول")
            print("💡 يرجى تسجيل الدخول في WhatsApp Web أولاً")
            return
        
        print("✅ WhatsApp Web متصل ومُسجل دخول")
        
        # بدء المراقبة
        print("\n🔄 بدء مراقبة الرسائل الجديدة...")
        print("💡 لإيقاف النظام اضغط Ctrl+C")
        print("=" * 60)
        
        # تشغيل النظام
        await auto_reply.start_monitoring()
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف النظام بواسطة المستخدم")
        
        # إيقاف المراقبة
        if 'auto_reply' in locals():
            await auto_reply.stop_monitoring()
            print("✅ تم إيقاف المراقبة بنجاح")
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        import traceback
        traceback.print_exc()
        
        # إيقاف المراقبة في حالة الخطأ
        if 'auto_reply' in locals():
            try:
                await auto_reply.stop_monitoring()
            except:
                pass

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 وداعاً!")
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
