from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta, date

from models import Campaign, Contact, Message, AIResponse, Settings
from schemas import DashboardStats

class AnalyticsService:
    """خدمة التحليلات والإحصائيات"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_dashboard_stats(self) -> DashboardStats:
        """الحصول على إحصائيات لوحة التحكم"""
        
        # إجمالي الرسائل المرسلة
        total_messages_sent = self.db.query(Message).filter(
            Message.status.in_(["sent", "delivered"])
        ).count()
        
        # عدد الردود التلقائية
        auto_replies_count = self.db.query(AIResponse).count()
        
        # العملاء النشطين (الذين تم إرسال رسائل لهم في آخر 30 يوم)
        thirty_days_ago = datetime.now() - timedelta(days=30)
        active_customers = self.db.query(Contact).join(Message).filter(
            Message.created_at >= thirty_days_ago
        ).distinct().count()
        
        # معدل النجاح
        total_messages = self.db.query(Message).count()
        delivered_messages = self.db.query(Message).filter(Message.status == "delivered").count()
        success_rate = (delivered_messages / total_messages * 100) if total_messages > 0 else 0
        
        # النشاط الأخير
        recent_activity = self._get_recent_activity()
        
        return DashboardStats(
            total_messages_sent=total_messages_sent,
            auto_replies_count=auto_replies_count,
            active_customers=active_customers,
            success_rate=round(success_rate, 1),
            recent_activity=recent_activity
        )
    
    def _get_recent_activity(self) -> List[Dict[str, Any]]:
        """الحصول على النشاط الأخير"""
        recent_activity = []
        
        # آخر الحملات
        recent_campaigns = self.db.query(Campaign).order_by(Campaign.created_at.desc()).limit(3).all()
        for campaign in recent_campaigns:
            time_diff = datetime.now() - campaign.created_at
            time_str = self._format_time_diff(time_diff)
            
            recent_activity.append({
                "action": "تم إرسال حملة ترويجية",
                "target": f"{campaign.total_contacts} عميل",
                "time": time_str,
                "status": "success" if campaign.status == "completed" else "info"
            })
        
        # آخر الردود التلقائية
        recent_ai_responses = self.db.query(AIResponse).order_by(AIResponse.created_at.desc()).limit(2).all()
        for response in recent_ai_responses:
            time_diff = datetime.now() - response.created_at
            time_str = self._format_time_diff(time_diff)
            
            recent_activity.append({
                "action": "رد تلقائي جديد",
                "target": response.contact_phone,
                "time": time_str,
                "status": "info"
            })
        
        return recent_activity[:5]  # أحدث 5 أنشطة
    
    def _format_time_diff(self, time_diff: timedelta) -> str:
        """تنسيق الفرق الزمني"""
        if time_diff.days > 0:
            return f"منذ {time_diff.days} يوم"
        elif time_diff.seconds > 3600:
            hours = time_diff.seconds // 3600
            return f"منذ {hours} ساعة"
        else:
            minutes = time_diff.seconds // 60
            return f"منذ {minutes} دقيقة"
    
    def get_campaigns_analytics(
        self, 
        start_date: Optional[date] = None,
        end_date: Optional[date] = None
    ) -> Dict[str, Any]:
        """تحليلات الحملات"""
        
        query = self.db.query(Campaign)
        
        if start_date:
            query = query.filter(Campaign.created_at >= start_date)
        if end_date:
            query = query.filter(Campaign.created_at <= end_date)
        
        campaigns = query.all()
        
        # إحصائيات عامة
        total_campaigns = len(campaigns)
        completed_campaigns = len([c for c in campaigns if c.status == "completed"])
        running_campaigns = len([c for c in campaigns if c.status == "running"])
        failed_campaigns = len([c for c in campaigns if c.status == "failed"])
        
        # إحصائيات الرسائل
        total_messages_sent = sum([c.sent_count for c in campaigns])
        total_delivered = sum([c.delivered_count for c in campaigns])
        total_failed = sum([c.failed_count for c in campaigns])
        
        # معدل النجاح العام
        overall_success_rate = (total_delivered / total_messages_sent * 100) if total_messages_sent > 0 else 0
        
        # أفضل الحملات أداءً
        best_campaigns = sorted(
            [c for c in campaigns if c.sent_count > 0],
            key=lambda x: (x.delivered_count / x.sent_count) if x.sent_count > 0 else 0,
            reverse=True
        )[:5]
        
        return {
            "summary": {
                "total_campaigns": total_campaigns,
                "completed_campaigns": completed_campaigns,
                "running_campaigns": running_campaigns,
                "failed_campaigns": failed_campaigns,
                "total_messages_sent": total_messages_sent,
                "total_delivered": total_delivered,
                "total_failed": total_failed,
                "overall_success_rate": round(overall_success_rate, 2)
            },
            "best_campaigns": [
                {
                    "id": c.id,
                    "name": c.name,
                    "success_rate": round((c.delivered_count / c.sent_count * 100), 2),
                    "total_sent": c.sent_count,
                    "delivered": c.delivered_count
                }
                for c in best_campaigns
            ]
        }
    
    def get_messages_timeline(self, days: int = 30) -> Dict[str, Any]:
        """الجدول الزمني للرسائل"""
        
        start_date = datetime.now() - timedelta(days=days)
        
        # استعلام للحصول على عدد الرسائل يومياً
        daily_messages = self.db.query(
            func.date(Message.created_at).label('date'),
            func.count(Message.id).label('total'),
            func.sum(func.case([(Message.status == 'delivered', 1)], else_=0)).label('delivered'),
            func.sum(func.case([(Message.status == 'failed', 1)], else_=0)).label('failed')
        ).filter(
            Message.created_at >= start_date
        ).group_by(
            func.date(Message.created_at)
        ).order_by(
            func.date(Message.created_at)
        ).all()
        
        timeline_data = []
        for day in daily_messages:
            success_rate = (day.delivered / day.total * 100) if day.total > 0 else 0
            timeline_data.append({
                "date": day.date.isoformat(),
                "total_messages": day.total,
                "delivered_messages": day.delivered,
                "failed_messages": day.failed,
                "success_rate": round(success_rate, 2)
            })
        
        return {
            "timeline": timeline_data,
            "period_summary": {
                "total_days": days,
                "total_messages": sum([d["total_messages"] for d in timeline_data]),
                "total_delivered": sum([d["delivered_messages"] for d in timeline_data]),
                "total_failed": sum([d["failed_messages"] for d in timeline_data]),
                "average_daily_messages": round(
                    sum([d["total_messages"] for d in timeline_data]) / len(timeline_data), 2
                ) if timeline_data else 0
            }
        }
    
    def get_ai_responses_analytics(self, days: int = 30) -> Dict[str, Any]:
        """تحليلات الردود التلقائية"""
        
        start_date = datetime.now() - timedelta(days=days)
        
        # إجمالي الردود في الفترة
        total_responses = self.db.query(AIResponse).filter(
            AIResponse.created_at >= start_date
        ).count()
        
        # متوسط وقت الاستجابة
        avg_response_time = self.db.query(
            func.avg(AIResponse.response_time_ms)
        ).filter(
            AIResponse.created_at >= start_date,
            AIResponse.response_time_ms.isnot(None)
        ).scalar()
        
        # الردود حسب النموذج المستخدم
        responses_by_model = self.db.query(
            AIResponse.gpt_model_used,
            func.count(AIResponse.id).label('count')
        ).filter(
            AIResponse.created_at >= start_date
        ).group_by(AIResponse.gpt_model_used).all()
        
        # أكثر العملاء تفاعلاً
        top_customers = self.db.query(
            AIResponse.contact_phone,
            func.count(AIResponse.id).label('response_count')
        ).filter(
            AIResponse.created_at >= start_date
        ).group_by(
            AIResponse.contact_phone
        ).order_by(
            func.count(AIResponse.id).desc()
        ).limit(10).all()
        
        return {
            "summary": {
                "total_responses": total_responses,
                "avg_response_time_ms": round(avg_response_time, 2) if avg_response_time else 0,
                "period_days": days
            },
            "models_usage": [
                {"model": model, "count": count, "percentage": round(count/total_responses*100, 2)}
                for model, count in responses_by_model
            ] if total_responses > 0 else [],
            "top_customers": [
                {"phone": customer.contact_phone, "responses_count": customer.response_count}
                for customer in top_customers
            ]
        }
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """مقاييس الأداء العامة"""
        
        # إحصائيات الحملات
        total_campaigns = self.db.query(Campaign).count()
        active_campaigns = self.db.query(Campaign).filter(Campaign.status == "running").count()
        completed_campaigns = self.db.query(Campaign).filter(Campaign.status == "completed").count()
        
        # إحصائيات الرسائل
        total_messages = self.db.query(Message).count()
        delivered_messages = self.db.query(Message).filter(Message.status == "delivered").count()
        failed_messages = self.db.query(Message).filter(Message.status == "failed").count()
        
        # إحصائيات العملاء
        total_contacts = self.db.query(Contact).count()
        unique_phone_numbers = self.db.query(Contact.phone_number).distinct().count()
        
        # إحصائيات الردود التلقائية
        total_ai_responses = self.db.query(AIResponse).count()
        
        # معدلات النجاح
        campaign_success_rate = (completed_campaigns / total_campaigns * 100) if total_campaigns > 0 else 0
        message_success_rate = (delivered_messages / total_messages * 100) if total_messages > 0 else 0
        
        return {
            "campaigns": {
                "total": total_campaigns,
                "active": active_campaigns,
                "completed": completed_campaigns,
                "success_rate": round(campaign_success_rate, 2)
            },
            "messages": {
                "total": total_messages,
                "delivered": delivered_messages,
                "failed": failed_messages,
                "success_rate": round(message_success_rate, 2)
            },
            "contacts": {
                "total": total_contacts,
                "unique_phones": unique_phone_numbers
            },
            "ai_responses": {
                "total": total_ai_responses
            }
        }
    
    def get_cost_analysis(self, cost_per_message: float = 0.05) -> Dict[str, Any]:
        """تحليل التكاليف"""
        
        # إجمالي الرسائل المرسلة
        total_sent = self.db.query(Message).filter(Message.status.in_(["sent", "delivered"])).count()
        
        # التكلفة الإجمالية
        total_cost = total_sent * cost_per_message
        
        # التكلفة حسب الحملة
        campaigns_cost = []
        campaigns = self.db.query(Campaign).all()
        
        for campaign in campaigns:
            campaign_cost = campaign.sent_count * cost_per_message
            campaigns_cost.append({
                "campaign_id": campaign.id,
                "campaign_name": campaign.name,
                "messages_sent": campaign.sent_count,
                "cost": round(campaign_cost, 2),
                "cost_per_delivered": round(
                    campaign_cost / campaign.delivered_count, 2
                ) if campaign.delivered_count > 0 else 0
            })
        
        # التكلفة الشهرية
        current_month = datetime.now().replace(day=1)
        monthly_messages = self.db.query(Message).filter(
            Message.created_at >= current_month,
            Message.status.in_(["sent", "delivered"])
        ).count()
        monthly_cost = monthly_messages * cost_per_message
        
        return {
            "total_cost": round(total_cost, 2),
            "monthly_cost": round(monthly_cost, 2),
            "cost_per_message": cost_per_message,
            "total_messages_sent": total_sent,
            "campaigns_cost": sorted(campaigns_cost, key=lambda x: x["cost"], reverse=True)
        }
