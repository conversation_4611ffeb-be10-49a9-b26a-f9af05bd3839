# Smart WhatsApp Campaigner - Backend

## نظام إدارة حملات WhatsApp الذكية مع الردود التلقائية

### الميزات الرئيسية

- 🚀 **إدارة الحملات**: إنشاء وإرسال حملات ترويجية متقدمة
- 🤖 **الردود التلقائية**: ردود ذكية باستخدام GPT
- 📊 **التحليلات**: إحصائيات مفصلة وتقارير شاملة
- 📱 **WhatsApp API**: دمج مع WhatsApp Business API
- 📁 **رفع الملفات**: دعم ملفات Excel و CSV للعملاء
- 🔒 **الأمان**: حماية البيانات والمصادقة

### التقنيات المستخدمة

- **FastAPI**: إطار عمل Python سريع وحديث
- **SQLAlchemy**: ORM متقدم لقاعدة البيانات
- **SQLite**: قاعدة بيانات مدمجة وسريعة
- **OpenAI GPT**: ذكاء اصطناعي للردود التلقائية
- **Pandas**: معالجة ملفات Excel و CSV
- **Aiohttp**: عميل HTTP غير متزامن

### التثبيت والتشغيل

#### 1. تثبيت المتطلبات

```bash
cd backend
pip install -r requirements.txt
```

#### 2. إعداد متغيرات البيئة

```bash
cp .env.example .env
# قم بتعديل ملف .env وإضافة مفاتيح API الخاصة بك
```

#### 3. تشغيل الخادم

```bash
python main.py
```

أو باستخدام uvicorn مباشرة:

```bash
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

### الـ API Endpoints

#### الحملات
- `GET /api/campaigns/` - قائمة الحملات
- `POST /api/campaigns/` - إنشاء حملة جديدة
- `GET /api/campaigns/{id}` - تفاصيل حملة
- `PUT /api/campaigns/{id}` - تحديث حملة
- `DELETE /api/campaigns/{id}` - حذف حملة
- `POST /api/campaigns/{id}/upload-contacts` - رفع ملف العملاء
- `POST /api/campaigns/{id}/send` - إرسال الحملة

#### الردود التلقائية
- `GET /api/ai-responses/` - قائمة الردود التلقائية
- `POST /api/ai-responses/test` - اختبار الرد التلقائي
- `GET /api/ai-responses/settings/current` - الإعدادات الحالية
- `PUT /api/ai-responses/settings` - تحديث الإعدادات
- `POST /api/ai-responses/toggle-auto-reply` - تفعيل/إلغاء الرد التلقائي

#### التحليلات
- `GET /api/analytics/dashboard` - إحصائيات لوحة التحكم
- `GET /api/analytics/campaigns/performance` - أداء الحملات
- `GET /api/analytics/messages/timeline` - الجدول الزمني للرسائل
- `GET /api/analytics/ai-responses/analytics` - تحليلات الردود التلقائية

### هيكل المشروع

```
backend/
├── main.py                 # الملف الرئيسي للتطبيق
├── database.py            # إعداد قاعدة البيانات
├── models.py              # نماذج قاعدة البيانات
├── schemas.py             # مخططات التحقق من البيانات
├── requirements.txt       # المكتبات المطلوبة
├── .env.example          # مثال على متغيرات البيئة
├── routers/              # مسارات API
│   ├── campaigns.py      # مسارات الحملات
│   ├── ai_responses.py   # مسارات الردود التلقائية
│   └── analytics.py      # مسارات التحليلات
├── services/             # الخدمات
│   ├── whatsapp_service.py    # خدمة WhatsApp
│   ├── ai_service.py          # خدمة الذكاء الاصطناعي
│   ├── campaign_service.py    # خدمة الحملات
│   ├── file_service.py        # خدمة الملفات
│   └── analytics_service.py   # خدمة التحليلات
└── uploads/              # مجلد الملفات المرفوعة
```

### إعداد WhatsApp Business API

1. إنشاء حساب Facebook Developer
2. إنشاء تطبيق WhatsApp Business
3. الحصول على Access Token و Phone Number ID
4. إضافة المفاتيح في ملف .env

### إعداد OpenAI API

1. إنشاء حساب OpenAI
2. الحصول على API Key
3. إضافة المفتاح في ملف .env

### الاستخدام

#### إنشاء حملة جديدة

```python
import requests

# إنشاء حملة
campaign_data = {
    "name": "حملة العروض الصيفية",
    "message_text": "مرحباً {الاسم}، لديك عرض خاص!"
}

response = requests.post("http://localhost:8000/api/campaigns/", json=campaign_data)
campaign = response.json()

# رفع ملف العملاء
files = {"file": open("customers.xlsx", "rb")}
requests.post(f"http://localhost:8000/api/campaigns/{campaign['id']}/upload-contacts", files=files)

# إرسال الحملة
requests.post(f"http://localhost:8000/api/campaigns/{campaign['id']}/send")
```

#### تفعيل الرد التلقائي

```python
# تحديث إعدادات GPT
settings = {
    "openai_api_key": "your_api_key",
    "gpt_prompt": "أنت مساعد خدمة عملاء ودود...",
    "auto_reply_enabled": True
}

requests.put("http://localhost:8000/api/ai-responses/settings", json=settings)
```

### المساهمة

نرحب بالمساهمات! يرجى:

1. Fork المشروع
2. إنشاء branch جديد للميزة
3. Commit التغييرات
4. Push إلى Branch
5. إنشاء Pull Request

### الترخيص

هذا المشروع مرخص تحت رخصة MIT.

### الدعم

للحصول على الدعم، يرجى إنشاء issue في GitHub أو التواصل معنا.

### التحديثات المستقبلية

- [ ] دعم الرسائل الصوتية
- [ ] تحليلات متقدمة بالذكاء الاصطناعي
- [ ] دعم قنوات أخرى (Telegram, SMS)
- [ ] واجهة إدارة متقدمة
- [ ] تصدير التقارير PDF
