#!/usr/bin/env python3
"""
تشخيص عميق لمشكلة الردود التلقائية
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(__file__))

from database import SessionLocal
from models import Settings
from whatsapp_selenium import whatsapp_manager
from services.auto_reply_service import get_auto_reply_service

async def debug_auto_reply():
    """تشخيص عميق لمشكلة الردود التلقائية"""
    
    print("🔍 تشخيص عميق لمشكلة الردود التلقائية")
    print("=" * 60)
    
    # 1. فحص قاعدة البيانات والإعدادات
    print("1️⃣ فحص قاعدة البيانات والإعدادات...")
    try:
        db = SessionLocal()
        settings = db.query(Settings).first()
        
        if not settings:
            print("   ❌ لا توجد إعدادات في قاعدة البيانات")
            return
        
        print(f"   ✅ الإعدادات موجودة")
        print(f"   🔑 مفتاح OpenAI: {'موجود' if settings.openai_api_key else 'غير موجود'}")
        print(f"   🔄 الرد التلقائي: {'مفعل' if settings.auto_reply_enabled else 'معطل'}")
        print(f"   🤖 نموذج GPT: {settings.gpt_model}")
        
        if not settings.openai_api_key:
            print("   ❌ مفتاح OpenAI غير موجود - هذا سبب المشكلة!")
            return
            
        if not settings.auto_reply_enabled:
            print("   ⚠️ الرد التلقائي معطل في الإعدادات")
            
        db.close()
        
    except Exception as e:
        print(f"   ❌ خطأ في قاعدة البيانات: {e}")
        return
    
    # 2. فحص حالة WhatsApp Web
    print("\n2️⃣ فحص حالة WhatsApp Web...")
    try:
        status = whatsapp_manager.get_status()
        print(f"   📊 حالة الجلسة: {'نشطة' if status.get('session_active') else 'غير نشطة'}")
        print(f"   🔐 تسجيل الدخول: {'مُسجل' if status.get('logged_in') else 'غير مُسجل'}")
        print(f"   🌐 المتصفح: {'يعمل' if status.get('driver_active') else 'متوقف'}")
        
        if not status.get('session_active') or not status.get('logged_in'):
            print("   ❌ WhatsApp Web غير مُسجل دخول - هذا سبب المشكلة!")
            return
            
    except Exception as e:
        print(f"   ❌ خطأ في فحص WhatsApp: {e}")
        return
    
    # 3. فحص خدمة الردود التلقائية
    print("\n3️⃣ فحص خدمة الردود التلقائية...")
    try:
        auto_reply = get_auto_reply_service(whatsapp_manager)
        print(f"   📊 حالة الخدمة: {'تعمل' if auto_reply.is_running else 'متوقفة'}")
        print(f"   🧠 خدمة AI: {'جاهزة' if auto_reply.ai_service else 'غير جاهزة'}")
        print(f"   🌐 المتصفح: {'متصل' if auto_reply.driver else 'غير متصل'}")
        print(f"   🎯 مهمة المراقبة: {'موجودة' if auto_reply.monitor_task else 'غير موجودة'}")
        
        if auto_reply.monitor_task:
            print(f"   🔄 حالة المهمة: {'تعمل' if not auto_reply.monitor_task.done() else 'متوقفة'}")
            if auto_reply.monitor_task.done():
                try:
                    exception = auto_reply.monitor_task.exception()
                    if exception:
                        print(f"   ❌ خطأ في المهمة: {exception}")
                except:
                    pass
        
    except Exception as e:
        print(f"   ❌ خطأ في فحص الخدمة: {e}")
        return
    
    # 4. محاولة بدء الخدمة مع تفاصيل الأخطاء
    print("\n4️⃣ محاولة بدء الخدمة...")
    try:
        if not auto_reply.is_running:
            print("   🚀 محاولة بدء الخدمة...")
            await auto_reply.start_auto_reply()
            
            # انتظار قصير للتحقق من النجاح
            await asyncio.sleep(2)
            
            print(f"   📊 حالة الخدمة بعد البدء: {'تعمل' if auto_reply.is_running else 'متوقفة'}")
            
            if auto_reply.monitor_task:
                print(f"   🔄 حالة مهمة المراقبة: {'تعمل' if not auto_reply.monitor_task.done() else 'متوقفة'}")
                
                if auto_reply.monitor_task.done():
                    try:
                        exception = auto_reply.monitor_task.exception()
                        if exception:
                            print(f"   ❌ خطأ في مهمة المراقبة: {exception}")
                            import traceback
                            traceback.print_exception(type(exception), exception, exception.__traceback__)
                    except:
                        pass
            else:
                print("   ❌ مهمة المراقبة لم يتم إنشاؤها")
                
        else:
            print("   ✅ الخدمة تعمل بالفعل")
            
    except Exception as e:
        print(f"   ❌ خطأ في بدء الخدمة: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # 5. اختبار مكونات الخدمة
    print("\n5️⃣ اختبار مكونات الخدمة...")
    try:
        # اختبار الاتصال
        connection_ok = auto_reply._check_whatsapp_connection()
        print(f"   📡 اختبار الاتصال: {'نجح' if connection_ok else 'فشل'}")
        
        # اختبار فحص انشغال النظام
        is_busy = await auto_reply._is_system_busy()
        print(f"   ⚙️ فحص انشغال النظام: {'مشغول' if is_busy else 'متاح'}")
        
        # اختبار البحث عن رسائل
        print("   🔍 اختبار البحث عن رسائل...")
        new_messages = await auto_reply._get_new_messages()
        print(f"   📨 عدد الرسائل الجديدة: {len(new_messages)}")
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار المكونات: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n" + "=" * 60)
    print("انتهى التشخيص العميق")

if __name__ == "__main__":
    asyncio.run(debug_auto_reply())
