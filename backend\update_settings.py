#!/usr/bin/env python3
"""
تحديث إعدادات قاعدة البيانات
"""

import sys
import os
sys.path.insert(0, os.getcwd())

from database import SessionLocal
from models import Settings

def update_settings():
    """تحديث الإعدادات الافتراضية"""
    
    print("🔧 تحديث إعدادات قاعدة البيانات...")
    
    db = SessionLocal()
    try:
        # الحصول على الإعدادات الحالية أو إنشاء جديدة
        settings = db.query(Settings).first()
        
        if not settings:
            print("📝 إنشاء إعدادات جديدة...")
            settings = Settings()
            db.add(settings)
        else:
            print("🔄 تحديث الإعدادات الموجودة...")
        
        # تحديث التأخير الافتراضي إلى 40 ثانية
        if settings.response_delay < 40:
            settings.response_delay = 40
            print(f"⏰ تم تحديث تأخير الرد إلى 40 ثانية")
        
        # إضافة prompt افتراضي إذا لم يكن موجود
        if not settings.gpt_prompt:
            default_prompt = """أنت مساعد ذكي لخدمة العملاء في شركة {company_name}.

مهامك:
- الرد على استفسارات العملاء بطريقة مهذبة ومفيدة
- تقديم المساعدة والدعم الفني
- توجيه العملاء للحلول المناسبة
- الحفاظ على طابع ودود ومهني

إرشادات الرد:
- استخدم اللغة العربية
- كن مختصراً ومفيداً
- اذكر اسم الشركة عند الحاجة
- إذا لم تعرف الإجابة، وجه العميل للتواصل مع الدعم الفني

معلومات الشركة:
- اسم الشركة: {company_name}
- هاتف الدعم: {support_phone}
- بريد الدعم: {support_email}"""
            
            settings.gpt_prompt = default_prompt
            print(f"📝 تم إضافة prompt افتراضي للذكاء الاصطناعي")
        
        # حفظ التغييرات
        db.commit()
        print("✅ تم تحديث الإعدادات بنجاح!")
        
        # عرض الإعدادات الحالية
        print(f"\n📊 الإعدادات الحالية:")
        print(f"   ⏰ تأخير الرد: {settings.response_delay} ثانية")
        print(f"   🤖 نموذج GPT: {settings.gpt_model}")
        print(f"   📝 Prompt موجود: {'نعم' if settings.gpt_prompt else 'لا'}")
        print(f"   🏢 اسم الشركة: {settings.company_name}")
        
    except Exception as e:
        print(f"❌ خطأ في تحديث الإعدادات: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    update_settings()
