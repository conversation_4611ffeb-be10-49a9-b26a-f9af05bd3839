#!/usr/bin/env python3
"""
تشخيص حالة WhatsApp Web للحملات
"""

import sys
import os
import asyncio

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.getcwd())

from whatsapp_selenium import whatsapp_manager
from services.whatsapp_web_service import WhatsAppWebService
from services.campaign_service import CampaignService
from database import SessionLocal

def check_whatsapp_status():
    """فحص حالة WhatsApp Web"""
    
    print("🔍 فحص حالة WhatsApp Web...")
    print("=" * 50)
    
    try:
        status = whatsapp_manager.get_status()
        
        print(f"📊 حالة الجلسة: {'نشطة' if status.get('session_active', False) else 'غير نشطة'}")
        print(f"🔐 حالة تسجيل الدخول: {'مسجل دخول' if status.get('logged_in', False) else 'غير مسجل دخول'}")
        print(f"👁️ وضع الخلفية: {'مفعل' if status.get('headless', False) else 'معطل'}")
        print(f"💬 الرسالة: {status.get('message', 'غير متوفرة')}")
        
        return status
        
    except Exception as e:
        print(f"❌ خطأ في فحص الحالة: {e}")
        return None

def check_whatsapp_service():
    """فحص خدمة WhatsApp Web"""
    
    print("\n🔧 فحص خدمة WhatsApp Web...")
    print("=" * 50)
    
    try:
        # إنشاء خدمة WhatsApp
        whatsapp_service = WhatsAppWebService(whatsapp_manager)
        
        print("✅ تم إنشاء WhatsAppWebService بنجاح")
        
        # فحص الاتصال
        if whatsapp_service._ensure_logged_in():
            print("✅ WhatsApp Web متصل ومُسجل دخول")
            return True
        else:
            print("❌ WhatsApp Web غير متصل أو غير مُسجل دخول")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في فحص خدمة WhatsApp: {e}")
        return False

def check_campaign_service():
    """فحص خدمة الحملات"""
    
    print("\n📢 فحص خدمة الحملات...")
    print("=" * 50)
    
    try:
        db = SessionLocal()
        campaign_service = CampaignService(db)
        
        print("✅ تم إنشاء CampaignService بنجاح")
        
        if campaign_service.whatsapp_service:
            print("✅ WhatsAppWebService مُرتبط بـ CampaignService")
        else:
            print("❌ WhatsAppWebService غير مُرتبط بـ CampaignService")
            
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص خدمة الحملات: {e}")
        return False

async def test_whatsapp_connection():
    """اختبار الاتصال مع WhatsApp Web"""
    
    print("\n🧪 اختبار الاتصال مع WhatsApp Web...")
    print("=" * 50)
    
    try:
        db = SessionLocal()
        campaign_service = CampaignService(db)
        
        # اختبار الاتصال
        result = await campaign_service.test_whatsapp_connection()
        
        print(f"📊 نتيجة الاختبار: {result.get('status', 'غير معروف')}")
        print(f"💬 الرسالة: {result.get('message', 'غير متوفرة')}")
        
        db.close()
        return result.get('status') == 'success'
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الاتصال: {e}")
        return False

def provide_solutions(status):
    """تقديم حلول للمشاكل"""
    
    print("\n💡 الحلول المقترحة:")
    print("=" * 50)
    
    if not status:
        print("❌ لا يمكن الحصول على حالة WhatsApp Web")
        print("🔧 الحلول:")
        print("   1. تأكد من تشغيل الباك إند")
        print("   2. تحقق من تثبيت Selenium و ChromeDriver")
        print("   3. أعد تشغيل الخادم")
        return
    
    if not status.get('session_active', False):
        print("❌ جلسة WhatsApp Web غير نشطة")
        print("🔧 الحلول:")
        print("   1. اذهب إلى الإعدادات: http://localhost:5173/settings")
        print("   2. انتقل لقسم 'WhatsApp Web'")
        print("   3. اضغط 'بدء جلسة جديدة'")
        print("   4. امسح QR Code بهاتفك")
        
    elif not status.get('logged_in', False):
        print("❌ WhatsApp Web غير مُسجل دخول")
        print("🔧 الحلول:")
        print("   1. اذهب إلى الإعدادات: http://localhost:5173/settings")
        print("   2. انتقل لقسم 'WhatsApp Web'")
        print("   3. اضغط 'تسجيل دخول'")
        print("   4. امسح QR Code بهاتفك")
        print("   5. انتظر رسالة 'تم تسجيل الدخول بنجاح'")
        
    else:
        print("✅ WhatsApp Web يعمل بشكل صحيح!")
        print("🚀 يمكنك الآن إرسال الحملات")
        print("💡 نصائح:")
        print("   - تأكد من عدم إغلاق متصفح Chrome")
        print("   - تجنب استخدام WhatsApp على الهاتف أثناء الإرسال")
        print("   - احتفظ بالهاتف متصلاً بالإنترنت")

def main():
    """الدالة الرئيسية"""
    
    print("🔍 تشخيص حالة WhatsApp Web للحملات")
    print("=" * 60)
    
    # فحص حالة WhatsApp Web
    status = check_whatsapp_status()
    
    # فحص خدمة WhatsApp Web
    service_ok = check_whatsapp_service()
    
    # فحص خدمة الحملات
    campaign_ok = check_campaign_service()
    
    # اختبار الاتصال
    print("\n🧪 اختبار الاتصال...")
    try:
        connection_ok = asyncio.run(test_whatsapp_connection())
    except Exception as e:
        print(f"❌ خطأ في اختبار الاتصال: {e}")
        connection_ok = False
    
    # تقديم الحلول
    provide_solutions(status)
    
    print("\n" + "=" * 60)
    print("📊 ملخص التشخيص:")
    print(f"   🔍 حالة WhatsApp Web: {'✅ جيد' if status and status.get('logged_in') else '❌ يحتاج إصلاح'}")
    print(f"   🔧 خدمة WhatsApp: {'✅ جيد' if service_ok else '❌ يحتاج إصلاح'}")
    print(f"   📢 خدمة الحملات: {'✅ جيد' if campaign_ok else '❌ يحتاج إصلاح'}")
    print(f"   🧪 اختبار الاتصال: {'✅ جيد' if connection_ok else '❌ يحتاج إصلاح'}")
    
    if status and status.get('logged_in') and service_ok and campaign_ok:
        print("\n🎉 كل شيء يعمل بشكل صحيح! يمكنك إرسال الحملات الآن.")
    else:
        print("\n⚠️ هناك مشاكل تحتاج إلى إصلاح قبل إرسال الحملات.")
    
    print("\n🌐 روابط مفيدة:")
    print("   📱 الإعدادات: http://localhost:5173/settings")
    print("   📢 الحملات: http://localhost:5173/campaigns")
    print("   📚 API التوثيق: http://localhost:8000/docs")

if __name__ == "__main__":
    main()
