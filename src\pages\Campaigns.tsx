import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Send,
  Upload,
  Users,
  MessageSquare,
  Clock,
  CheckCircle,
  AlertCircle,
  Plus,
  FileText,
  Image,
  RefreshCw,
  Trash2,
  Eye
} from "lucide-react";
import { useApi, useApiMutation } from "@/hooks/useApi";
import {
  getCampaigns,
  createCampaign,
  sendCampaign,
  deleteCampaign,
  uploadContactsFile,
  getCampaignContacts,
  getCampaignMessages,
  retryFailedMessages,
  getFailedContacts,
  Campaign,
  Contact,
  Message
} from "@/lib/api";

const Campaigns = () => {
  const [selectedTab, setSelectedTab] = useState<"create" | "manage">("create");
  const [campaignName, setCampaignName] = useState("");
  const [messageText, setMessageText] = useState("");

  const [contactsFile, setContactsFile] = useState<File | null>(null);
  const [expandedCampaign, setExpandedCampaign] = useState<number | null>(null);
  const [campaignContacts, setCampaignContacts] = useState<{[key: number]: Contact[]}>({});
  const [campaignMessages, setCampaignMessages] = useState<{[key: number]: Message[]}>({});
  const [failedContacts, setFailedContacts] = useState<{[key: number]: any[]}>({});

  // جلب البيانات الحقيقية من الباك إند
  const { data: campaigns, loading, error, refetch } = useApi<Campaign[]>(getCampaigns);

  // تسجيل حالة البيانات للتشخيص
  console.log('Campaigns data:', { campaigns, loading, error });

  // تحديث البيانات عند التبديل إلى تبويب إدارة الحملات (مرة واحدة فقط)
  useEffect(() => {
    if (selectedTab === "manage") {
      console.log('Switching to manage tab, refetching data...');
      refetch();
    }
  }, [selectedTab]); // إزالة refetch من dependencies لتجنب التحديث المستمر

  // العمليات
  const { mutate: createCampaignMutation, loading: creating } = useApiMutation(createCampaign);
  const { mutate: sendCampaignMutation, loading: sending } = useApiMutation(sendCampaign);
  const { mutate: deleteCampaignMutation, loading: deleting } = useApiMutation(deleteCampaign);
  const { mutate: retryFailedMutation, loading: retryingMutation } = useApiMutation(retryFailedMessages);

  // إنشاء حملة جديدة
  const handleCreateCampaign = async () => {
    if (!campaignName.trim() || !messageText.trim()) {
      alert("يرجى ملء جميع الحقول المطلوبة");
      return;
    }

    const newCampaign = {
      name: campaignName,
      message_text: messageText,
      status: "pending"
    };

    const result = await createCampaignMutation(newCampaign);
    if (result) {
      let uploadSuccess = true;
      let contactsCount = 0;

      // رفع ملف العملاء إذا كان متوفراً
      if (contactsFile) {
        try {
          console.log(`Uploading contacts file for campaign ${result.id}...`);
          const uploadResult = await uploadContactsFile(result.id, contactsFile);
          console.log('Upload result:', uploadResult);

          if (uploadResult && uploadResult.contacts_count) {
            contactsCount = uploadResult.contacts_count;
            console.log(`Successfully uploaded ${contactsCount} contacts`);
          } else {
            console.warn('Upload result missing contacts_count');
          }
        } catch (error) {
          console.error("خطأ في رفع ملف العملاء:", error);
          uploadSuccess = false;
          alert(`⚠️ تم إنشاء الحملة لكن فشل في رفع ملف العملاء: ${error}`);
        }
      }

      // إعادة تعيين النموذج
      setCampaignName("");
      setMessageText("");
      setContactsFile(null);
      refetch();

      // رسالة نجاح مع تفاصيل
      if (contactsFile && uploadSuccess) {
        alert(`✅ تم إنشاء الحملة بنجاح!\n📞 تم رفع ${contactsCount} جهة اتصال\n🚀 يمكنك الآن إرسال الحملة`);
      } else if (contactsFile && !uploadSuccess) {
        alert(`⚠️ تم إنشاء الحملة لكن فشل رفع ملف العملاء\n💡 يرجى رفع الملف مرة أخرى`);
      } else {
        alert("✅ تم إنشاء الحملة بنجاح!\n💡 لا تنس رفع ملف العملاء قبل الإرسال");
      }
    }
  };

  // إرسال حملة
  const handleSendCampaign = async (campaignId: number) => {
    if (confirm("هل أنت متأكد من إرسال هذه الحملة؟")) {
      try {
        const result = await sendCampaignMutation(campaignId);
        if (result) {
          refetch();
          alert(`✅ تم بدء إرسال الحملة!\n📊 إجمالي جهات الاتصال: ${result.total_contacts}\n💡 استخدم زر "تحديث" لمتابعة التقدم`);
        }
      } catch (error) {
        alert(`❌ خطأ في إرسال الحملة: ${error}`);
      }
    }
  };

  // حذف حملة
  const handleDeleteCampaign = async (campaignId: number) => {
    if (confirm("هل أنت متأكد من حذف هذه الحملة؟ لا يمكن التراجع عن هذا الإجراء.")) {
      const result = await deleteCampaignMutation(campaignId);
      if (result !== null) {
        refetch();
        alert("تم حذف الحملة بنجاح!");
      }
    }
  };

  // إعادة إرسال الرسائل الفاشلة
  const handleRetryFailedMessages = async (campaignId: number) => {
    if (confirm("هل تريد إعادة إرسال الرسائل الفاشلة لهذه الحملة؟")) {
      try {
        const result = await retryFailedMutation(campaignId);
        if (result) {
          // إعادة تحميل البيانات
          refetch();
          // مسح البيانات المحفوظة محلياً لإعادة تحميلها
          setFailedContacts(prev => ({...prev, [campaignId]: []}));
          setCampaignMessages(prev => ({...prev, [campaignId]: []}));

          alert(`✅ تم بدء إعادة الإرسال!\n📊 عدد الرسائل الفاشلة: ${result.failed_messages_count}\n💡 استخدم زر "تحديث" لمتابعة التقدم`);
        }
      } catch (error) {
        alert(`❌ خطأ في إعادة الإرسال: ${error}`);
      }
    }
  };

  // توسيع/طي تفاصيل الحملة
  const toggleCampaignDetails = async (campaignId: number) => {
    if (expandedCampaign === campaignId) {
      setExpandedCampaign(null);
    } else {
      setExpandedCampaign(campaignId);

      // جلب جهات الاتصال والرسائل إذا لم تكن محملة
      if (!campaignContacts[campaignId]) {
        try {
          const contacts = await getCampaignContacts(campaignId);
          setCampaignContacts(prev => ({...prev, [campaignId]: contacts}));
        } catch (error) {
          console.error("خطأ في جلب جهات الاتصال:", error);
        }
      }

      if (!campaignMessages[campaignId]) {
        try {
          const messages = await getCampaignMessages(campaignId);
          setCampaignMessages(prev => ({...prev, [campaignId]: messages}));
        } catch (error) {
          console.error("خطأ في جلب الرسائل:", error);
        }
      }

      // جلب جهات الاتصال الفاشلة إذا لم تكن محملة
      if (!failedContacts[campaignId]) {
        try {
          const failed = await getFailedContacts(campaignId);
          setFailedContacts(prev => ({...prev, [campaignId]: failed.failed_contacts || []}));
        } catch (error) {
          console.error("خطأ في جلب جهات الاتصال الفاشلة:", error);
        }
      }
    }
  };

  // رفع ملف عملاء لحملة موجودة
  const handleUploadContacts = async (campaignId: number, file: File) => {
    try {
      console.log(`Uploading contacts for campaign ${campaignId}...`);
      const result = await uploadContactsFile(campaignId, file);
      console.log('Upload result:', result);

      if (result && result.contacts_count) {
        // تحديث جهات الاتصال في الحالة المحلية
        const contacts = await getCampaignContacts(campaignId);
        setCampaignContacts(prev => ({...prev, [campaignId]: contacts}));

        // تحديث قائمة الحملات
        refetch();

        alert(`✅ تم رفع ${result.contacts_count} جهة اتصال بنجاح!`);
      } else {
        alert("⚠️ تم رفع الملف لكن لم يتم العثور على جهات اتصال صالحة");
      }
    } catch (error) {
      console.error("خطأ في رفع ملف العملاء:", error);
      alert(`❌ فشل في رفع ملف العملاء: ${error}`);
    }
  };



  return (
    <div className="min-h-screen bg-background">
      <div className="max-w-7xl mx-auto p-6">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-foreground mb-2">
            إدارة الحملات الإعلانية
          </h1>
          <p className="text-muted-foreground text-lg">
            أنشئ وأرسل حملات ترويجية لعملائك بسهولة
          </p>
        </div>

        {/* Tabs */}
        <div className="mb-6">
          <div className="flex gap-2">
            <Button
              variant={selectedTab === "create" ? "default" : "ghost"}
              onClick={() => setSelectedTab("create")}
              className="gap-2"
            >
              <Plus className="h-4 w-4" />
              إنشاء حملة جديدة
            </Button>
            <Button
              variant={selectedTab === "manage" ? "default" : "ghost"}
              onClick={() => {
                console.log('Switching to manage tab...');
                setSelectedTab("manage");
              }}
              className="gap-2"
            >
              <FileText className="h-4 w-4" />
              إدارة الحملات
            </Button>
          </div>
        </div>

        {selectedTab === "create" ? (
          /* Create Campaign Form */
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2">
              <Card className="p-6">
                <h2 className="text-xl font-semibold text-foreground mb-6">
                  تفاصيل الحملة
                </h2>
                
                <div className="space-y-6">
                  {/* Campaign Name */}
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      اسم الحملة
                    </label>
                    <Input
                      placeholder="مثال: حملة العروض الصيفية"
                      value={campaignName}
                      onChange={(e) => setCampaignName(e.target.value)}
                    />
                  </div>

                  {/* Message Text */}
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      نص الرسالة
                    </label>
                    <Textarea
                      placeholder="مرحباً {الاسم}، لديك عرض خاص..."
                      value={messageText}
                      onChange={(e) => setMessageText(e.target.value)}
                      rows={6}
                      className="resize-none"
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      يمكنك استخدام متغيرات مثل {"{الاسم}"} للتخصيص
                    </p>
                  </div>

                  {/* File Upload */}
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      ملف العملاء (Excel)
                    </label>
                    <div className="border-2 border-dashed border-border rounded-lg p-6 text-center">
                      <Upload className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <div className="space-y-2">
                        <p className="text-sm text-foreground">
                          اسحب ملف Excel هنا أو 
                        </p>
                        <label className="cursor-pointer">
                          <span className="text-primary hover:text-primary-dark font-medium">
                            اختر ملف
                          </span>
                          <input
                            type="file"
                            accept=".xlsx,.xls,.csv"
                            onChange={(e) => {
                              const file = e.target.files?.[0];
                              if (file) setContactsFile(file);
                            }}
                            className="hidden"
                          />
                        </label>
                      </div>
                      {contactsFile && (
                        <div className="mt-4 p-2 bg-accent rounded-lg">
                          <p className="text-sm text-foreground">
                            ملف محدد: {contactsFile.name}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Image Upload */}
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      صورة مرفقة (اختياري)
                    </label>
                    <div className="border border-border rounded-lg p-4">
                      <label className="cursor-pointer flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground">
                        <Image className="h-4 w-4" />
                        إضافة صورة للرسالة
                        <input type="file" accept="image/*" className="hidden" />
                      </label>
                    </div>
                  </div>

                  {/* Create Button */}
                  <Button
                    onClick={handleCreateCampaign}
                    variant="whatsapp"
                    size="lg"
                    className="w-full"
                    disabled={!campaignName.trim() || !messageText.trim() || creating}
                  >
                    <Plus className="h-5 w-5 mr-2" />
                    {creating ? "جاري الإنشاء..." : "إنشاء الحملة"}
                  </Button>
                </div>
              </Card>
            </div>

            {/* Preview */}
            <div>
              <Card className="p-6">
                <h3 className="text-lg font-semibold text-foreground mb-4">
                  معاينة الرسالة
                </h3>
                <div className="bg-accent/30 rounded-lg p-4 min-h-[200px]">
                  <div className="bg-primary text-primary-foreground rounded-lg p-3 max-w-[80%] ml-auto">
                    {messageText || "اكتب رسالتك لرؤية المعاينة..."}
                  </div>
                </div>
                
                <div className="mt-6 space-y-3">
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Users className="h-4 w-4" />
                    العملاء المحددين: {contactsFile ? "تم التحميل" : "لم يتم التحديد"}
                  </div>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <MessageSquare className="h-4 w-4" />
                    طول الرسالة: {messageText.length} حرف
                  </div>
                </div>
              </Card>
            </div>
          </div>
        ) : (
          /* Manage Campaigns */
          <div className="space-y-6">
            {/* Header with refresh button */}
            <div className="flex justify-between items-center">
              <div>
                <h2 className="text-xl font-semibold">إدارة الحملات</h2>
                <p className="text-sm text-muted-foreground mt-1">
                  {campaigns ? `${campaigns.length} حملة` : 'جاري التحميل...'}
                </p>
              </div>
              <Button
                onClick={() => {
                  console.log('Manual refresh clicked');
                  refetch();
                }}
                variant="outline"
                size="sm"
                disabled={loading}
                className="flex items-center gap-2"
              >
                <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                تحديث
              </Button>
            </div>

            {/* Loading state */}
            {loading && (
              <div className="space-y-4">
                {[1, 2, 3].map((i) => (
                  <Card key={i} className="p-6">
                    <div className="animate-pulse space-y-4">
                      <div className="h-6 bg-muted rounded w-1/3"></div>
                      <div className="h-4 bg-muted rounded w-1/4"></div>
                      <div className="grid grid-cols-4 gap-4">
                        {[1, 2, 3, 4].map((j) => (
                          <div key={j} className="h-16 bg-muted rounded"></div>
                        ))}
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            )}

            {/* Error state */}
            {error && (
              <Card className="p-6 border-destructive/50 bg-destructive/5">
                <div className="flex items-center gap-3">
                  <AlertCircle className="h-5 w-5 text-destructive" />
                  <div>
                    <h3 className="font-semibold text-destructive">خطأ في تحميل الحملات</h3>
                    <p className="text-sm text-muted-foreground mt-1">{error}</p>
                    <div className="mt-3 space-y-2 text-xs text-muted-foreground">
                      <p>💡 حلول مقترحة:</p>
                      <ul className="list-disc list-inside space-y-1">
                        <li>تأكد من تشغيل الباك إند على localhost:8000</li>
                        <li>تحقق من اتصال الإنترنت</li>
                        <li>اضغط "تحديث" لإعادة المحاولة</li>
                        <li>افتح Console (F12) للمزيد من التفاصيل</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </Card>
            )}

            {/* Debug info */}
            {!loading && !error && !campaigns && (
              <Card className="p-6 border-warning/50 bg-warning/5">
                <div className="flex items-center gap-3">
                  <AlertCircle className="h-5 w-5 text-warning" />
                  <div>
                    <h3 className="font-semibold text-warning">مشكلة في تحميل البيانات</h3>
                    <p className="text-sm text-muted-foreground mt-1">
                      البيانات غير محددة (undefined). قد تكون هناك مشكلة في الاتصال بالخادم.
                    </p>
                    <Button
                      onClick={refetch}
                      variant="outline"
                      size="sm"
                      className="mt-3"
                    >
                      <RefreshCw className="h-4 w-4 mr-2" />
                      إعادة المحاولة
                    </Button>
                  </div>
                </div>
              </Card>
            )}

            {/* Campaigns list */}
            {campaigns && campaigns.length === 0 && !loading && (
              <Card className="p-8 text-center">
                <MessageSquare className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-foreground mb-2">لا توجد حملات</h3>
                <p className="text-muted-foreground mb-4">ابدأ بإنشاء حملتك الأولى</p>
                <Button onClick={() => setSelectedTab("create")}>
                  <Plus className="h-4 w-4 mr-2" />
                  إنشاء حملة جديدة
                </Button>
              </Card>
            )}

            {campaigns && campaigns.map((campaign) => (
              <Card key={campaign.id} className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h3 className="text-lg font-semibold text-foreground">
                      {campaign.name}
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      تم الإنشاء في {new Date(campaign.created_at).toLocaleDateString('ar-SA')}
                    </p>
                  </div>
                  <div className="flex items-center gap-2">
                    {campaign.status === "completed" && (
                      <span className="flex items-center gap-1 text-success text-sm">
                        <CheckCircle className="h-4 w-4" />
                        مكتملة
                      </span>
                    )}
                    {campaign.status === "running" && (
                      <span className="flex items-center gap-1 text-warning text-sm">
                        <Clock className="h-4 w-4" />
                        قيد التشغيل
                      </span>
                    )}
                    {campaign.status === "pending" && (
                      <span className="flex items-center gap-1 text-muted-foreground text-sm">
                        <AlertCircle className="h-4 w-4" />
                        في الانتظار
                      </span>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="text-center p-3 bg-accent/30 rounded-lg">
                    <div className="text-2xl font-bold text-foreground">
                      {campaign.sent_count}
                    </div>
                    <div className="text-xs text-muted-foreground">مرسلة</div>
                  </div>
                  <div className="text-center p-3 bg-success/10 rounded-lg">
                    <div className="text-2xl font-bold text-success">
                      {campaign.delivered_count}
                    </div>
                    <div className="text-xs text-muted-foreground">وصلت</div>
                  </div>
                  <div className="text-center p-3 bg-destructive/10 rounded-lg">
                    <div className="text-2xl font-bold text-destructive">
                      {campaign.failed_count}
                    </div>
                    <div className="text-xs text-muted-foreground">فشلت</div>
                  </div>
                  <div className="text-center p-3 bg-primary/10 rounded-lg">
                    <div className="text-2xl font-bold text-primary">
                      {campaign.sent_count > 0 ? Math.round((campaign.delivered_count / campaign.sent_count) * 100) : 0}%
                    </div>
                    <div className="text-xs text-muted-foreground">معدل النجاح</div>
                  </div>
                </div>

                {/* Action buttons */}
                <div className="flex gap-2 mt-4">
                  {campaign.status === "pending" && (
                    <Button
                      onClick={() => handleSendCampaign(campaign.id)}
                      variant="whatsapp"
                      size="sm"
                      disabled={sending}
                    >
                      <Send className="h-4 w-4 mr-2" />
                      {sending ? "جاري الإرسال..." : "إرسال الحملة"}
                    </Button>
                  )}

                  {(campaign.status === "completed" || campaign.status === "failed") && campaign.failed_count > 0 && (
                    <Button
                      onClick={() => handleRetryFailedMessages(campaign.id)}
                      variant="secondary"
                      size="sm"
                      disabled={retryingMutation}
                    >
                      <RefreshCw className="h-4 w-4 mr-2" />
                      {retryingMutation ? "جاري إعادة الإرسال..." : `إعادة إرسال (${campaign.failed_count})`}
                    </Button>
                  )}

                  <Button
                    onClick={() => toggleCampaignDetails(campaign.id)}
                    variant="outline"
                    size="sm"
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    {expandedCampaign === campaign.id ? "إخفاء التفاصيل" : "عرض التفاصيل"}
                  </Button>

                  <Button
                    onClick={() => handleDeleteCampaign(campaign.id)}
                    variant="destructive"
                    size="sm"
                    disabled={deleting}
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    {deleting ? "جاري الحذف..." : "حذف"}
                  </Button>
                </div>

                {/* Campaign Details */}
                {expandedCampaign === campaign.id && (
                  <div className="mt-6 space-y-4 border-t pt-4">
                    {/* Message Text */}
                    <div>
                      <h4 className="font-semibold text-foreground mb-2">نص الرسالة:</h4>
                      <div className="p-3 bg-muted rounded-lg text-sm">
                        {campaign.message_text}
                      </div>
                    </div>

                    {/* Contacts */}
                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-semibold text-foreground">
                          جهات الاتصال ({campaignContacts[campaign.id]?.length || 0}):
                        </h4>

                        {/* Upload contacts button */}
                        {(!campaignContacts[campaign.id] || campaignContacts[campaign.id].length === 0) && (
                          <label className="cursor-pointer">
                            <Button variant="outline" size="sm" asChild>
                              <span>
                                <Upload className="h-4 w-4 mr-2" />
                                رفع ملف العملاء
                              </span>
                            </Button>
                            <input
                              type="file"
                              accept=".xlsx,.xls,.csv"
                              onChange={(e) => {
                                const file = e.target.files?.[0];
                                if (file) {
                                  handleUploadContacts(campaign.id, file);
                                }
                              }}
                              className="hidden"
                            />
                          </label>
                        )}
                      </div>

                      {campaignContacts[campaign.id] && campaignContacts[campaign.id].length > 0 ? (
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                          {campaignContacts[campaign.id].slice(0, 6).map((contact, index) => (
                            <div key={contact.id} className="p-2 bg-accent/30 rounded text-sm">
                              <div className="font-medium">{contact.name}</div>
                              <div className="text-muted-foreground">{contact.phone_number}</div>
                            </div>
                          ))}
                          {campaignContacts[campaign.id].length > 6 && (
                            <div className="p-2 bg-muted rounded text-sm text-center text-muted-foreground">
                              +{campaignContacts[campaign.id].length - 6} أكثر...
                            </div>
                          )}
                        </div>
                      ) : campaignContacts[campaign.id] ? (
                        <div className="text-center p-4 bg-muted/50 rounded-lg">
                          <Users className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                          <p className="text-muted-foreground text-sm">لا توجد جهات اتصال</p>
                          <p className="text-xs text-muted-foreground mt-1">استخدم زر "رفع ملف العملاء" أعلاه</p>
                        </div>
                      ) : (
                        <div className="text-muted-foreground text-sm">جاري تحميل جهات الاتصال...</div>
                      )}
                    </div>

                    {/* Messages Status */}
                    {campaignMessages[campaign.id] && campaignMessages[campaign.id].length > 0 && (
                      <div>
                        <h4 className="font-semibold text-foreground mb-2">حالة الرسائل:</h4>
                        <div className="space-y-1 max-h-40 overflow-y-auto">
                          {campaignMessages[campaign.id].slice(0, 10).map((message, index) => (
                            <div key={message.id} className="flex items-center justify-between p-2 bg-accent/20 rounded text-sm">
                              <span>{message.contact_name || `جهة اتصال ${index + 1}`}</span>
                              <span className={`px-2 py-1 rounded text-xs ${
                                message.status === 'sent' ? 'bg-success/20 text-success' :
                                message.status === 'failed' ? 'bg-destructive/20 text-destructive' :
                                'bg-warning/20 text-warning'
                              }`}>
                                {message.status === 'sent' ? 'تم الإرسال' :
                                 message.status === 'failed' ? 'فشل' : 'في الانتظار'}
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Failed Contacts */}
                    {failedContacts[campaign.id] && failedContacts[campaign.id].length > 0 && (
                      <div>
                        <h4 className="font-semibold text-destructive mb-2">
                          جهات الاتصال الفاشلة ({failedContacts[campaign.id].length}):
                        </h4>
                        <div className="space-y-2 max-h-40 overflow-y-auto">
                          {failedContacts[campaign.id].map((contact, index) => (
                            <div key={index} className="p-2 bg-destructive/10 border border-destructive/20 rounded text-sm">
                              <div className="flex items-center justify-between">
                                <div>
                                  <div className="font-medium text-destructive">{contact.name}</div>
                                  <div className="text-muted-foreground">{contact.phone_number}</div>
                                </div>
                                <AlertCircle className="h-4 w-4 text-destructive" />
                              </div>
                              {contact.error_message && (
                                <div className="mt-1 text-xs text-muted-foreground">
                                  خطأ: {contact.error_message}
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                        <div className="mt-2 text-xs text-muted-foreground">
                          💡 استخدم زر "إعادة إرسال" أعلاه لإعادة المحاولة
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default Campaigns;