#!/usr/bin/env python3
"""
اختبار نظام الردود التلقائية المحسن
"""

import asyncio
import time
from services.auto_reply_service import AutoReplyService
from whatsapp_selenium import whatsapp_manager

async def test_auto_reply_system():
    """اختبار شامل لنظام الردود التلقائية"""
    
    print("🧪 اختبار نظام الردود التلقائية المحسن")
    print("=" * 60)
    
    try:
        # 1. إنشاء خدمة الردود التلقائية
        print("1️⃣ إنشاء خدمة الردود التلقائية...")
        auto_reply = AutoReplyService(whatsapp_manager)
        
        # 2. فحص حالة WhatsApp Web
        print("2️⃣ فحص حالة WhatsApp Web...")
        status = whatsapp_manager.get_status()
        print(f"   📊 حالة الجلسة: {'نشطة' if status.get('session_active') else 'غير نشطة'}")
        print(f"   🔐 تسجيل الدخول: {'مُسجل' if status.get('logged_in') else 'غير مُسجل'}")
        
        if not status.get('session_active') or not status.get('logged_in'):
            print("   ⚠️ يجب تسجيل الدخول لـ WhatsApp Web أولاً")
            return
        
        # 3. اختبار كشف الاتصال
        print("3️⃣ اختبار فحص الاتصال...")
        connection_ok = auto_reply._check_whatsapp_connection()
        print(f"   📡 حالة الاتصال: {'متصل' if connection_ok else 'منقطع'}")
        
        # 4. اختبار فحص انشغال النظام
        print("4️⃣ اختبار فحص انشغال النظام...")
        is_busy = await auto_reply._is_system_busy()
        print(f"   ⚙️ حالة النظام: {'مشغول' if is_busy else 'متاح'}")
        
        # 5. محاكاة بدء المراقبة
        print("5️⃣ محاكاة بدء المراقبة...")
        print("   🔄 سيتم فحص الرسائل كل 3 ثوانٍ")
        print("   📱 افتح WhatsApp Web وأرسل رسالة لنفسك من هاتف آخر")
        print("   ⏹️ اضغط Ctrl+C لإيقاف الاختبار")
        
        # بدء المراقبة لمدة محدودة للاختبار
        auto_reply.is_running = True
        auto_reply.driver = whatsapp_manager.driver
        
        # محاكاة دورة مراقبة واحدة
        for i in range(5):  # 5 دورات فقط للاختبار
            print(f"\n🔍 دورة مراقبة {i+1}/5...")
            
            try:
                # فحص الرسائل الجديدة
                new_messages = await auto_reply._get_new_messages()
                
                if new_messages:
                    print(f"   📨 تم العثور على {len(new_messages)} رسالة جديدة!")
                    for msg in new_messages:
                        print(f"   👤 من: {msg['contact_name']}")
                        print(f"   📱 الرقم: {msg['phone_number']}")
                        print(f"   💬 الرسالة: {msg['message_text'][:50]}...")
                        print(f"   🕐 الوقت: {msg.get('message_time', 'غير محدد')}")
                else:
                    print("   ✅ لا توجد رسائل جديدة")
                
                # انتظار قبل الدورة التالية
                await asyncio.sleep(3)
                
            except KeyboardInterrupt:
                print("\n⏹️ تم إيقاف الاختبار بواسطة المستخدم")
                break
            except Exception as e:
                print(f"   ❌ خطأ في دورة المراقبة: {e}")
        
        auto_reply.is_running = False
        
        print(f"\n" + "=" * 60)
        print("✅ انتهى اختبار نظام الردود التلقائية")
        
        # 6. ملخص الميزات
        print("\n🎯 ميزات النظام المحسن:")
        print("   ✅ كشف ذكي للرسائل الواردة فقط")
        print("   ✅ تجنب الرد على رسائلك المرسلة")
        print("   ✅ تأخير ذكي وعشوائي (30-90 ثانية)")
        print("   ✅ تجنب التعارض مع الحملات النشطة")
        print("   ✅ مراقبة مستمرة كل 3 ثوانٍ")
        print("   ✅ معالجة متسلسلة لتجنب التداخل")
        print("   ✅ حفظ تلقائي في قاعدة البيانات")
        print("   ✅ إعادة الاتصال التلقائي عند الانقطاع")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النظام: {e}")
        import traceback
        traceback.print_exc()

def test_message_detection():
    """اختبار كشف الرسائل بدون تشغيل المراقبة الكاملة"""
    
    print("\n🔍 اختبار كشف الرسائل...")
    print("-" * 40)
    
    try:
        auto_reply = AutoReplyService(whatsapp_manager)
        auto_reply.driver = whatsapp_manager.driver
        
        # فحص الاتصال
        if not auto_reply._check_whatsapp_connection():
            print("❌ لا يوجد اتصال مع WhatsApp Web")
            return
        
        print("✅ متصل مع WhatsApp Web")
        print("📱 افتح دردشة تحتوي على رسائل واردة...")
        
        # محاولة قراءة الرسائل من الدردشة المفتوحة
        messages = asyncio.run(auto_reply._read_chat_messages())
        
        if messages:
            print(f"📨 تم العثور على {len(messages)} رسالة:")
            for msg in messages:
                print(f"   👤 {msg['contact_name']}: {msg['message_text'][:50]}...")
        else:
            print("ℹ️ لا توجد رسائل جديدة في الدردشة المفتوحة")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار كشف الرسائل: {e}")

if __name__ == "__main__":
    print("🚀 بدء اختبار نظام الردود التلقائية")
    print("=" * 60)
    
    # اختبار كشف الرسائل أولاً
    test_message_detection()
    
    # اختبار النظام الكامل
    asyncio.run(test_auto_reply_system())
