#!/usr/bin/env python3
"""
اختبار CORS لـ analytics endpoints
"""

import requests
import json

def test_analytics_cors():
    """اختبار CORS لـ analytics"""
    
    print("🔍 اختبار CORS لـ Analytics...")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    origin = "http://localhost:5173"
    
    endpoints = [
        "/api/analytics/messages/timeline?days=30",
        "/api/analytics/dashboard",
        "/api/analytics/campaigns/performance"
    ]
    
    for endpoint in endpoints:
        print(f"\n📊 اختبار: {endpoint}")
        print("-" * 30)
        
        try:
            # اختبار GET request مع Origin header
            headers = {
                'Origin': origin,
                'User-Agent': 'Mozilla/5.0 (Test)'
            }
            
            response = requests.get(f"{base_url}{endpoint}", headers=headers)
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                print("✅ Request نجح")
                
                # فحص CORS headers
                cors_headers = {
                    'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
                    'Access-Control-Allow-Credentials': response.headers.get('Access-Control-Allow-Credentials'),
                    'Access-Control-Expose-Headers': response.headers.get('Access-Control-Expose-Headers'),
                }
                
                print("CORS Headers:")
                for header, value in cors_headers.items():
                    if value:
                        print(f"  ✅ {header}: {value}")
                    else:
                        print(f"  ❌ {header}: مفقود")
                
                # عرض البيانات
                try:
                    data = response.json()
                    if isinstance(data, dict):
                        print(f"📊 البيانات: {list(data.keys())}")
                    else:
                        print(f"📊 نوع البيانات: {type(data)}")
                except:
                    print("📊 البيانات: غير JSON")
                    
            else:
                print(f"❌ Request فشل: {response.text[:100]}")
                
        except requests.exceptions.ConnectionError:
            print("❌ لا يمكن الاتصال بالخادم")
            break
        except Exception as e:
            print(f"❌ خطأ: {e}")
    
    print("\n" + "=" * 50)
    print("انتهى اختبار Analytics CORS")

if __name__ == "__main__":
    test_analytics_cors()
