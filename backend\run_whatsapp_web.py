"""
تشغيل الخادم مع دعم WhatsApp Web
"""

import sys
import os
import uvicorn

# إضافة المجلد الحالي إلى مسار Python
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

if __name__ == "__main__":
    print("🚀 بدء تشغيل خادم WhatsApp Web...")
    print("📍 الباك إند: http://localhost:8000")
    print("📍 API التوثيق: http://localhost:8000/docs")
    print("📍 الفرونت إند: http://localhost:5173")
    print()
    
    try:
        uvicorn.run(
            "main:app",
            host="0.0.0.0",
            port=8000,
            reload=True,
            reload_dirs=[current_dir],
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")
        input("اضغط Enter للخروج...")
