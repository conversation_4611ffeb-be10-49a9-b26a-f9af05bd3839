#!/usr/bin/env python3
"""
اختبار endpoint إرسال الحملة مباشرة
"""

import requests
import json

def test_send_endpoint():
    """اختبار endpoint إرسال الحملة"""
    
    print("🧪 اختبار endpoint إرسال الحملة")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    campaign_id = 1  # الحملة رقم 1
    
    try:
        # 1. فحص تفاصيل الحملة أولاً
        print(f"📋 فحص تفاصيل الحملة {campaign_id}...")
        response = requests.get(f"{base_url}/api/campaigns/{campaign_id}")
        
        if response.status_code == 200:
            campaign = response.json()
            print(f"   ✅ الحملة موجودة:")
            print(f"   📝 الاسم: {campaign.get('name')}")
            print(f"   📊 الحالة: {campaign.get('status')}")
            print(f"   👥 عدد جهات الاتصال: {campaign.get('total_contacts')}")
        else:
            print(f"   ❌ خطأ في جلب الحملة: {response.status_code}")
            print(f"   📄 الرد: {response.text}")
            return
        
        # 2. فحص جهات الاتصال
        print(f"\n👥 فحص جهات اتصال الحملة...")
        response = requests.get(f"{base_url}/api/campaigns/{campaign_id}/contacts")
        
        if response.status_code == 200:
            contacts = response.json()
            print(f"   ✅ عدد جهات الاتصال: {len(contacts)}")
            for i, contact in enumerate(contacts[:3], 1):
                print(f"   {i}. {contact.get('name')} - {contact.get('phone_number')}")
        else:
            print(f"   ❌ خطأ في جلب جهات الاتصال: {response.status_code}")
        
        # 3. اختبار endpoint الإرسال
        print(f"\n🚀 اختبار endpoint الإرسال...")
        
        # إعداد headers
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
        
        # إرسال طلب POST
        send_url = f"{base_url}/api/campaigns/{campaign_id}/send"
        print(f"📡 إرسال POST إلى: {send_url}")
        
        response = requests.post(send_url, headers=headers)
        
        print(f"\n📊 نتيجة الطلب:")
        print(f"   📈 كود الاستجابة: {response.status_code}")
        print(f"   📄 Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"   ✅ نجح الطلب!")
                print(f"   📝 الرسالة: {result.get('message')}")
                print(f"   📊 معرف الحملة: {result.get('campaign_id')}")
                print(f"   👥 عدد جهات الاتصال: {result.get('total_contacts')}")
            except json.JSONDecodeError:
                print(f"   ⚠️ الرد ليس JSON صحيح: {response.text}")
        else:
            print(f"   ❌ فشل الطلب!")
            try:
                error_data = response.json()
                print(f"   📝 تفاصيل الخطأ: {error_data}")
            except:
                print(f"   📄 نص الخطأ: {response.text}")
        
        # 4. اختبار طرق HTTP أخرى للتأكد
        print(f"\n🔍 اختبار طرق HTTP أخرى...")
        
        methods = ['GET', 'PUT', 'DELETE', 'PATCH']
        for method in methods:
            try:
                test_response = requests.request(method, send_url, headers=headers)
                print(f"   {method}: {test_response.status_code}")
            except Exception as e:
                print(f"   {method}: خطأ - {e}")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
    
    print("\n" + "=" * 50)
    print("انتهى اختبار endpoint")

if __name__ == "__main__":
    test_send_endpoint()
