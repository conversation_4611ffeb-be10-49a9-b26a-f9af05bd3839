📊 تعليمات استيراد ملف العملاء
========================================

🎯 الهدف:
استيراد قائمة العملاء لإرسال حملات WhatsApp

📋 الأعمدة المطلوبة:
1. الاسم (مطلوب) - اسم العميل كاملاً
2. رقم الهاتف (مطلوب) - رقم الهاتف مع رمز الدولة
3. البريد الإلكتروني (اختياري) - عنوان البريد الإلكتروني
4. الشركة (اختياري) - اسم الشركة أو المؤسسة
5. المدينة (اختياري) - المدينة أو المنطقة
6. ملاحظات (اختياري) - أي ملاحظات إضافية

📱 تنسيق أرقام الهواتف:
✅ الأشكال الصحيحة:
   +966501234567 (مع رمز الدولة والعلامة +)
   966501234567 (مع رمز الدولة بدون علامة +)
   0501234567 (رقم محلي - سيتم إضافة 966 تلقائياً)

❌ الأشكال الخاطئة:
   501234567 (بدون 0 أو رمز الدولة)
   +1234567890 (رمز دولة خاطئ)
   966-50-123-4567 (يحتوي على شرطات)

🔧 خطوات الاستخدام:
1. افتح ملف "نموذج_استيراد_العملاء.csv"
2. احذف البيانات النموذجية
3. أدخل بيانات عملائك الحقيقية
4. تأكد من ملء عمود "الاسم" و "رقم الهاتف"
5. احفظ الملف
6. ارفعه في صفحة الحملات

💡 نصائح مهمة:
- تأكد من صحة أرقام الهواتف
- استخدم أرقام هواتف سعودية فقط
- تجنب الأرقام المكررة
- املأ عمود الاسم لجميع الصفوف
- يمكن ترك الأعمدة الاختيارية فارغة

📊 أمثلة على البيانات:

مثال 1 - عميل كامل البيانات:
الاسم: أحمد محمد السعيد
رقم الهاتف: +966501234567
البريد الإلكتروني: <EMAIL>
الشركة: شركة التقنية
المدينة: الرياض
ملاحظات: عميل VIP

مثال 2 - عميل بيانات أساسية:
الاسم: فاطمة أحمد
رقم الهاتف: 0502345678
البريد الإلكتروني: (فارغ)
الشركة: (فارغ)
المدينة: جدة
ملاحظات: (فارغ)

⚠️ أخطاء شائعة:
- ترك عمود الاسم فارغ
- استخدام تنسيق خاطئ للأرقام
- إدخال أرقام غير سعودية
- وجود صفوف فارغة في المنتصف

🔍 فحص الملف قبل الاستيراد:
□ عمود "الاسم" مملوء لجميع الصفوف
□ عمود "رقم الهاتف" مملوء وبتنسيق صحيح
□ لا توجد صفوف فارغة في المنتصف
□ أرقام الهواتف سعودية صحيحة
□ لا توجد أرقام مكررة

📞 للدعم الفني:
إذا واجهت أي مشاكل، تحقق من:
1. تنسيق الملف
2. صحة البيانات
3. ترميز الملف (UTF-8)

🎉 النتيجة المتوقعة:
بعد اتباع هذه التعليمات، ستحصل على:
- ملف منسق بشكل صحيح
- بيانات عملاء صحيحة
- استيراد ناجح بدون أخطاء
- جاهز لإرسال الحملات

تاريخ الإنشاء: 2024-12-19
الإصدار: 1.0
