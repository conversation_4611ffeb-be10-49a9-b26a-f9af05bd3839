"""
اختبار تشغيل الخادم
"""

import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.getcwd())

def test_imports():
    """اختبار الاستيرادات"""
    
    print("📦 اختبار الاستيرادات...")
    print("=" * 50)
    
    try:
        # اختبار FastAPI
        from fastapi import FastAPI
        print("✅ FastAPI")
        
        # اختبار CORS
        from fastapi.middleware.cors import CORSMiddleware
        print("✅ CORSMiddleware")
        
        # اختبار SQLAlchemy
        from sqlalchemy.orm import Session
        print("✅ SQLAlchemy")
        
        # اختبار uvicorn
        import uvicorn
        print("✅ uvicorn")
        
        # اختبار pandas
        try:
            import pandas as pd
            print("✅ pandas")
        except ImportError:
            print("⚠️ pandas غير متوفر")
        
        # اختبار models
        from models import Campaign, Contact, Message
        print("✅ Models")
        
        # اختبار database
        from database import get_db, init_db
        print("✅ Database")
        
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def test_app_creation():
    """اختبار إنشاء التطبيق"""
    
    print("\n🚀 اختبار إنشاء التطبيق...")
    print("=" * 50)
    
    try:
        # استيراد main
        import main
        print("✅ تم استيراد main.py")
        
        # فحص app
        if hasattr(main, 'app'):
            app = main.app
            print("✅ FastAPI app موجود")
            
            # فحص middleware
            middlewares = [str(type(m)) for m in app.user_middleware]
            print(f"📋 Middlewares: {len(middlewares)}")
            
            for i, middleware in enumerate(middlewares):
                if 'CORS' in middleware:
                    print(f"  {i+1}. ✅ CORS Middleware")
                else:
                    print(f"  {i+1}. {middleware}")
            
            # فحص routes
            routes = [route.path for route in app.routes]
            print(f"📋 Routes: {len(routes)}")
            
            api_routes = [route for route in routes if route.startswith('/api')]
            print(f"📋 API Routes: {len(api_routes)}")
            
            return True
        else:
            print("❌ FastAPI app غير موجود")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء التطبيق: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database():
    """اختبار قاعدة البيانات"""
    
    print("\n💾 اختبار قاعدة البيانات...")
    print("=" * 50)
    
    try:
        from database import get_db, init_db
        from models import Campaign
        
        # تهيئة قاعدة البيانات
        init_db()
        print("✅ تم تهيئة قاعدة البيانات")
        
        # اختبار الاتصال
        db = next(get_db())
        campaigns = db.query(Campaign).count()
        print(f"✅ عدد الحملات: {campaigns}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False

def run_all_tests():
    """تشغيل جميع الاختبارات"""
    
    print("🔧 اختبار شامل للخادم")
    print("=" * 60)
    
    tests = [
        ("اختبار الاستيرادات", test_imports),
        ("اختبار إنشاء التطبيق", test_app_creation),
        ("اختبار قاعدة البيانات", test_database)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ خطأ في {test_name}: {e}")
            results.append((test_name, False))
    
    print("\n📊 نتائج الاختبار:")
    print("=" * 60)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"  {test_name}: {status}")
        if not result:
            all_passed = False
    
    print(f"\n🎯 النتيجة النهائية:")
    if all_passed:
        print("✅ جميع الاختبارات نجحت!")
        print("🚀 الخادم جاهز للتشغيل")
        print("💡 شغل الخادم بـ: python main.py")
    else:
        print("❌ بعض الاختبارات فشلت")
        print("💡 راجع الأخطاء أعلاه")
    
    return all_passed

if __name__ == "__main__":
    print("🔧 اختبار تشغيل الخادم...")
    print("=" * 60)
    
    # تشغيل الاختبارات
    result = run_all_tests()
    
    if result:
        print("\n🎉 الخادم جاهز!")
        print("🚀 لتشغيل الخادم:")
        print("   python main.py")
        print("\n💡 بعد تشغيل الخادم:")
        print("   - اذهب للفرونت إند")
        print("   - جرب رفع ملف العملاء")
        print("   - يجب أن يعمل بدون أخطاء")
    else:
        print("\n⚠️ هناك مشاكل في الخادم")
        print("💡 راجع الأخطاء وأصلحها")
    
    print("=" * 60)
    input("اضغط Enter للخروج...")
