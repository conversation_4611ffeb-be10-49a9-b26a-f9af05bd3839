#!/usr/bin/env python3
"""
اختبار سريع لحالة النظام
"""

import requests
import json

def test_system():
    """اختبار سريع للنظام"""
    
    base_url = "http://localhost:8000"
    
    print("🔍 اختبار سريع للنظام...")
    print("=" * 40)
    
    # 1. فحص حالة WhatsApp Web
    try:
        response = requests.get(f"{base_url}/api/whatsapp/status")
        if response.status_code == 200:
            data = response.json()
            print(f"📱 WhatsApp Web:")
            print(f"   الجلسة: {'نشطة' if data.get('session_active') else 'غير نشطة'}")
            print(f"   تسجيل الدخول: {'مُسجل' if data.get('logged_in') else 'غير مُسجل'}")
            
            if data.get('session_active') and data.get('logged_in'):
                whatsapp_ok = True
            else:
                whatsapp_ok = False
        else:
            print(f"❌ خطأ في فحص WhatsApp: {response.status_code}")
            whatsapp_ok = False
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
        whatsapp_ok = False
    
    # 2. فحص حالة الردود التلقائية
    try:
        response = requests.get(f"{base_url}/api/whatsapp/auto-reply/status")
        if response.status_code == 200:
            data = response.json()
            print(f"\n🤖 الردود التلقائية:")
            print(f"   الحالة: {'تعمل' if data.get('is_running') else 'متوقفة'}")
            auto_reply_running = data.get('is_running', False)
        else:
            print(f"❌ خطأ في فحص الردود التلقائية: {response.status_code}")
            auto_reply_running = False
    except Exception as e:
        print(f"❌ خطأ في فحص الردود التلقائية: {e}")
        auto_reply_running = False
    
    # 3. فحص الإعدادات
    try:
        response = requests.get(f"{base_url}/api/ai-responses/settings/current")
        if response.status_code == 200:
            data = response.json()
            print(f"\n⚙️ الإعدادات:")
            print(f"   مفتاح OpenAI: {'موجود' if data.get('openai_api_key') else 'غير موجود'}")
            print(f"   الرد التلقائي: {'مفعل' if data.get('auto_reply_enabled') else 'معطل'}")
            print(f"   نموذج GPT: {data.get('gpt_model', 'غير محدد')}")
            settings_ok = bool(data.get('openai_api_key')) and data.get('auto_reply_enabled')
        else:
            print(f"❌ خطأ في فحص الإعدادات: {response.status_code}")
            settings_ok = False
    except Exception as e:
        print(f"❌ خطأ في فحص الإعدادات: {e}")
        settings_ok = False
    
    # 4. محاولة بدء الردود التلقائية إذا لم تكن تعمل
    if whatsapp_ok and settings_ok and not auto_reply_running:
        print(f"\n🚀 محاولة بدء الردود التلقائية...")
        try:
            response = requests.post(f"{base_url}/api/whatsapp/auto-reply/start")
            if response.status_code == 200:
                print("✅ تم بدء الردود التلقائية بنجاح")
                auto_reply_running = True
            else:
                error_data = response.json() if response.headers.get('content-type') == 'application/json' else response.text
                print(f"❌ فشل في بدء الردود التلقائية: {error_data}")
        except Exception as e:
            print(f"❌ خطأ في بدء الردود التلقائية: {e}")
    
    # النتيجة النهائية
    print(f"\n" + "=" * 40)
    print("📋 ملخص الحالة:")
    print(f"   📱 WhatsApp Web: {'✅' if whatsapp_ok else '❌'}")
    print(f"   ⚙️ الإعدادات: {'✅' if settings_ok else '❌'}")
    print(f"   🤖 الردود التلقائية: {'✅ تعمل' if auto_reply_running else '❌ متوقفة'}")
    
    if whatsapp_ok and settings_ok and auto_reply_running:
        print(f"\n🎉 النظام جاهز!")
        print("💡 جرب إرسال رسالة لنفسك من هاتف آخر")
        print("⏳ انتظر 30-90 ثانية للحصول على رد")
    else:
        print(f"\n⚠️ النظام غير جاهز!")
        if not whatsapp_ok:
            print("🔧 سجل دخول WhatsApp Web من الإعدادات")
        if not settings_ok:
            print("🔧 تحقق من مفتاح OpenAI وتفعيل الرد التلقائي")
        if not auto_reply_running:
            print("🔧 اضغط 'بدء الخدمة' في الإعدادات")

if __name__ == "__main__":
    test_system()
