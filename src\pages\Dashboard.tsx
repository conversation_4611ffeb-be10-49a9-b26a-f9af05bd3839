import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import {
  MessageSquare,
  Send,
  Bot,
  Users,
  TrendingUp,
  Activity,
  Clock,
  CheckCircle,
  RefreshCw,
  AlertCircle
} from "lucide-react";
import { Link } from "react-router-dom";
import { useApiWithRefresh } from "@/hooks/useApi";
import { getDashboardStats } from "@/lib/api";

const Dashboard = () => {
  // جلب البيانات الحقيقية من الباك إند
  const { data: dashboardData, loading, error, refetch } = useApiWithRefresh(
    getDashboardStats,
    30000 // تحديث كل 30 ثانية
  );

  // إعداد الإحصائيات باستخدام البيانات الحقيقية
  const stats = dashboardData ? [
    {
      title: "إجمالي الرسائل المرسلة",
      value: dashboardData.total_messages_sent.toLocaleString(),
      change: "+12%", // يمكن حسابها لاحقاً من البيانات التاريخية
      icon: Send,
      color: "text-blue-600"
    },
    {
      title: "الردود التلقائية",
      value: dashboardData.auto_replies_count.toLocaleString(),
      change: "+8%",
      icon: Bot,
      color: "text-green-600"
    },
    {
      title: "العملاء النشطين",
      value: dashboardData.active_customers.toLocaleString(),
      change: "+15%",
      icon: Users,
      color: "text-purple-600"
    },
    {
      title: "معدل النجاح",
      value: `${dashboardData.success_rate.toFixed(1)}%`,
      change: "+2%",
      icon: TrendingUp,
      color: "text-orange-600"
    }
  ] : [];

  const quickActions = [
    {
      title: "إنشاء حملة جديدة",
      description: "أرسل رسائل ترويجية لقائمة العملاء",
      icon: Send,
      link: "/campaigns",
      variant: "whatsapp" as const
    },
    {
      title: "إعداد الرد التلقائي",
      description: "خصص ردود الذكاء الاصطناعي",
      icon: Bot,
      link: "/ai-responses",
      variant: "default" as const
    },
    {
      title: "عرض الإحصائيات",
      description: "تابع أداء حملاتك ورسائلك",
      icon: Activity,
      link: "/analytics",
      variant: "secondary" as const
    }
  ];

  // النشاط الأخير من البيانات الحقيقية
  const recentActivity = dashboardData?.recent_activity || [];

  return (
    <div className="min-h-screen bg-background">
      <div className="max-w-7xl mx-auto p-6">
        {/* Header */}
        <div className="mb-8 flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-foreground mb-2">
              مرحباً بك في نظام WhatsApp الذكي
            </h1>
            <p className="text-muted-foreground text-lg">
              إدارة شاملة للحملات الإعلانية والردود التلقائية
            </p>
          </div>
          <Button
            onClick={refetch}
            variant="outline"
            size="sm"
            disabled={loading}
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            تحديث
          </Button>
        </div>

        {/* Error State */}
        {error && (
          <Card className="p-6 mb-8 border-destructive/50 bg-destructive/5">
            <div className="flex items-center gap-3">
              <AlertCircle className="h-5 w-5 text-destructive" />
              <div>
                <h3 className="font-semibold text-destructive">خطأ في تحميل البيانات</h3>
                <p className="text-sm text-muted-foreground mt-1">{error}</p>
              </div>
            </div>
          </Card>
        )}

        {/* Loading State */}
        {loading && !dashboardData && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {[1, 2, 3, 4].map((i) => (
              <Card key={i} className="p-6">
                <div className="animate-pulse">
                  <div className="flex items-center justify-between mb-4">
                    <div className="w-10 h-10 bg-muted rounded-lg"></div>
                    <div className="w-12 h-4 bg-muted rounded"></div>
                  </div>
                  <div className="w-16 h-8 bg-muted rounded mb-2"></div>
                  <div className="w-24 h-4 bg-muted rounded"></div>
                </div>
              </Card>
            ))}
          </div>
        )}

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <Card key={index} className="p-6 hover:shadow-lg transition-all duration-300 border border-border/50">
                <div className="flex items-center justify-between mb-4">
                  <div className={`p-2 rounded-lg bg-accent ${stat.color}`}>
                    <Icon className="h-6 w-6" />
                  </div>
                  <span className="text-sm text-success font-medium">
                    {stat.change}
                  </span>
                </div>
                <h3 className="text-2xl font-bold text-foreground mb-1">
                  {stat.value}
                </h3>
                <p className="text-sm text-muted-foreground">
                  {stat.title}
                </p>
              </Card>
            );
          })}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Quick Actions */}
          <div className="lg:col-span-2">
            <Card className="p-6">
              <h2 className="text-xl font-semibold text-foreground mb-6">
                إجراءات سريعة
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {quickActions.map((action, index) => {
                  const Icon = action.icon;
                  return (
                    <div key={index} className="text-center p-6 rounded-lg border border-border/50 hover:shadow-md transition-all duration-300">
                      <div className="flex justify-center mb-4">
                        <div className="p-3 bg-accent rounded-full">
                          <Icon className="h-8 w-8 text-primary" />
                        </div>
                      </div>
                      <h3 className="font-semibold text-foreground mb-2">
                        {action.title}
                      </h3>
                      <p className="text-sm text-muted-foreground mb-4">
                        {action.description}
                      </p>
                      <Link to={action.link}>
                        <Button variant={action.variant} size="sm" className="w-full">
                          ابدأ الآن
                        </Button>
                      </Link>
                    </div>
                  );
                })}
              </div>
            </Card>
          </div>

          {/* Recent Activity */}
          <div>
            <Card className="p-6">
              <h2 className="text-xl font-semibold text-foreground mb-6">
                النشاط الأخير
              </h2>
              <div className="space-y-4">
                {recentActivity.map((activity, index) => (
                  <div key={index} className="flex items-start gap-3 p-3 rounded-lg bg-accent/30">
                    <div className={`p-1.5 rounded-full ${
                      activity.status === 'success' ? 'bg-success' :
                      activity.status === 'warning' ? 'bg-warning' : 'bg-primary'
                    }`}>
                      <CheckCircle className="h-3 w-3 text-white" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-foreground">
                        {activity.action}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {activity.target}
                      </p>
                      <div className="flex items-center gap-1 mt-1">
                        <Clock className="h-3 w-3 text-muted-foreground" />
                        <span className="text-xs text-muted-foreground">
                          {activity.time}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;