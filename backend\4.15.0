Defaulting to user installation because normal site-packages is not writeable
Requirement already satisfied: selenium in c:\users\<USER>\appdata\roaming\python\python313\site-packages (4.34.2)
Requirement already satisfied: urllib3~=2.5.0 in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from urllib3[socks]~=2.5.0->selenium) (2.5.0)
Requirement already satisfied: trio~=0.30.0 in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from selenium) (0.30.0)
Requirement already satisfied: trio-websocket~=0.12.2 in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from selenium) (0.12.2)
Requirement already satisfied: certifi>=2025.6.15 in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from selenium) (2025.7.14)
Requirement already satisfied: typing_extensions~=4.14.0 in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from selenium) (4.14.1)
Requirement already satisfied: websocket-client~=1.8.0 in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from selenium) (1.8.0)
Requirement already satisfied: attrs>=23.2.0 in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from trio~=0.30.0->selenium) (25.3.0)
Requirement already satisfied: sortedcontainers in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from trio~=0.30.0->selenium) (2.4.0)
Requirement already satisfied: idna in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from trio~=0.30.0->selenium) (3.10)
Requirement already satisfied: outcome in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from trio~=0.30.0->selenium) (1.3.0.post0)
Requirement already satisfied: sniffio>=1.3.0 in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from trio~=0.30.0->selenium) (1.3.1)
Requirement already satisfied: cffi>=1.14 in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from trio~=0.30.0->selenium) (1.17.1)
Requirement already satisfied: wsproto>=0.14 in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from trio-websocket~=0.12.2->selenium) (1.2.0)
Requirement already satisfied: pysocks!=1.5.7,<2.0,>=1.5.6 in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from urllib3[socks]~=2.5.0->selenium) (1.7.1)
Requirement already satisfied: pycparser in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from cffi>=1.14->trio~=0.30.0->selenium) (2.22)
Requirement already satisfied: h11<1,>=0.9.0 in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from wsproto>=0.14->trio-websocket~=0.12.2->selenium) (0.14.0)
