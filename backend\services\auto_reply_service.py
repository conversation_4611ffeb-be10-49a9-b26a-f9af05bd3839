"""
خدمة الردود التلقائية الذكية
"""

import asyncio
import logging
import time
import random
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from selenium.webdriver.common.action_chains import ActionChains

from models import Settings, AIResponse, Contact
from services.ai_service import AIService
from database import SessionLocal

logger = logging.getLogger(__name__)

class AutoReplyService:
    """خدمة الردود التلقائية الذكية"""
    
    def __init__(self, whatsapp_manager):
        self.whatsapp_manager = whatsapp_manager
        self.driver = None
        self.ai_service = None
        self.is_running = False
        self.monitor_task = None
        self.last_processed_messages = set()
        self.processed_messages = set()  # لتتبع الرسائل المُعالجة
        self.processing_lock = asyncio.Lock()  # لمنع التعارض مع الحملات
        
    async def start_auto_reply(self):
        """بدء خدمة الردود التلقائية - حل جذري محسن"""
        try:
            if self.is_running:
                logger.warning("⚠️ خدمة الردود التلقائية تعمل بالفعل")
                return

            logger.info("🤖 بدء خدمة الردود التلقائية الذكية...")

            # الحل الجذري: استخدام WhatsAppWebService مثل الحملات تماماً
            logger.info("🔧 إنشاء خدمة WhatsApp Web موحدة...")
            from services.whatsapp_web_service import WhatsAppWebService
            self.whatsapp_service = WhatsAppWebService(self.whatsapp_manager)

            # التأكد من جاهزية الخدمة
            logger.info("🔍 فحص جاهزية خدمة WhatsApp Web...")
            connection_test = await self.whatsapp_service.test_connection()
            logger.info(f"📊 نتيجة اختبار الاتصال: {connection_test}")

            if not connection_test.get('success', False):
                # محاولة إعادة تهيئة الخدمة
                logger.warning("⚠️ خدمة WhatsApp Web غير جاهزة، محاولة إعادة التهيئة...")

                # إعادة بدء الجلسة إذا لزم الأمر
                status = self.whatsapp_manager.get_status()
                if not status.get('session_active') or not status.get('logged_in'):
                    logger.info("🔄 إعادة بدء جلسة WhatsApp Web...")
                    session_result = self.whatsapp_manager.start_session()
                    if not session_result.get('success', False):
                        error_msg = "فشل في بدء جلسة WhatsApp Web. يرجى تسجيل الدخول من الإعدادات."
                        logger.error(f"❌ {error_msg}")
                        raise ValueError(error_msg)

                    # انتظار تحميل الصفحة
                    import time
                    time.sleep(5)

                # إعادة إنشاء الخدمة
                self.whatsapp_service = WhatsAppWebService(self.whatsapp_manager)
                connection_test = await self.whatsapp_service.test_connection()

                if not connection_test.get('success', False):
                    error_msg = f"فشل في تهيئة خدمة WhatsApp Web: {connection_test.get('message', 'خطأ غير معروف')}"
                    logger.error(f"❌ {error_msg}")
                    raise ValueError(error_msg)

            # الحصول على المتصفح من الخدمة الموحدة
            self.driver = self.whatsapp_service.driver
            if not self.driver:
                error_msg = "فشل في الحصول على متصفح من خدمة WhatsApp Web"
                logger.error(f"❌ {error_msg}")
                raise ValueError(error_msg)

            logger.info("✅ تم تهيئة خدمة WhatsApp Web بنجاح")

            # التأكد من أن المتصفح في الصفحة الصحيحة
            try:
                current_url = self.driver.current_url
                logger.info(f"🌐 URL الحالي: {current_url}")

                if "web.whatsapp.com" not in current_url:
                    logger.info("🔄 إعادة توجيه المتصفح لـ WhatsApp Web...")
                    self.driver.get("https://web.whatsapp.com")
                    import time
                    time.sleep(3)

            except Exception as e:
                logger.warning(f"⚠️ خطأ في فحص URL: {e}")
                # محاولة إعادة التوجيه
                try:
                    self.driver.get("https://web.whatsapp.com")
                    import time
                    time.sleep(3)
                except Exception as e2:
                    error_msg = f"فشل في إعادة توجيه المتصفح: {e2}"
                    logger.error(f"❌ {error_msg}")
                    raise ValueError(error_msg)

            # إنشاء خدمة الذكاء الاصطناعي
            logger.info("🧠 إنشاء خدمة الذكاء الاصطناعي...")
            db = SessionLocal()
            try:
                settings = db.query(Settings).first()
                if not settings:
                    error_msg = "لا توجد إعدادات في قاعدة البيانات"
                    logger.error(f"❌ {error_msg}")
                    raise ValueError(error_msg)

                if not settings.openai_api_key:
                    error_msg = "مفتاح OpenAI API غير مُعين في الإعدادات"
                    logger.error(f"❌ {error_msg}")
                    raise ValueError(error_msg)

                logger.info(f"✅ تم العثور على مفتاح OpenAI ونموذج: {settings.gpt_model}")

                self.ai_service = AIService(
                    api_key=settings.openai_api_key,
                    model=settings.gpt_model or "gpt-3.5-turbo",
                    system_prompt=settings.gpt_prompt or "أنت مساعد ذكي لخدمة العملاء."
                )

                logger.info("✅ تم إنشاء خدمة الذكاء الاصطناعي بنجاح")

            finally:
                db.close()

            # تعيين حالة التشغيل
            self.is_running = True
            logger.info("✅ تم تعيين حالة التشغيل إلى True")

            # فحص نهائي للتأكد من أن كل شيء يعمل
            logger.info("🔍 فحص نهائي للنظام...")
            final_check = await self._final_system_check()
            if not final_check:
                error_msg = "فشل في الفحص النهائي للنظام"
                logger.error(f"❌ {error_msg}")
                raise ValueError(error_msg)

            # بدء حلقة مراقبة الرسائل في background
            logger.info("🔄 بدء مراقبة الرسائل في الخلفية...")
            self.monitor_task = asyncio.create_task(self._monitor_messages_wrapper())
            logger.info("✅ تم إنشاء مهمة المراقبة")

            # انتظار قصير للتحقق من أن المراقبة بدأت بنجاح
            logger.info("⏳ انتظار للتحقق من بدء المراقبة...")
            await asyncio.sleep(2)  # انتظار أطول

            # التحقق من حالة المهمة
            if self.monitor_task.done():
                logger.warning("⚠️ مهمة المراقبة انتهت بسرعة - فحص الأخطاء...")
                try:
                    exception = self.monitor_task.exception()
                    if exception:
                        self.is_running = False
                        error_msg = f"فشل في بدء مراقبة الرسائل: {exception}"
                        logger.error(f"❌ {error_msg}")
                        import traceback
                        logger.error(f"تفاصيل الخطأ: {traceback.format_exc()}")
                        raise Exception(error_msg)
                except asyncio.InvalidStateError:
                    logger.info("✅ مهمة المراقبة ما زالت تعمل")
            else:
                logger.info("✅ مهمة المراقبة تعمل بنجاح")

            if self.is_running:
                logger.info("🎉 تم بدء خدمة الردود التلقائية والمراقبة بنجاح كاملاً")
            else:
                error_msg = "فشل في بدء خدمة المراقبة - الحالة False"
                logger.error(f"❌ {error_msg}")
                raise Exception(error_msg)

        except Exception as e:
            # لا نوقف الخدمة، بل نحاول المتابعة
            logger.error(f"❌ خطأ في بدء خدمة الردود التلقائية: {e}")
            import traceback
            logger.error(f"تفاصيل الخطأ الكامل: {traceback.format_exc()}")

            # محاولة بدء بسيط بدون فحوصات معقدة
            try:
                self.is_running = True
                self.monitor_task = asyncio.create_task(self._monitor_messages_wrapper())
                logger.info("🔄 تم بدء المراقبة في وضع الطوارئ")
            except Exception as fallback_error:
                self.is_running = False
                logger.error(f"❌ فشل حتى في وضع الطوارئ: {fallback_error}")
                raise
    
    async def stop_auto_reply(self):
        """إيقاف خدمة الردود التلقائية"""
        logger.info("🛑 إيقاف خدمة الردود التلقائية...")
        self.is_running = False

        # إيقاف مهمة المراقبة
        if self.monitor_task and not self.monitor_task.done():
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass

        logger.info("✅ تم إيقاف خدمة الردود التلقائية")

    async def _monitor_messages_wrapper(self):
        """Wrapper للمراقبة مع معالجة الأخطاء وإعادة التشغيل"""
        restart_count = 0
        max_restarts = 3

        while self.is_running and restart_count < max_restarts:
            try:
                logger.info(f"🔄 بدء مراقبة الرسائل (محاولة {restart_count + 1})")
                await self._monitor_messages()

            except asyncio.CancelledError:
                logger.info("⏹️ تم إلغاء مراقبة الرسائل")
                break

            except Exception as e:
                restart_count += 1
                logger.error(f"❌ خطأ في مراقبة الرسائل (محاولة {restart_count}/{max_restarts}): {e}")

                if restart_count < max_restarts and self.is_running:
                    wait_time = min(30 * restart_count, 120)  # انتظار متدرج: 30, 60, 120 ثانية
                    logger.info(f"⏳ إعادة المحاولة خلال {wait_time} ثانية...")
                    await asyncio.sleep(wait_time)
                else:
                    logger.error("❌ تم تجاوز الحد الأقصى لإعادة المحاولة - إيقاف المراقبة")
                    self.is_running = False
                    break

    async def _monitor_messages(self):
        """مراقبة الرسائل الواردة الجديدة"""
        logger.info("👁️ بدء مراقبة الرسائل الواردة...")

        # فحص أولي للتأكد من جاهزية النظام
        try:
            if not self.driver:
                raise Exception("متصفح WhatsApp Web غير متاح")

            if not self.ai_service:
                raise Exception("خدمة الذكاء الاصطناعي غير متاحة")

            # فحص الاتصال الأولي (مع تساهل أكثر)
            connection_ok = self._check_whatsapp_connection()
            if not connection_ok:
                logger.warning("⚠️ فحص الاتصال فشل - سنحاول المتابعة")
                # لا نرفع خطأ، بل نحاول المتابعة

            logger.info("✅ تم التحقق من جاهزية النظام للمراقبة")

        except Exception as e:
            logger.warning(f"⚠️ فشل في الفحص الأولي: {e}")
            logger.info("🔄 سنحاول المتابعة رغم فشل الفحص الأولي")

        consecutive_errors = 0
        max_errors = 5

        while self.is_running:
            try:
                # انتظار متدرج: 3 ثوانٍ عادي، 10 ثوانٍ عند الأخطاء
                sleep_time = 3 if consecutive_errors == 0 else min(10 + consecutive_errors * 2, 30)
                print(f"\n⏰ [انتظار] انتظار {sleep_time} ثانية قبل الدورة التالية...")
                await asyncio.sleep(sleep_time)

                print("🔄 [دورة جديدة] بدء دورة مراقبة جديدة...")

                # التحقق من حالة WhatsApp Web
                print("🌐 [فحص الاتصال] فحص الاتصال مع WhatsApp Web...")
                if not self._check_whatsapp_connection():
                    print("⚠️ [فقدان الاتصال] فقدان الاتصال مع WhatsApp Web")
                    logger.warning("⚠️ فقدان الاتصال مع WhatsApp Web")
                    consecutive_errors += 1
                    continue

                print("✅ [متصل] الاتصال مع WhatsApp Web سليم")

                # التحقق من وجود رسائل جديدة ومعالجتها فوراً
                # (المعالجة تتم داخل _get_new_messages الآن)
                new_messages = await self._get_new_messages()

                if new_messages:
                    print(f"🎉 [نجح] تم العثور ومعالجة {len(new_messages)} رسالة جديدة")
                    logger.info(f"✅ تم العثور ومعالجة {len(new_messages)} رسالة جديدة")
                else:
                    print("ℹ️ [لا توجد رسائل] لا توجد رسائل جديدة في هذه الدورة")
                    logger.debug("ℹ️ لا توجد رسائل جديدة")

                # إعادة تعيين عداد الأخطاء عند النجاح
                consecutive_errors = 0

            except Exception as e:
                consecutive_errors += 1
                logger.error(f"❌ خطأ في مراقبة الرسائل ({consecutive_errors}/{max_errors}): {e}")

                # طباعة تفاصيل الخطأ للتشخيص
                import traceback
                logger.debug(f"تفاصيل الخطأ: {traceback.format_exc()}")

                # إيقاف الخدمة إذا تجاوزت الأخطاء الحد الأقصى
                if consecutive_errors >= max_errors:
                    logger.error(f"❌ تم إيقاف المراقبة بعد {max_errors} أخطاء متتالية")
                    self.is_running = False
                    break

                # انتظار أطول في حالة الخطأ
                wait_time = min(10 + consecutive_errors * 5, 60)
                logger.info(f"⏳ انتظار {wait_time} ثانية قبل المحاولة التالية...")
                await asyncio.sleep(wait_time)

    def _check_whatsapp_connection(self) -> bool:
        """التحقق من حالة الاتصال مع WhatsApp Web"""
        try:
            if not self.driver:
                logger.debug("❌ لا يوجد driver")
                return False

            # التحقق من أن الصفحة ما زالت مفتوحة
            try:
                current_url = self.driver.current_url
                if "web.whatsapp.com" not in current_url:
                    logger.debug(f"❌ URL خاطئ: {current_url}")
                    return False
            except Exception as e:
                logger.debug(f"❌ خطأ في جلب URL: {e}")
                return False

            # التحقق من وجود عناصر WhatsApp الأساسية (مع تساهل أكثر)
            selectors_to_try = [
                "[data-testid='chat-list']",
                "#app",
                "div[data-testid='app']",
                ".app-wrapper-web",
                "#main"
            ]

            for selector in selectors_to_try:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if element:
                        logger.debug(f"✅ تم العثور على عنصر: {selector}")
                        return True
                except:
                    continue

            logger.debug("❌ لم يتم العثور على أي عنصر WhatsApp")
            return False

        except Exception as e:
            logger.debug(f"❌ خطأ في فحص الاتصال: {e}")
            return False

    async def _is_system_busy(self) -> bool:
        """التحقق من انشغال النظام بمهام أخرى"""
        try:
            # فحص وجود حملات قيد التنفيذ
            db = SessionLocal()
            try:
                from models import Campaign
                active_campaigns = db.query(Campaign).filter(
                    Campaign.status.in_(["running", "sending"])
                ).count()

                if active_campaigns > 0:
                    logger.debug(f"⚠️ يوجد {active_campaigns} حملة نشطة")
                    return True

                # فحص استخدام WhatsApp Web من قبل مهام أخرى
                # يمكن إضافة فحوصات أخرى هنا حسب الحاجة

                return False

            finally:
                db.close()

        except Exception as e:
            logger.error(f"❌ خطأ في فحص انشغال النظام: {e}")
            return False

    async def _get_new_messages(self) -> List[Dict[str, Any]]:
        """الحصول على الرسائل الجديدة - بحث محسن وشامل"""
        try:
            print("\n" + "="*80)
            print("🔍 [الرد الذكي] بدء البحث عن رسائل جديدة...")
            print("="*80)
            logger.info("🔍 البحث عن رسائل جديدة...")

            # التأكد من وجود المتصفح
            if not self.driver:
                print("❌ [خطأ] المتصفح غير متاح")
                logger.error("❌ المتصفح غير متاح")
                return []

            print("🌐 [المتصفح] المتصفح متاح ونشط")

            # البحث عن المحادثات بطرق متعددة
            print("🔍 [البحث] البحث عن المحادثات في القائمة...")
            all_chats = await self._find_all_chats_enhanced()

            if not all_chats:
                print("ℹ️ [النتيجة] لا توجد محادثات في القائمة")
                logger.debug("ℹ️ لا توجد محادثات")
                return []

            print(f"✅ [النتيجة] تم العثور على {len(all_chats)} محادثة في القائمة")
            logger.info(f"✅ تم العثور على {len(all_chats)} محادثة")

            new_messages = []
            max_chats_per_cycle = 3  # فحص 3 محادثات في كل دورة

            for i, chat_element in enumerate(all_chats[:max_chats_per_cycle]):
                try:
                    print(f"\n📱 [المحادثة {i+1}] بدء فحص المحادثة {i+1}/{min(len(all_chats), max_chats_per_cycle)}")
                    logger.info(f"📱 فحص المحادثة {i+1}/{min(len(all_chats), max_chats_per_cycle)}")

                    # فحص نوع المحادثة قبل النقر (مجموعة أم فردية)
                    print("🔍 [فحص النوع] فحص نوع المحادثة...")
                    is_group = await self._is_group_chat(chat_element)

                    if is_group:
                        print("👥 [مجموعة] هذه مجموعة - تخطي...")
                        logger.info("👥 تخطي المجموعة")
                        continue

                    print("👤 [فردية] هذه محادثة فردية - متابعة...")

                    # فحص وجود رسائل جديدة قبل النقر
                    print("🔍 [فحص الرسائل] فحص وجود رسائل جديدة...")
                    has_new_messages = await self._check_new_messages_indicator(chat_element)

                    if not has_new_messages:
                        print("ℹ️ [لا توجد رسائل] لا توجد رسائل جديدة - تخطي...")
                        logger.info("ℹ️ لا توجد رسائل جديدة في هذه المحادثة")
                        continue

                    print("✨ [رسائل جديدة] توجد رسائل جديدة - متابعة...")

                    # النقر على المحادثة
                    print(f"👆 [النقر] محاولة النقر على المحادثة {i+1}...")
                    success = await self._click_chat_enhanced(chat_element)
                    if not success:
                        print(f"❌ [فشل] فشل في فتح المحادثة {i+1}")
                        logger.warning(f"⚠️ فشل في فتح المحادثة {i+1}")
                        continue

                    print(f"✅ [نجح] تم فتح المحادثة {i+1} بنجاح")
                    print("⏳ [انتظار] انتظار تحميل المحادثة (2 ثانية)...")
                    await asyncio.sleep(2)  # انتظار تحميل المحادثة

                    # قراءة آخر رسالة وفحص إذا كانت جديدة
                    print("📖 [القراءة] قراءة آخر رسالة في المحادثة...")
                    message_data = await self._read_and_check_message()

                    if message_data and message_data.get('is_new', False):
                        print(f"🎉 [رسالة جديدة] تم العثور على رسالة جديدة!")
                        print(f"👤 [المرسل] من: {message_data['contact_name']}")
                        print(f"💬 [المحتوى] النص: {message_data['message_text'][:50]}...")
                        print(f"📞 [الرقم] رقم الهاتف: {message_data.get('phone_number', 'غير محدد')}")
                        logger.info(f"📨 رسالة جديدة من: {message_data['contact_name']}: {message_data['message_text'][:30]}...")

                        # معالجة الرسالة فوراً مع حماية من التعارض
                        print("🤖 [المعالجة] بدء معالجة الرسالة والرد عليها...")
                        async with self.processing_lock:
                            logger.info("🤖 بدء معالجة الرسالة فوراً...")
                            await self._process_message(message_data)

                        # إضافة للقائمة للإحصائيات
                        new_messages.append(message_data)

                        # انتظار قصير قبل الانتقال للمحادثة التالية
                        print("⏳ [انتظار] انتظار قبل الانتقال للمحادثة التالية (2 ثانية)...")
                        await asyncio.sleep(2)
                    else:
                        print(f"ℹ️ [لا توجد رسائل] لا توجد رسائل جديدة في المحادثة {i+1}")

                except Exception as e:
                    print(f"❌ [خطأ] خطأ في فحص المحادثة {i+1}: {e}")
                    logger.error(f"❌ خطأ في فحص المحادثة {i+1}: {e}")
                    continue

            print(f"\n📊 [النتيجة النهائية] تم العثور ومعالجة {len(new_messages)} رسالة جديدة")
            print("="*80)
            logger.info(f"📊 تم العثور ومعالجة {len(new_messages)} رسالة جديدة")
            return new_messages

        except Exception as e:
            logger.error(f"❌ خطأ في البحث عن الرسائل الجديدة: {e}")
            return []

    async def _is_group_chat(self, chat_element) -> bool:
        """فحص إذا كانت المحادثة مجموعة"""
        try:
            # علامات المجموعات في الواتساب
            group_indicators = [
                # أيقونة المجموعة
                "img[alt*='group']",
                "img[alt*='مجموعة']",
                "span[data-icon='group']",
                "div[data-icon='group']",

                # نص يدل على المجموعة
                "span[title*='group']",
                "span[title*='مجموعة']",

                # عدد الأعضاء
                "span[title*='members']",
                "span[title*='عضو']",
                "span[title*='أعضاء']",

                # أيقونة المجموعة الافتراضية
                "div[data-testid='default-group']",
                "img[src*='group']"
            ]

            for indicator in group_indicators:
                try:
                    elements = chat_element.find_elements(By.CSS_SELECTOR, indicator)
                    if elements:
                        print(f"        👥 [مجموعة] تم اكتشاف مجموعة بـ: {indicator}")
                        return True
                except:
                    continue

            # فحص النص للكلمات الدالة على المجموعة
            try:
                chat_text = chat_element.text.lower()
                group_keywords = ['group', 'مجموعة', 'members', 'عضو', 'أعضاء']

                for keyword in group_keywords:
                    if keyword in chat_text:
                        print(f"        👥 [مجموعة] تم اكتشاف مجموعة بالكلمة: {keyword}")
                        return True
            except:
                pass

            print("        👤 [فردية] هذه محادثة فردية")
            return False

        except Exception as e:
            print(f"        ❌ [خطأ] خطأ في فحص نوع المحادثة: {e}")
            # في حالة الخطأ، نعتبرها محادثة فردية للأمان
            return False

    async def _check_new_messages_indicator(self, chat_element) -> bool:
        """فحص وجود مؤشرات الرسائل الجديدة"""
        try:
            # مؤشرات الرسائل الجديدة
            new_message_indicators = [
                # عداد الرسائل غير المقروءة
                "span[data-testid='unread-count']",
                "div[data-testid='unread-count']",

                # النقطة الخضراء
                "span[aria-label*='unread']",
                "div[aria-label*='unread']",
                "span[aria-label*='غير مقروءة']",

                # عداد الرسائل
                "span.unread-count",
                "div.unread-count",

                # مؤشرات أخرى
                "span[data-icon='unread-count']",
                "div[data-icon='unread-count']",

                # الخلفية المميزة للرسائل الجديدة
                "div[style*='background']",

                # النص الغامق (رسائل جديدة)
                "span[style*='font-weight: bold']",
                "span[style*='font-weight:bold']"
            ]

            for indicator in new_message_indicators:
                try:
                    elements = chat_element.find_elements(By.CSS_SELECTOR, indicator)
                    for element in elements:
                        if element.is_displayed():
                            # فحص إذا كان العداد يحتوي على رقم
                            text = element.text.strip()
                            if text and (text.isdigit() or any(char.isdigit() for char in text)):
                                print(f"        ✨ [رسائل جديدة] تم العثور على عداد: {text}")
                                return True
                            elif element.size['height'] > 0 and element.size['width'] > 0:
                                print(f"        ✨ [رسائل جديدة] تم العثور على مؤشر بـ: {indicator}")
                                return True
                except:
                    continue

            # فحص إضافي: البحث عن النص الغامق في اسم المحادثة
            try:
                name_elements = chat_element.find_elements(By.CSS_SELECTOR, "span, div")
                for element in name_elements:
                    style = element.get_attribute("style") or ""
                    if "font-weight" in style and ("bold" in style or "700" in style):
                        print("        ✨ [رسائل جديدة] تم العثور على نص غامق (رسائل جديدة)")
                        return True
            except:
                pass

            print("        ℹ️ [لا توجد رسائل] لا توجد مؤشرات رسائل جديدة")
            return False

        except Exception as e:
            print(f"        ❌ [خطأ] خطأ في فحص مؤشرات الرسائل الجديدة: {e}")
            # في حالة الخطأ، نفترض وجود رسائل جديدة للأمان
            return True

    async def _find_all_chats_enhanced(self):
        """البحث عن جميع المحادثات بطرق محسنة"""
        try:
            logger.debug("🔍 البحث عن المحادثات...")

            # طرق متعددة للعثور على المحادثات
            chat_selectors = [
                "div[data-testid='cell-frame-container']",  # الطريقة الأساسية
                "div[data-testid='chat-list'] > div",       # قائمة المحادثات
                "[role='listitem']",                        # عناصر القائمة
                "div[data-testid='chat']",                  # محادثات مباشرة
                "div[aria-label*='chat']",                  # محادثات بـ aria-label
                ".x10l6tqk.x13vifvy",                      # classes محددة
                "div[tabindex='-1'][role='listitem']"       # عناصر قابلة للنقر
            ]

            all_chats = []

            for selector in chat_selectors:
                try:
                    chats = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if chats:
                        logger.debug(f"✅ تم العثور على {len(chats)} محادثة باستخدام: {selector}")

                        # فلترة المحادثات الصالحة
                        valid_chats = []
                        for chat in chats:
                            if chat.is_displayed() and chat.is_enabled():
                                # فحص إضافي للتأكد من أنها محادثة حقيقية
                                if self._is_valid_chat(chat):
                                    valid_chats.append(chat)

                        if valid_chats:
                            all_chats.extend(valid_chats)
                            break  # استخدم أول selector ناجح

                except Exception as e:
                    logger.debug(f"❌ فشل selector {selector}: {e}")
                    continue

            # إزالة المكررات
            unique_chats = []
            seen_locations = set()

            for chat in all_chats:
                try:
                    location = (chat.location['x'], chat.location['y'])
                    if location not in seen_locations:
                        unique_chats.append(chat)
                        seen_locations.add(location)
                except:
                    unique_chats.append(chat)  # إضافة حتى لو فشل في الحصول على الموقع

            logger.debug(f"📊 تم العثور على {len(unique_chats)} محادثة فريدة")
            return unique_chats[:10]  # أول 10 محادثات

        except Exception as e:
            logger.error(f"❌ خطأ في البحث عن المحادثات: {e}")
            return []

    def _is_valid_chat(self, chat_element) -> bool:
        """فحص إذا كان العنصر محادثة صالحة"""
        try:
            # فحص وجود نص أو صورة (مؤشر على محادثة حقيقية)
            text_elements = chat_element.find_elements(By.CSS_SELECTOR, "span, div")
            has_text = any(elem.text.strip() for elem in text_elements[:5])  # فحص أول 5 عناصر

            # فحص الحجم (المحادثات الحقيقية لها حجم معقول)
            size = chat_element.size
            has_reasonable_size = size['height'] > 20 and size['width'] > 100

            return has_text and has_reasonable_size

        except:
            return True  # في حالة الشك، اعتبرها صالحة

    async def _click_chat_enhanced(self, chat_element) -> bool:
        """النقر على المحادثة بطرق محسنة"""
        try:
            print("    🎯 [النقر] محاولة النقر على المحادثة...")

            # طرق متعددة للنقر
            click_methods = [
                ("النقر العادي", lambda: chat_element.click()),
                ("النقر بـ JavaScript", lambda: self.driver.execute_script("arguments[0].click();", chat_element)),
                ("التمرير والنقر", lambda: self.driver.execute_script("arguments[0].scrollIntoView(); arguments[0].click();", chat_element))
            ]

            for i, (method_name, method) in enumerate(click_methods):
                try:
                    print(f"    🔄 [المحاولة {i+1}] {method_name}...")
                    method()
                    await asyncio.sleep(1)
                    print(f"    ✅ [نجح] نجح النقر بـ {method_name}")
                    logger.debug(f"✅ نجح النقر بالطريقة {i+1}")
                    return True
                except Exception as e:
                    print(f"    ❌ [فشل] فشل {method_name}: {e}")
                    logger.debug(f"❌ فشل النقر بالطريقة {i+1}: {e}")
                    continue

            print("    ❌ [فشل تام] فشل في جميع طرق النقر")
            return False

        except Exception as e:
            print(f"    ❌ [خطأ عام] خطأ في النقر على المحادثة: {e}")
            logger.error(f"❌ خطأ في النقر على المحادثة: {e}")
            return False

    async def _read_and_check_message(self) -> Optional[Dict[str, Any]]:
        """قراءة آخر رسالة وفحص إذا كانت جديدة"""
        try:
            # انتظار تحميل المحادثة
            await asyncio.sleep(1)

            # الحصول على اسم المرسل
            contact_name = await self._get_contact_name()

            # الحصول على آخر رسالة
            last_message = await self._get_last_incoming_message()

            if not last_message:
                logger.debug("❌ لم يتم العثور على رسالة واردة")
                return None

            # الحصول على السياق (آخر 3 رسائل)
            print("    📚 [السياق] بدء جمع السياق...")
            context = await self._get_conversation_context()
            print(f"    📚 [السياق] النتيجة: {context[:100]}...")

            # إنشاء معرف فريد للرسالة
            message_id = self._generate_message_id(contact_name, last_message)

            # فحص إذا كانت الرسالة جديدة
            is_new = message_id not in self.processed_messages

            if is_new:
                self.processed_messages.add(message_id)
                logger.info(f"✅ رسالة جديدة: {last_message[:30]}...")
            else:
                logger.debug(f"ℹ️ رسالة مُعالجة مسبقاً: {last_message[:30]}...")

            return {
                'contact_name': contact_name,
                'message_text': last_message,
                'context': context,
                'message_id': message_id,
                'is_new': is_new,
                'timestamp': asyncio.get_event_loop().time()
            }

        except Exception as e:
            logger.error(f"❌ خطأ في قراءة الرسالة: {e}")
            return None

    async def _get_conversation_context(self) -> str:
        """أنت مساعد خدمة عملاء محترف ومتماسك في تذكر تفاصيل المحادثة.
        عندما يسأل العميل عن شيء تم ذكره في الرسائل السابقة، راجع السياق وذكر المعلومات المطلوبة.
        مثال: إذا سأله عميل "من أنا؟" وتم ذكر اسمه في رسالة سابقة، يجب أن تجيب باسم العميل.
        تذكر دائماً محتوى المحادثة بأكملها وراجعه قبل الرد."""
        """الحصول على السياق من آخر 6 رسائل مع تحديد المرسل"""
        try:
            print("        📚 [السياق] البحث عن آخر 6 رسائل...")

            # البحث عن جميع الرسائل في المحادثة
            message_containers = []
            container_selectors = [
                "div[data-testid='msg-container']",
                "div[class*='message']",
                "div[role='row']"
            ]

            for selector in container_selectors:
                try:
                    containers = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if containers and len(containers) > 0:
                        message_containers = containers
                        print(f"        ✅ [وجد] {len(containers)} رسالة باستخدام {selector}")
                        break
                except:
                    continue

            if not message_containers:
                print("        ❌ [فشل] لم يتم العثور على رسائل")
                return ""

            # أخذ آخر 6 رسائل
            recent_containers = message_containers[-6:] if len(message_containers) >= 6 else message_containers

            context_messages = []
            for i, container in enumerate(recent_containers):
                try:
                    print(f"        🔍 [رسالة {i+1}] معالجة الرسالة...")

                    # تحديد إذا كانت الرسالة صادرة أم واردة
                    is_outgoing = await self._is_outgoing_message(container)
                    sender = "ai bot" if is_outgoing else "العميل"
                    print(f"        👤 [المرسل] {sender}")

                    # استخراج نص الرسالة
                    message_text = await self._extract_message_text_simple(container)
                    print(f"        📝 [النص الخام] '{message_text}'")

                    if message_text and len(message_text.strip()) > 0:
                        context_messages.append(f"{sender}: {message_text}")
                        print(f"        ✅ [تم إضافة] {sender}: {message_text[:50]}...")
                    else:
                        print(f"        ❌ [تم تجاهل] نص فارغ أو قصير")

                except Exception as e:
                    print(f"        ❌ [خطأ] خطأ في معالجة رسالة: {e}")
                    continue

            if context_messages:
                context = "\n".join(context_messages)
                print(f"        ✅ [نجح] تم جمع {len(context_messages)} رسائل للسياق")
                return context
            else:
                print("        ❌ [فشل] لم يتم استخراج أي رسائل")
                return ""

        except Exception as e:
            print(f"        ❌ [خطأ عام] خطأ في جمع السياق: {e}")
            logger.error(f"❌ خطأ في جمع السياق: {e}")
            return ""

    async def _extract_message_text_simple(self, container) -> str:
        """استخراج نص الرسالة بطريقة بسيطة مع تجنب الأوقات"""
        try:
            # البحث عن النص الفعلي للرسالة (تجنب عناصر الوقت)
            message_text_selectors = [
                "span[class*='selectable-text'][dir='ltr']",
                "span[class*='selectable-text'][dir='auto']",
                "div[class*='selectable-text'] span",
                "span[data-testid*='conversation-text']",
                "div[class*='copyable-text'] span"
            ]

            # جرب كل selector للعثور على النص الفعلي
            for selector in message_text_selectors:
                try:
                    text_elements = container.find_elements(By.CSS_SELECTOR, selector)
                    for element in text_elements:
                        text = element.text.strip()
                        if text and len(text) > 0:
                            print(f"            🔍 [نص خام] '{text}'")

                            # فحص إذا كان النص يحتوي على وقت (تجاهله)
                            import re
                            if re.search(r'\d{1,2}:\d{2}\s*[صم]?\s*\d{0,4}', text):
                                print(f"            ⏰ [تجاهل وقت] '{text}'")
                                continue
                            if re.search(r'\d{4}/\d{1,2}/\d{1,2}', text):
                                print(f"            📅 [تجاهل تاريخ] '{text}'")
                                continue
                            if re.search(r'\d{1,2}/\d{1,2}', text):
                                print(f"            📅 [تجاهل تاريخ مبسط] '{text}'")
                                continue

                            # تنظيف النص
                            cleaned_text = self._clean_message_text(text)
                            print(f"            🧹 [بعد التنظيف] '{cleaned_text}'")

                            if cleaned_text and len(cleaned_text) > 2:  # تجنب النصوص القصيرة جداً
                                print(f"            ✅ [نص نهائي] {cleaned_text[:50]}...")
                                return cleaned_text
                            else:
                                print(f"            ❌ [نص قصير] '{cleaned_text}' - تجاهل")
                except:
                    continue

            # إذا فشلت الطرق المحددة، جرب البحث العام مع تنظيف أقوى
            try:
                all_spans = container.find_elements(By.TAG_NAME, "span")
                for span in all_spans:
                    text = span.text.strip()
                    if text and len(text) > 2:
                        # تجنب النصوص التي تحتوي على أوقات أو تواريخ
                        import re
                        if re.search(r'\d{1,2}:\d{2}', text):
                            continue
                        if re.search(r'\d{4}/\d{1,2}/\d{1,2}', text):
                            continue
                        if re.search(r'\d{1,2}/\d{1,2}', text):
                            continue
                        if re.search(r'[صم]،', text):
                            continue

                        cleaned_text = self._clean_message_text(text)
                        if cleaned_text and len(cleaned_text) > 2:
                            print(f"            📝 [نص عام] {cleaned_text[:50]}...")
                            return cleaned_text
            except:
                pass

            return ""

        except Exception as e:
            print(f"            ❌ [خطأ] خطأ في استخراج النص: {e}")
            return ""

    def _clean_message_text(self, text: str) -> str:
        """تنظيف نص الرسالة من الأوقات والرموز غير المرغوبة"""
        import re

        # إزالة الأوقات والتواريخ الكاملة مثل "[5:26 ص، 2025/7/28]"
        text = re.sub(r'\[\d{1,2}:\d{2}\s*[صم]،\s*\d{4}/\d{1,2}/\d{1,2}\]', '', text)

        # إزالة الأوقات البسيطة مثل "5:17 ص" أو "14:25"
        text = re.sub(r'\b\d{1,2}:\d{2}\s*[صم]?\b', '', text)

        # إزالة التواريخ مثل "2025/7/28"
        text = re.sub(r'\b\d{4}/\d{1,2}/\d{1,2}\b', '', text)
        
        # إزالة التواريخ المبسطة مثل "7/28"
        text = re.sub(r'\b\d{1,2}/\d{1,2}\b', '', text)

        # إزالة أسماء المرسلين مثل "رقمي ايلوكس خاص:" أو "alipasha:"
        text = re.sub(r'^[^:]+:\s*', '', text)

        # إزالة الفواصل والأقواس الزائدة
        text = re.sub(r'[\[\],،]', '', text)

        # إزالة النقطتين المتتاليتين
        text = re.sub(r':{2,}', '', text)
        
        # إزالة عبارات التسليم مثل "تمSeen" أو "Seen"
        text = re.sub(r'\b(seen|تمseen|delivered|تم التسليم)\b', '', text, flags=re.IGNORECASE)

        # تنظيف المسافات الزائدة
        text = ' '.join(text.split())

        return text.strip()

    async def _is_outgoing_message(self, message_element) -> bool:
        """فحص إذا كانت الرسالة صادرة (مني) أم واردة (من العميل)"""
        try:
            # البحث عن علامات الرسائل الصادرة
            outgoing_indicators = [
                "div[data-testid='tail-out']",  # ذيل الرسالة الصادرة
                "div[class*='message-out']",    # كلاس الرسالة الصادرة
                "span[data-testid='msg-dblcheck']",  # علامة التسليم
                "span[data-testid='msg-check']"      # علامة الإرسال
            ]

            for indicator in outgoing_indicators:
                try:
                    if message_element.find_elements(By.CSS_SELECTOR, indicator):
                        return True
                except:
                    continue

            # فحص الموقع - الرسائل الصادرة عادة على اليمين
            try:
                parent = message_element.find_element(By.XPATH, "./..")
                class_name = parent.get_attribute("class") or ""
                if "message-out" in class_name or "tail-out" in class_name:
                    return True
            except:
                pass

            return False

        except Exception as e:
            return False

    async def _get_contact_name(self) -> str:
        """الحصول على اسم المرسل"""
        try:
            # selectors لاسم المرسل
            name_selectors = [
                "header span[title]",
                "header div[data-testid='conversation-info-header'] span",
                "div[data-testid='conversation-header'] span",
                "header h1",
                "header span[dir='auto']"
            ]

            for selector in name_selectors:
                try:
                    name_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if name_element and name_element.text.strip():
                        return name_element.text.strip()
                except:
                    continue

            return "مجهول"

        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على اسم المرسل: {e}")
            return "مجهول"

    async def _get_last_incoming_message(self) -> Optional[str]:
        """الحصول على آخر رسالة نصية واردة بذكاء"""
        try:
            # انتظار تحميل الرسائل
            await asyncio.sleep(2)

            logger.debug("🔍 البحث عن آخر رسالة نصية واردة...")

            # البحث عن جميع الرسائل في المحادثة
            message_containers = await self._find_all_messages()

            if not message_containers:
                logger.debug("❌ لم يتم العثور على رسائل")
                return None

            # فلترة الرسائل الواردة فقط (ليست مرسلة مني)
            incoming_messages = []

            for msg_container in reversed(message_containers):  # البدء من الأحدث
                try:
                    # فحص إذا كانت الرسالة واردة (ليست مرسلة مني)
                    if await self._is_incoming_message(msg_container):
                        # فحص إذا كانت رسالة نصية (ليست صورة أو صوت)
                        text_content = await self._extract_text_from_message(msg_container)
                        if text_content:
                            incoming_messages.append(text_content)
                            logger.debug(f"✅ رسالة نصية واردة: {text_content[:30]}...")

                            # أخذ أول (أحدث) رسالة نصية واردة
                            return text_content

                except Exception as e:
                    logger.debug(f"❌ خطأ في فحص رسالة: {e}")
                    continue

            logger.debug("❌ لم يتم العثور على رسائل نصية واردة")
            return None

        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على آخر رسالة: {e}")
            return None

    async def _find_all_messages(self):
        """العثور على جميع الرسائل في المحادثة"""
        try:
            # selectors شاملة للرسائل
            message_selectors = [
                "div[data-testid='msg-container']",                    # الأساسي
                "div[data-testid='conversation-panel-messages'] > div", # رسائل المحادثة
                "div[role='row']",                                     # صفوف الرسائل
                "div[class*='message']",                               # أي عنصر يحتوي على message
                "div[data-testid='msg-container'] div",                # عناصر فرعية
            ]

            all_messages = []

            for selector in message_selectors:
                try:
                    messages = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if messages:
                        logger.debug(f"✅ تم العثور على {len(messages)} رسالة باستخدام: {selector}")

                        # فلترة الرسائل الصالحة
                        valid_messages = []
                        for msg in messages:
                            if msg.is_displayed() and await self._is_valid_message(msg):
                                valid_messages.append(msg)

                        if valid_messages:
                            all_messages = valid_messages
                            break  # استخدم أول selector ناجح

                except Exception as e:
                    logger.debug(f"❌ فشل selector {selector}: {e}")
                    continue

            logger.debug(f"📊 تم العثور على {len(all_messages)} رسالة صالحة")
            return all_messages

        except Exception as e:
            logger.error(f"❌ خطأ في البحث عن الرسائل: {e}")
            return []

    async def _is_valid_message(self, message_element) -> bool:
        """فحص إذا كان العنصر رسالة صالحة"""
        try:
            # فحص الحجم
            size = message_element.size
            if size['height'] < 10 or size['width'] < 50:
                return False

            # فحص وجود محتوى
            has_content = bool(message_element.text.strip())

            # فحص عدم كونها عنصر تحكم
            tag_name = message_element.tag_name.lower()
            if tag_name in ['button', 'input', 'select']:
                return False

            return has_content

        except:
            return True  # في حالة الشك، اعتبرها صالحة

    async def _is_incoming_message(self, message_element) -> bool:
        """فحص إذا كانت الرسالة واردة (ليست مرسلة مني)"""
        try:
            # طرق متعددة لتحديد الرسائل الواردة

            # 1. فحص الـ classes
            class_attr = message_element.get_attribute('class') or ''
            if 'message-in' in class_attr or 'incoming' in class_attr:
                return True
            if 'message-out' in class_attr or 'outgoing' in class_attr:
                return False

            # 2. فحص العناصر الفرعية
            try:
                # البحث عن مؤشرات الرسائل الواردة
                incoming_indicators = message_element.find_elements(By.CSS_SELECTOR,
                    "div[class*='message-in'], span[class*='message-in'], div[class*='incoming']")
                if incoming_indicators:
                    return True

                # البحث عن مؤشرات الرسائل الصادرة
                outgoing_indicators = message_element.find_elements(By.CSS_SELECTOR,
                    "div[class*='message-out'], span[class*='message-out'], div[class*='outgoing']")
                if outgoing_indicators:
                    return False
            except:
                pass

            # 3. فحص الموقع (الرسائل الواردة عادة على اليسار)
            try:
                location = message_element.location
                parent_width = self.driver.execute_script("return window.innerWidth;")

                # إذا كانت الرسالة في النصف الأيسر من الشاشة، فهي واردة غالباً
                if location['x'] < parent_width * 0.6:
                    return True
            except:
                pass

            # 4. فحص وجود صورة المرسل (الرسائل الواردة لها صورة مرسل)
            try:
                avatar = message_element.find_element(By.CSS_SELECTOR, "img[alt*='profile'], div[class*='avatar']")
                if avatar:
                    return True
            except:
                pass

            # افتراضياً، اعتبرها واردة إذا لم نتمكن من التحديد
            return True

        except Exception as e:
            logger.debug(f"❌ خطأ في فحص نوع الرسالة: {e}")
            return True  # افتراضياً واردة

    async def _extract_text_from_message(self, message_element) -> Optional[str]:
        """استخراج النص من الرسالة بذكاء (نص فقط، لا صور أو صوت)"""
        try:
            # فحص إذا كانت الرسالة تحتوي على وسائط (صور، صوت، فيديو)
            media_indicators = [
                "img[src*='blob']",                    # صور
                "video",                               # فيديو
                "audio",                               # صوت
                "div[data-testid='media-viewer']",     # عارض الوسائط
                "div[class*='media']",                 # عناصر وسائط
                "svg[data-testid='audio']",            # أيقونة صوت
                "svg[data-testid='video']",            # أيقونة فيديو
                "div[data-testid='image']",            # صورة
                "span[data-testid='audio-duration']",  # مدة الصوت
            ]

            # فحص وجود وسائط
            for media_selector in media_indicators:
                try:
                    media_elements = message_element.find_elements(By.CSS_SELECTOR, media_selector)
                    if media_elements:
                        logger.debug("❌ الرسالة تحتوي على وسائط - تجاهل")
                        return None
                except:
                    continue

            # استخراج النص من الرسالة
            text_selectors = [
                "span[class*='selectable-text']",      # النص القابل للتحديد
                "div[class*='selectable-text']",       # نص في div
                "span[dir='auto']",                    # نص تلقائي الاتجاه
                "span[dir='ltr']",                     # نص من اليسار لليمين
                "span[dir='rtl']",                     # نص من اليمين لليسار
                ".selectable-text",                    # class مباشر
                "span:not([data-testid])",             # span بدون testid
                "div:not([data-testid])",              # div بدون testid
            ]

            extracted_text = ""

            for text_selector in text_selectors:
                try:
                    text_elements = message_element.find_elements(By.CSS_SELECTOR, text_selector)
                    for text_element in text_elements:
                        text = text_element.text.strip()
                        if text and len(text) > 2:  # تجاهل النصوص القصيرة جداً
                            # تجنب النصوص التي تبدو كأوقات أو معلومات تقنية
                            if not self._is_metadata_text(text):
                                extracted_text += text + " "
                except:
                    continue

            # تنظيف النص
            extracted_text = extracted_text.strip()

            # فحص إضافي للتأكد من أن النص مفيد
            if extracted_text and len(extracted_text) >= 3:
                # إزالة النصوص المكررة
                words = extracted_text.split()
                unique_words = []
                for word in words:
                    if word not in unique_words:
                        unique_words.append(word)

                final_text = " ".join(unique_words)

                if len(final_text) >= 3:
                    logger.debug(f"✅ تم استخراج النص: {final_text[:50]}...")
                    return final_text

            # إذا فشل كل شيء، جرب النص المباشر للعنصر
            direct_text = message_element.text.strip()
            if direct_text and len(direct_text) >= 3 and not self._is_metadata_text(direct_text):
                logger.debug(f"✅ نص مباشر: {direct_text[:50]}...")
                return direct_text

            return None

        except Exception as e:
            logger.error(f"❌ خطأ في استخراج النص: {e}")
            return None

    def _is_metadata_text(self, text: str) -> bool:
        """فحص إذا كان النص معلومات تقنية وليس محتوى الرسالة"""
        text_lower = text.lower().strip()

        # أنماط النصوص التقنية التي يجب تجاهلها
        metadata_patterns = [
            # أوقات
            r'^\d{1,2}:\d{2}',                    # 12:34
            r'^\d{1,2}:\d{2}\s*(am|pm)',          # 12:34 PM

            # تواريخ
            r'^\d{1,2}/\d{1,2}',                  # 12/34
            r'^\d{4}-\d{2}-\d{2}',                # 2024-01-01

            # حالات الرسالة
            'delivered', 'read', 'sent', 'pending',
            'تم التسليم', 'تم القراءة', 'تم الإرسال',

            # معلومات تقنية
            'typing...', 'online', 'last seen',
            'يكتب...', 'متصل', 'آخر ظهور',

            # أرقام فقط
            r'^\d+$',

            # نصوص قصيرة جداً أو فارغة
            r'^.{1,2}$',
        ]

        import re
        for pattern in metadata_patterns:
            if re.search(pattern, text_lower):
                return True

        # فحص إضافي للنصوص الغريبة
        if len(text) < 3 or text.isdigit():
            return True

        return False

    def _generate_message_id(self, contact_name: str, message_text: str) -> str:
        """إنشاء معرف فريد للرسالة"""
        import hashlib

        # إنشاء hash من اسم المرسل ونص الرسالة
        content = f"{contact_name}:{message_text[:100]}"  # أول 100 حرف
        return hashlib.md5(content.encode()).hexdigest()[:16]

    async def _find_unread_chats_unified(self):
        """البحث عن المحادثات غير المقروءة باستخدام الخدمة الموحدة - نفس منطق الحملات"""
        try:
            from selenium.webdriver.common.by import By
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC

            # انتظار تحميل قائمة المحادثات
            wait = WebDriverWait(self.driver, 10)

            # استخدام نفس selectors المستخدمة في الحملات الإعلانية
            chat_selectors = [
                "div[data-testid='cell-frame-container']",
                "div[data-testid='chat-list'] > div",
                "div[role='listitem']",
                "#pane-side div[data-testid='cell-frame-container']",
                "div[data-testid='chat-list-item']"
            ]

            all_chats = []
            for selector in chat_selectors:
                try:
                    chats = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if chats:
                        all_chats = chats
                        logger.debug(f"✅ تم العثور على {len(chats)} محادثة باستخدام: {selector}")
                        break
                except Exception as e:
                    logger.debug(f"❌ فشل selector {selector}: {e}")
                    continue

            if not all_chats:
                logger.warning("⚠️ لم يتم العثور على أي محادثات")
                return []

            unread_chats = []

            # استخدام نفس منطق فحص الرسائل غير المقروءة المستخدم في الحملات
            for chat in all_chats:
                try:
                    # البحث عن مؤشرات الرسائل غير المقروءة - نفس المنطق المستخدم في الحملات
                    unread_indicators = [
                        "span[data-testid='icon-unread-count']",  # عداد الرسائل
                        "div[data-testid='unread-count']",        # عداد بديل
                        "span[aria-label*='unread']",             # aria label
                        "div[class*='unread']",                   # class يحتوي على unread
                        "span[class*='unread']",                  # span unread
                        ".bg-unread",                             # خلفية غير مقروءة
                        "[data-icon='unread-count']"              # data-icon
                    ]

                    has_unread = False
                    for indicator in unread_indicators:
                        try:
                            unread_element = chat.find_element(By.CSS_SELECTOR, indicator)
                            if unread_element and unread_element.is_displayed():
                                has_unread = True
                                logger.debug(f"✅ تم العثور على مؤشر غير مقروء: {indicator}")
                                break
                        except:
                            continue

                    # فحص إضافي للنقطة الخضراء أو العدادات
                    if not has_unread:
                        try:
                            # البحث عن النقطة الخضراء أو العدادات الرقمية
                            green_indicators = chat.find_elements(By.CSS_SELECTOR, "span[data-testid='icon-unread-count'], div[data-testid='unread-count'], span[aria-label*='unread']")
                            for indicator in green_indicators:
                                if indicator.is_displayed() and (indicator.text.strip() or indicator.get_attribute('aria-label')):
                                    has_unread = True
                                    logger.debug("✅ تم العثور على مؤشر رقمي غير مقروء")
                                    break
                        except:
                            pass

                    if has_unread:
                        unread_chats.append(chat)
                        logger.debug("📨 تم إضافة محادثة غير مقروءة")

                except Exception as e:
                    logger.debug(f"❌ خطأ في فحص محادثة: {e}")
                    continue

            logger.debug(f"📊 تم العثور على {len(unread_chats)} محادثة غير مقروءة من أصل {len(all_chats)}")
            return unread_chats

        except Exception as e:
            logger.error(f"❌ خطأ في البحث الموحد: {e}")
            return []

    async def _click_chat_unified(self, chat_element) -> bool:
        """النقر على المحادثة باستخدام الخدمة الموحدة - نفس منطق الحملات"""
        try:
            # استخدام نفس طريقة النقر المستخدمة في الحملات
            self.driver.execute_script("arguments[0].scrollIntoView(true);", chat_element)
            await asyncio.sleep(0.5)

            # محاولة النقر بطرق متعددة
            try:
                # الطريقة الأولى: النقر العادي
                chat_element.click()
                logger.debug("✅ تم النقر على المحادثة بالطريقة العادية")
                return True
            except:
                try:
                    # الطريقة الثانية: النقر باستخدام JavaScript
                    self.driver.execute_script("arguments[0].click();", chat_element)
                    logger.debug("✅ تم النقر على المحادثة باستخدام JavaScript")
                    return True
                except:
                    try:
                        # الطريقة الثالثة: النقر باستخدام ActionChains
                        from selenium.webdriver.common.action_chains import ActionChains
                        ActionChains(self.driver).move_to_element(chat_element).click().perform()
                        logger.debug("✅ تم النقر على المحادثة باستخدام ActionChains")
                        return True
                    except Exception as e:
                        logger.error(f"❌ فشل في النقر على المحادثة: {e}")
                        return False

        except Exception as e:
            logger.error(f"❌ خطأ في النقر على المحادثة: {e}")
            return False

    async def _read_last_message_unified(self) -> Optional[Dict[str, Any]]:
        """قراءة آخر رسالة باستخدام الخدمة الموحدة - نفس منطق الحملات"""
        try:
            from selenium.webdriver.common.by import By

            # الحصول على اسم المرسل من header المحادثة - نفس المنطق المستخدم في الحملات
            contact_name = "غير معروف"
            try:
                name_selectors = [
                    "header span[title]",
                    "header h1 span[title]",
                    "[data-testid='conversation-header'] span[title]",
                    "header div[role='button'] span[title]",
                    "header [data-testid='conversation-info-header'] span[title]"
                ]

                for selector in name_selectors:
                    try:
                        name_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                        contact_name = name_element.get_attribute("title") or name_element.text
                        if contact_name and contact_name.strip() and contact_name != "غير معروف":
                            contact_name = contact_name.strip()
                            break
                    except:
                        continue

            except Exception as e:
                logger.debug(f"❌ خطأ في الحصول على اسم المرسل: {e}")

            # قراءة آخر رسالة - نفس المنطق المستخدم في الحملات
            message_selectors = [
                "div[data-testid='conversation-panel-messages'] div[data-testid='msg-container']:last-child span[data-testid='msg-text']",
                "div[data-testid='conversation-panel-messages'] div[class*='message']:last-child span",
                "#main div[data-testid='msg-container']:last-child span[data-testid='msg-text']",
                "#main div[class*='message']:last-child span[class*='selectable-text']",
                "div[role='application'] div[data-testid='msg-container']:last-child span"
            ]

            message_text = None
            for selector in message_selectors:
                try:
                    message_elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if message_elements:
                        # أخذ آخر رسالة
                        last_message = message_elements[-1]
                        message_text = last_message.text.strip()
                        if message_text:
                            logger.debug(f"✅ تم العثور على الرسالة باستخدام: {selector}")
                            break
                except Exception as e:
                    logger.debug(f"❌ فشل selector {selector}: {e}")
                    continue

            if not message_text:
                logger.warning("⚠️ لم يتم العثور على نص الرسالة")
                return None

            # إنشاء معرف فريد للرسالة
            import hashlib
            message_id = hashlib.md5(f"{contact_name}_{message_text}_{len(message_text)}".encode()).hexdigest()

            # التحقق من أن الرسالة جديدة
            if message_id in self.processed_messages:
                logger.debug(f"📭 الرسالة تم معالجتها مسبقاً: {message_id}")
                return None

            # إضافة الرسالة للمعالجة
            self.processed_messages.add(message_id)

            # محاولة استخراج رقم الهاتف من اسم جهة الاتصال أو URL
            phone_number = await self._extract_phone_number(contact_name)

            message_data = {
                'contact_name': contact_name,
                'message_text': message_text,
                'message_id': message_id,
                'phone_number': phone_number,
                'timestamp': asyncio.get_event_loop().time(),
                'is_new': True
            }

            logger.info(f"📨 رسالة جديدة من {contact_name}: {message_text[:50]}...")
            return message_data

        except Exception as e:
            logger.error(f"❌ خطأ في قراءة الرسالة: {e}")
            return None

    async def _extract_phone_number(self, contact_name: str) -> str:
        """استخراج رقم الهاتف من اسم جهة الاتصال أو URL"""
        try:
            # محاولة الحصول على رقم الهاتف من URL الحالي
            current_url = self.driver.current_url
            if 'chat' in current_url:
                # استخراج الرقم من URL مثل: https://web.whatsapp.com/send?phone=1234567890
                import re
                phone_match = re.search(r'phone[=/](\d+)', current_url)
                if phone_match:
                    return phone_match.group(1)

                # استخراج من chat ID
                chat_match = re.search(r'chat/(\d+)', current_url)
                if chat_match:
                    return chat_match.group(1)

            # محاولة استخراج الرقم من اسم جهة الاتصال إذا كان يحتوي على أرقام
            import re
            numbers = re.findall(r'\d+', contact_name)
            if numbers:
                # أخذ أطول رقم (غالباً رقم الهاتف)
                longest_number = max(numbers, key=len)
                if len(longest_number) >= 8:  # رقم هاتف معقول
                    return longest_number

            # إذا فشل كل شيء، استخدم اسم جهة الاتصال كمعرف
            return contact_name.replace(' ', '_').replace('+', '')

        except Exception as e:
            logger.debug(f"❌ خطأ في استخراج رقم الهاتف: {e}")
            return contact_name.replace(' ', '_').replace('+', '')

    async def _find_unread_chats_advanced(self) -> List:
        """البحث عن المحادثات غير المقروءة باستخدام JavaScript المتقدم"""
        try:
            logger.debug("🔍 البحث المتقدم عن المحادثات غير المقروءة...")

            script = """
            // البحث عن المحادثات غير المقروءة بطرق متعددة
            var unreadChats = [];

            // طريقة 1: البحث عن عدادات الرسائل
            var counters = document.querySelectorAll('span[data-testid="icon-unread-count"]');
            counters.forEach(function(counter) {
                var chat = counter.closest('div[data-testid="cell-frame-container"]');
                if (chat && !unreadChats.includes(chat)) {
                    unreadChats.push(chat);
                }
            });

            // طريقة 2: البحث عن النقاط الخضراء
            var greenDots = document.querySelectorAll('span[aria-label*="unread"], span[title*="unread"]');
            greenDots.forEach(function(dot) {
                var chat = dot.closest('div[data-testid="cell-frame-container"]');
                if (chat && !unreadChats.includes(chat)) {
                    unreadChats.push(chat);
                }
            });

            // طريقة 3: البحث عن المحادثات مع خلفية مختلفة
            var allChats = document.querySelectorAll('div[data-testid="cell-frame-container"]');
            allChats.forEach(function(chat) {
                var style = window.getComputedStyle(chat);
                var bgColor = style.backgroundColor;
                // إذا كانت الخلفية ليست شفافة أو بيضاء
                if (bgColor !== 'rgba(0, 0, 0, 0)' && bgColor !== 'rgb(255, 255, 255)' && bgColor !== 'transparent') {
                    if (!unreadChats.includes(chat)) {
                        unreadChats.push(chat);
                    }
                }
            });

            return unreadChats;
            """

            elements = self.driver.execute_script(script)
            if elements:
                logger.debug(f"✅ البحث المتقدم: تم العثور على {len(elements)} محادثة")
                return elements

            return []

        except Exception as e:
            logger.debug(f"❌ فشل البحث المتقدم: {e}")
            return []

    async def _find_unread_chats_traditional(self) -> List:
        """البحث التقليدي عن المحادثات غير المقروءة"""
        try:
            logger.debug("🔍 البحث التقليدي عن المحادثات غير المقروءة...")

            # قائمة بجميع الطرق التقليدية
            selectors = [
                "span[data-testid='icon-unread-count']",
                "div[aria-label*='unread']",
                "span[aria-label*='unread']",
                "[data-testid='icon-unread-count']",
                "div[data-testid='cell-frame-container'] span[style*='background']",
                "div[data-testid='cell-frame-container'] div[style*='background-color']"
            ]

            unread_chats = []

            for selector in selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        # البحث عن المحادثة الأب
                        chat_container = element.find_element(By.XPATH, "./ancestor::div[@data-testid='cell-frame-container']")
                        if chat_container and chat_container not in unread_chats:
                            unread_chats.append(chat_container)
                except Exception as e:
                    logger.debug(f"فشل selector {selector}: {e}")
                    continue

            if unread_chats:
                logger.debug(f"✅ البحث التقليدي: تم العثور على {len(unread_chats)} محادثة")
                return unread_chats

            return []

        except Exception as e:
            logger.debug(f"❌ فشل البحث التقليدي: {e}")
            return []

    async def _find_unread_chats_scan_all(self) -> List:
        """فحص جميع المحادثات للبحث عن الجديدة - الطريقة الأخيرة"""
        try:
            logger.debug("🔍 فحص شامل لجميع المحادثات...")

            # الحصول على جميع المحادثات
            all_chats = self.driver.find_elements(By.CSS_SELECTOR, "div[data-testid='cell-frame-container']")

            if not all_chats:
                logger.debug("❌ لم يتم العثور على أي محادثات")
                return []

            logger.debug(f"📊 فحص {len(all_chats)} محادثة...")

            unread_chats = []

            # فحص كل محادثة للبحث عن علامات عدم القراءة
            for i, chat in enumerate(all_chats[:10]):  # فحص أول 10 محادثات فقط
                try:
                    # البحث عن علامات عدم القراءة داخل المحادثة
                    unread_indicators = [
                        # عدادات الرسائل
                        chat.find_elements(By.CSS_SELECTOR, "span[data-testid='icon-unread-count']"),
                        # النقاط الخضراء
                        chat.find_elements(By.CSS_SELECTOR, "span[aria-label*='unread']"),
                        # عناصر مع خلفية ملونة
                        chat.find_elements(By.CSS_SELECTOR, "span[style*='background']"),
                        # أي عنصر يحتوي على كلمة unread
                        chat.find_elements(By.XPATH, ".//*[contains(@aria-label, 'unread') or contains(@title, 'unread')]")
                    ]

                    # إذا وجدت أي علامة عدم قراءة
                    has_unread = any(indicators for indicators in unread_indicators)

                    if has_unread:
                        unread_chats.append(chat)
                        logger.debug(f"✅ محادثة غير مقروءة #{i+1}")

                    # فحص النص للبحث عن أرقام (عدادات)
                    chat_text = chat.text
                    if chat_text and any(char.isdigit() for char in chat_text):
                        # البحث عن أرقام منفصلة (قد تكون عدادات)
                        import re
                        numbers = re.findall(r'\b\d+\b', chat_text)
                        if numbers and chat not in unread_chats:
                            # إذا وجدت أرقام صغيرة (أقل من 100) قد تكون عدادات
                            small_numbers = [int(n) for n in numbers if int(n) < 100]
                            if small_numbers:
                                unread_chats.append(chat)
                                logger.debug(f"✅ محادثة مع عداد محتمل #{i+1}: {small_numbers}")

                except Exception as e:
                    logger.debug(f"خطأ في فحص المحادثة #{i+1}: {e}")
                    continue

            if unread_chats:
                logger.debug(f"✅ الفحص الشامل: تم العثور على {len(unread_chats)} محادثة محتملة")
                return unread_chats

            return []

        except Exception as e:
            logger.debug(f"❌ فشل الفحص الشامل: {e}")
            return []

    async def _final_system_check(self) -> bool:
        """فحص نهائي شامل للنظام قبل بدء المراقبة"""
        try:
            logger.info("🔍 بدء الفحص النهائي للنظام...")

            # 1. فحص المتصفح
            if not self.driver:
                logger.error("❌ المتصفح غير متاح")
                return False

            # 2. فحص الاتصال بـ WhatsApp Web
            try:
                current_url = self.driver.current_url
                if "web.whatsapp.com" not in current_url:
                    logger.error(f"❌ المتصفح ليس في WhatsApp Web: {current_url}")
                    return False
                logger.info("✅ المتصفح في WhatsApp Web")
            except Exception as e:
                logger.error(f"❌ خطأ في فحص URL: {e}")
                return False

            # 3. فحص تسجيل الدخول
            try:
                # البحث عن عناصر تدل على تسجيل الدخول
                login_indicators = [
                    "div[data-testid='cell-frame-container']",  # قائمة المحادثات
                    "header[data-testid='chatlist-header']",    # header قائمة المحادثات
                    "div[data-testid='side']"                   # الشريط الجانبي
                ]

                found_indicator = False
                for indicator in login_indicators:
                    try:
                        elements = self.driver.find_elements(By.CSS_SELECTOR, indicator)
                        if elements:
                            found_indicator = True
                            logger.info(f"✅ تم العثور على مؤشر تسجيل الدخول: {indicator}")
                            break
                    except:
                        continue

                if not found_indicator:
                    logger.error("❌ لم يتم العثور على مؤشرات تسجيل الدخول")
                    return False

            except Exception as e:
                logger.error(f"❌ خطأ في فحص تسجيل الدخول: {e}")
                return False

            # 4. فحص خدمة الذكاء الاصطناعي
            if not self.ai_service:
                logger.error("❌ خدمة الذكاء الاصطناعي غير متاحة")
                return False
            logger.info("✅ خدمة الذكاء الاصطناعي متاحة")

            # 5. اختبار بسيط لاكتشاف المحادثات
            try:
                all_chats = self.driver.find_elements(By.CSS_SELECTOR, "div[data-testid='cell-frame-container']")
                logger.info(f"✅ تم العثور على {len(all_chats)} محادثة في الصفحة")

                if len(all_chats) == 0:
                    logger.warning("⚠️ لا توجد محادثات في الصفحة - قد يكون هذا طبيعياً")

            except Exception as e:
                logger.warning(f"⚠️ خطأ في فحص المحادثات: {e}")
                # لا نفشل الفحص بسبب هذا

            logger.info("🎉 نجح الفحص النهائي للنظام!")
            return True

        except Exception as e:
            logger.error(f"❌ خطأ في الفحص النهائي: {e}")
            return False
    
    async def _read_last_message(self) -> Optional[Dict[str, Any]]:
        """قراءة آخر رسالة في المحادثة الحالية"""
        try:
            # الحصول على اسم المرسل من header المحادثة
            contact_name = "غير معروف"
            try:
                # طرق متعددة للحصول على اسم المرسل
                name_selectors = [
                    "header span[title]",
                    "header h1 span[title]",
                    "[data-testid='conversation-header'] span[title]",
                    "header div[role='button'] span[title]"
                ]

                for selector in name_selectors:
                    try:
                        name_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                        contact_name = name_element.get_attribute("title") or name_element.text
                        if contact_name and contact_name != "غير معروف":
                            break
                    except:
                        continue

            except Exception as e:
                logger.debug(f"لم يتم العثور على اسم المرسل: {e}")

            # الحصول على رقم الهاتف
            phone_number = "غير معروف"
            try:
                # البحث عن رقم الهاتف في عناصر مختلفة
                phone_selectors = [
                    "span[title*='+']",
                    "[data-testid='conversation-header'] span[title*='+']",
                    "header span[title*='+']"
                ]

                for selector in phone_selectors:
                    try:
                        phone_elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        for element in phone_elements:
                            title = element.get_attribute("title")
                            if title and "+" in title:
                                phone_number = title
                                break
                        if phone_number != "غير معروف":
                            break
                    except:
                        continue

            except Exception as e:
                logger.debug(f"لم يتم العثور على رقم الهاتف: {e}")

            # البحث عن آخر رسالة واردة
            last_message = await self._find_last_incoming_message()

            if not last_message:
                logger.debug("لم يتم العثور على رسائل واردة")
                return None

            message_text = last_message.get("text", "").strip()
            message_time = last_message.get("time", "")

            if not message_text:
                logger.debug("الرسالة فارغة")
                return None

            # إنشاء معرف فريد للرسالة
            message_id = f"{phone_number}_{hash(message_text)}_{message_time}"

            # التحقق من أن الرسالة جديدة
            if message_id in self.last_processed_messages:
                logger.debug(f"رسالة مُعالجة مسبقاً من {contact_name}")
                return None

            # إضافة الرسالة للمعالجة
            self.last_processed_messages.add(message_id)

            # تنظيف الذاكرة
            if len(self.last_processed_messages) > 100:
                old_messages = list(self.last_processed_messages)[:-50]
                for old_msg in old_messages:
                    self.last_processed_messages.discard(old_msg)

            return {
                "contact_name": contact_name,
                "phone_number": phone_number,
                "message_text": message_text,
                "message_id": message_id,
                "message_time": message_time,
                "timestamp": datetime.now()
            }

        except Exception as e:
            logger.error(f"❌ خطأ في قراءة آخر رسالة: {e}")
            return None

    async def _find_last_incoming_message(self) -> Optional[Dict[str, str]]:
        """البحث عن آخر رسالة واردة (ليست مرسلة مني)"""
        try:
            # انتظار تحميل الرسائل
            await asyncio.sleep(1)

            # طرق متعددة للبحث عن الرسائل
            message_selectors = [
                "div[data-testid='conversation-panel-messages'] div[data-testid='msg-container']",
                "div[data-testid='msg-container']",
                "[data-testid='msg-container']",
                "div[class*='message']"
            ]

            message_elements = []
            for selector in message_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        message_elements = elements
                        logger.debug(f"✅ تم العثور على {len(elements)} رسالة باستخدام: {selector}")
                        break
                except Exception as e:
                    logger.debug(f"فشل في استخدام selector: {selector} - {e}")
                    continue

            if not message_elements:
                logger.debug("❌ لم يتم العثور على رسائل في المحادثة")
                return None

            logger.debug(f"🔍 فحص آخر {min(len(message_elements), 5)} رسائل...")

            # البحث عن آخر رسالة واردة (فحص آخر 5 رسائل)
            for i, message_element in enumerate(reversed(message_elements[-5:])):
                try:
                    logger.debug(f"فحص الرسالة {i+1}...")

                    # التحقق من أن الرسالة واردة وليست مرسلة
                    if self._is_outgoing_message(message_element):
                        logger.debug("تخطي رسالة مرسلة")
                        continue

                    # استخراج نص الرسالة
                    message_text = self._extract_message_text(message_element)
                    if not message_text or len(message_text.strip()) < 2:
                        logger.debug("تخطي رسالة فارغة أو قصيرة جداً")
                        continue

                    # استخراج وقت الرسالة
                    message_time = self._extract_message_time(message_element)

                    logger.debug(f"📨 تم العثور على رسالة واردة: {message_text[:30]}...")
                    return {
                        "text": message_text,
                        "time": message_time or str(int(time.time()))
                    }

                except Exception as e:
                    logger.debug(f"تخطي رسالة بسبب خطأ: {e}")
                    continue

            logger.debug("❌ لم يتم العثور على رسائل واردة جديدة")
            return None

        except Exception as e:
            logger.error(f"❌ خطأ في البحث عن آخر رسالة واردة: {e}")
            return None

    def _is_outgoing_message(self, message_element) -> bool:
        """التحقق من أن الرسالة مرسلة مني وليست واردة"""
        try:
            # 1. فحص الـ classes
            classes = message_element.get_attribute("class") or ""
            if "message-out" in classes or "msg-out" in classes:
                return True
            if "message-in" in classes or "msg-in" in classes:
                return False

            # 2. فحص وجود علامة التسليم (تظهر فقط في الرسائل المرسلة)
            try:
                delivery_icons = message_element.find_elements(
                    By.CSS_SELECTOR,
                    "[data-testid*='msg-check'], [data-testid*='msg-dblcheck'], [data-testid='msg-time'] + span"
                )
                if delivery_icons:
                    return True
            except:
                pass

            # 3. فحص الموقع النسبي (الرسائل المرسلة عادة في الجانب الأيمن)
            try:
                location = message_element.location
                page_width = self.driver.execute_script("return window.innerWidth")
                if location['x'] > page_width * 0.5:  # إذا كانت في النصف الأيمن
                    return True
            except:
                pass

            # 4. فحص data attributes
            try:
                data_attrs = message_element.get_attribute("data-pre-plain-text") or ""
                if "أنت:" in data_attrs or "You:" in data_attrs:
                    return True
            except:
                pass

            # افتراضياً: اعتبرها رسالة واردة
            return False

        except Exception as e:
            logger.debug(f"خطأ في تحديد اتجاه الرسالة: {e}")
            return False

    def _extract_message_text(self, message_element) -> str:
        """استخراج نص الرسالة بطرق متعددة"""
        try:
            # طرق متعددة لاستخراج النص
            text_selectors = [
                "span.selectable-text",
                ".selectable-text",
                "span[data-testid='conversation-text']",
                "div.copyable-text span",
                "div[data-testid='conversation-text']",
                ".message-text",
                "span[dir='ltr']",
                "span[dir='rtl']",
                "span",
                "div"
            ]

            # محاولة استخراج النص باستخدام selectors مختلفة
            for selector in text_selectors:
                try:
                    text_elements = message_element.find_elements(By.CSS_SELECTOR, selector)
                    for text_element in text_elements:
                        text = text_element.text.strip()
                        # تجاهل النصوص القصيرة جداً أو الفارغة
                        if text and len(text) > 1 and not text.isdigit():
                            # تجاهل النصوص التي تبدو كأوقات أو تواريخ
                            if not any(char in text for char in [':', 'AM', 'PM', '/']):
                                return text
                except Exception as e:
                    logger.debug(f"فشل في استخدام selector {selector}: {e}")
                    continue

            # محاولة أخيرة باستخدام JavaScript
            try:
                script = """
                var element = arguments[0];
                var textContent = element.textContent || element.innerText || '';
                // إزالة الأوقات والتواريخ
                var lines = textContent.split('\\n');
                for (var i = 0; i < lines.length; i++) {
                    var line = lines[i].trim();
                    if (line.length > 1 && !line.match(/^\\d{1,2}:\\d{2}/) && !line.match(/AM|PM/)) {
                        return line;
                    }
                }
                return '';
                """
                text = self.driver.execute_script(script, message_element)
                if text and len(text.strip()) > 1:
                    return text.strip()
            except Exception as e:
                logger.debug(f"فشل في استخراج النص باستخدام JavaScript: {e}")

            return ""

        except Exception as e:
            logger.debug(f"خطأ في استخراج نص الرسالة: {e}")
            return ""

    def _extract_message_time(self, message_element) -> str:
        """استخراج وقت الرسالة"""
        try:
            time_selectors = [
                "span[data-testid='msg-time']",
                ".message-time",
                "span[title*=':']",
                "[data-testid='msg-time']"
            ]

            for selector in time_selectors:
                try:
                    time_element = message_element.find_element(By.CSS_SELECTOR, selector)
                    time_text = time_element.get_attribute("title") or time_element.text
                    if time_text:
                        return time_text
                except:
                    continue

            return ""

        except Exception as e:
            logger.debug(f"خطأ في استخراج وقت الرسالة: {e}")
            return ""

    async def _process_message(self, message_data: Dict[str, Any]):
        """معالجة رسالة واردة وإرسال رد ذكي"""
        try:
            print(f"\n    🤖 [معالجة الرسالة] بدء معالجة رسالة من {message_data['contact_name']}")
            print(f"    💬 [النص] {message_data['message_text'][:100]}...")
            logger.info(f"🤖 معالجة رسالة من {message_data['contact_name']}: {message_data['message_text'][:50]}...")

            # الحصول على الإعدادات
            print("    ⚙️ [الإعدادات] فحص إعدادات النظام...")
            db = SessionLocal()
            try:
                settings = db.query(Settings).first()
                if not settings or not settings.auto_reply_enabled:
                    print("    ⚠️ [معطل] الردود التلقائية معطلة في الإعدادات")
                    logger.info("⚠️ الردود التلقائية معطلة في الإعدادات")
                    return

                print("    ✅ [مفعل] الردود التلقائية مفعلة")

                # التحقق من الحد الأقصى للردود اليومية
                print("    📊 [الحد اليومي] فحص الحد الأقصى للردود اليومية...")
                today = datetime.now().date()
                daily_responses = db.query(AIResponse).filter(
                    AIResponse.created_at >= today,
                    AIResponse.created_at < today + timedelta(days=1)
                ).count()

                print(f"    📈 [الإحصائيات] الردود اليوم: {daily_responses}/{settings.max_daily_responses}")

                if daily_responses >= settings.max_daily_responses:
                    print(f"    ⚠️ [تجاوز الحد] تم الوصول للحد الأقصى للردود اليومية: {settings.max_daily_responses}")
                    logger.warning(f"⚠️ تم الوصول للحد الأقصى للردود اليومية: {settings.max_daily_responses}")
                    return

                # إنشاء الرد باستخدام الذكاء الاصطناعي
                print("    🧠 [الذكاء الاصطناعي] إرسال الرسالة للذكاء الاصطناعي...")
                start_time = time.time()
                
                # تحضير السياق بشكل منظم
                ai_context = {
                    "contact_name": message_data['contact_name'],
                    "company_name": settings.company_name,
                    "support_phone": settings.support_phone,
                    "support_email": settings.support_email
                }
                
                # إضافة سياق المحادثة بشكل منظم
                conversation_context = message_data.get('context', '')
                if conversation_context:
                    # تنظيف السياق من أي أخطاء محتملة
                    conversation_context = conversation_context.strip()
                    if conversation_context:
                        ai_context["conversation_context"] = conversation_context

                ai_response = await self.ai_service.generate_response(
                    message_data['message_text'],
                    contact_phone=message_data.get('phone_number'),
                    context=ai_context
                )
                response_time_ms = int((time.time() - start_time) * 1000)
                print(f"    ⚡ [سرعة الاستجابة] تم توليد الرد في {response_time_ms} مللي ثانية")

                if ai_response and ai_response.strip():
                    print(f"    💡 [الرد المولد] الرد: {ai_response[:100]}...")
                    # إرسال الرد فوراً (بدون تأخير للردود الذكية)
                    print("    📤 [الإرسال] إرسال الرد في الواتساب...")
                    logger.info("📤 إرسال الرد الذكي فوراً...")
                    success = await self._send_reply(ai_response)

                    # حفظ الرد في قاعدة البيانات
                    ai_record = AIResponse(
                        contact_phone=message_data.get('phone_number', 'unknown'),
                        incoming_message=message_data['message_text'],
                        ai_response=ai_response,
                        gpt_model_used=settings.gpt_model or "gpt-4o-mini",
                        response_time_ms=response_time_ms,
                        confidence_score=0.8  # قيمة افتراضية
                    )
                    
                    db.add(ai_record)
                    db.commit()
                    
                    if success:
                        logger.info(f"✅ تم إرسال رد ذكي إلى {message_data['contact_name']}")
                        await self._exit_current_chat()
                    else:
                        logger.error(f"❌ فشل في إرسال الرد إلى {message_data['contact_name']}")
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"❌ خطأ في معالجة الرسالة: {e}")
    
    async def _send_reply(self, reply_text: str) -> bool:
        """إرسال الرد باستخدام الخدمة الموحدة - نفس منطق الحملات الإعلانية"""
        try:
            print(f"    📤 [إرسال الرد] بدء إرسال الرد: {reply_text[:50]}...")
            logger.info(f"📤 إرسال الرد: {reply_text[:50]}...")

            # استخدام الخدمة الموحدة للإرسال - نفس الطريقة المستخدمة في الحملات
            if not self.whatsapp_service:
                print("    ❌ [خطأ] خدمة WhatsApp Web الموحدة غير متاحة")
                logger.error("❌ خدمة WhatsApp Web الموحدة غير متاحة")
                return False

            print("    ✅ [الخدمة] خدمة WhatsApp Web متاحة")

            # استخدام نفس منطق إرسال الرسائل المستخدم في الحملات
            print("    🔄 [الإرسال] بدء عملية الإرسال...")
            success = await self._send_message_unified(reply_text)

            if success:
                print("    ✅ [نجح] تم إرسال الرد بنجاح!")
                logger.info("✅ تم إرسال الرد بنجاح باستخدام الخدمة الموحدة")
                return True
            else:
                print("    ❌ [فشل] فشل في إرسال الرد")
                logger.error("❌ فشل في إرسال الرد باستخدام الخدمة الموحدة")
                return False

        except Exception as e:
            print(f"    ❌ [خطأ عام] خطأ في إرسال الرد: {e}")
            logger.error(f"❌ خطأ في إرسال الرد: {e}")
            return False

    async def _send_message_unified(self, message_text: str) -> bool:
        """إرسال رسالة بذكاء وأمان - محسن للواتساب"""
        try:
            print(f"        📝 [تحضير] إعداد إرسال الرسالة: {message_text[:30]}...")
            logger.info(f"📝 إعداد إرسال الرسالة: {message_text[:30]}...")

            # البحث عن مربع الإدخال بطرق متعددة
            print("        🔍 [البحث] البحث عن مربع الإدخال...")
            input_box = await self._find_input_box()
            if not input_box:
                print("        ❌ [فشل] لم يتم العثور على مربع الإدخال")
                logger.error("❌ لم يتم العثور على مربع الإدخال")
                return False

            print("        ✅ [وجد] تم العثور على مربع الإدخال")

            # تنظيف وتحضير النص
            print("        🧹 [تنظيف] تنظيف وتحضير النص...")
            clean_text = await self._prepare_message_text(message_text)
            if not clean_text:
                print("        ❌ [فارغ] النص فارغ بعد التنظيف")
                logger.error("❌ النص فارغ بعد التنظيف")
                return False

            print(f"        ✅ [جاهز] النص جاهز للإرسال: {clean_text[:30]}...")

            # إرسال النص بطريقة آمنة
            print("        ⌨️ [الكتابة] كتابة النص وإرساله...")
            success = await self._type_and_send_message(input_box, clean_text)

            if success:
                logger.info("✅ تم إرسال الرد بنجاح")
                return True
            else:
                logger.error("❌ فشل في إرسال الرد")
                return False

        except Exception as e:
            logger.error(f"❌ خطأ في إرسال الرسالة: {e}")
            return False

    async def _find_input_box(self):
        """البحث عن مربع الإدخال بطرق متعددة"""
        try:
            # selectors محسنة لمربع الإدخال
            input_selectors = [
                # الأساسية
                "div[data-testid='conversation-compose-box-input']",
                "div[contenteditable='true'][data-testid='conversation-compose-box-input']",

                # البديلة
                "div[role='textbox'][contenteditable='true']",
                "#main footer div[contenteditable='true']",
                "footer div[contenteditable='true']",

                # العامة
                "div[contenteditable='true'][data-tab='10']",
                "div[contenteditable='true'][spellcheck='true']",
                "div[contenteditable='true'][dir='auto']",

                # الاحتياطية
                "[data-testid='conversation-compose-box-input']",
                "div[data-testid='msg-input']",
                "div[class*='compose'] div[contenteditable='true']"
            ]

            for selector in input_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if await self._is_valid_input_box(element):
                            logger.debug(f"✅ تم العثور على مربع الإدخال: {selector}")
                            return element
                except Exception as e:
                    logger.debug(f"❌ فشل selector {selector}: {e}")
                    continue

            return None

        except Exception as e:
            logger.error(f"❌ خطأ في البحث عن مربع الإدخال: {e}")
            return None

    async def _is_valid_input_box(self, element) -> bool:
        """فحص صحة مربع الإدخال"""
        try:
            # فحص الرؤية والتفعيل
            if not (element.is_displayed() and element.is_enabled()):
                return False

            # فحص الحجم
            size = element.size
            if size['height'] < 10 or size['width'] < 50:
                return False

            # فحص contenteditable
            contenteditable = element.get_attribute('contenteditable')
            if contenteditable != 'true':
                return False

            # فحص الموقع (يجب أن يكون في أسفل الشاشة)
            location = element.location
            window_height = self.driver.execute_script("return window.innerHeight;")
            if location['y'] < window_height * 0.5:  # يجب أن يكون في النصف السفلي
                return False

            return True

        except:
            return False

    async def _prepare_message_text(self, text: str) -> str:
        """تنظيف وتحضير النص للإرسال"""
        try:
            # إزالة المسافات الزائدة
            clean_text = text.strip()

            # إزالة الأسطر الفارغة المتعددة
            import re
            clean_text = re.sub(r'\n\s*\n', '\n\n', clean_text)

            # التأكد من عدم وجود رموز خطيرة
            dangerous_chars = ['<script', '<iframe', 'javascript:', 'data:']
            for char in dangerous_chars:
                if char.lower() in clean_text.lower():
                    logger.warning(f"⚠️ تم العثور على رمز خطير: {char}")
                    clean_text = clean_text.replace(char, '')

            # قطع النص إذا كان طويلاً جداً
            max_length = 4000  # حد أقصى للرسالة
            if len(clean_text) > max_length:
                clean_text = clean_text[:max_length] + "..."
                logger.warning(f"⚠️ تم قطع النص إلى {max_length} حرف")

            return clean_text

        except Exception as e:
            logger.error(f"❌ خطأ في تحضير النص: {e}")
            return text  # إرجاع النص الأصلي في حالة الخطأ

    async def _type_and_send_message(self, input_box, message_text: str) -> bool:
        """كتابة وإرسال الرسالة بطريقة آمنة"""
        try:
            # التركيز على مربع الإدخال
            await self._focus_input_box(input_box)

            # مسح المحتوى الحالي
            await self._clear_input_box(input_box)

            # كتابة النص
            success = await self._type_text_safely(input_box, message_text)
            if not success:
                return False

            # إرسال الرسالة
            return await self._send_message_safely(input_box)

        except Exception as e:
            logger.error(f"❌ خطأ في كتابة وإرسال الرسالة: {e}")
            return False

    async def _focus_input_box(self, input_box):
        """التركيز على مربع الإدخال"""
        try:
            print("            🎯 [التركيز] التركيز على مربع الإدخال...")

            # طرق متعددة للتركيز
            methods = [
                ("النقر العادي", lambda: input_box.click()),
                ("JavaScript Focus", lambda: self.driver.execute_script("arguments[0].focus();", input_box)),
                ("JavaScript Click", lambda: self.driver.execute_script("arguments[0].click();", input_box)),
                ("ActionChains", lambda: ActionChains(self.driver).move_to_element(input_box).click().perform()),
                ("Scroll and Click", lambda: (
                    self.driver.execute_script("arguments[0].scrollIntoView(true);", input_box),
                    input_box.click()
                ))
            ]

            for method_name, method in methods:
                try:
                    print(f"            🔄 [محاولة] {method_name}...")
                    method()
                    await asyncio.sleep(0.5)

                    # فحص إذا تم التركيز
                    is_focused = self.driver.execute_script("return document.activeElement === arguments[0];", input_box)
                    if is_focused:
                        print(f"            ✅ [نجح] تم التركيز بـ {method_name}")
                        break
                    else:
                        print(f"            ⚠️ [لم ينجح] {method_name} لم يحقق التركيز")

                except Exception as e:
                    print(f"            ❌ [فشل] {method_name}: {e}")
                    continue

        except Exception as e:
            print(f"            ❌ [خطأ عام] خطأ في التركيز: {e}")
            logger.debug(f"❌ خطأ في التركيز: {e}")

    async def _clear_input_box(self, input_box):
        """مسح مربع الإدخال"""
        try:
            # طرق متعددة للمسح
            methods = [
                lambda: input_box.clear(),
                lambda: self.driver.execute_script("arguments[0].innerHTML = '';", input_box),
                lambda: self.driver.execute_script("arguments[0].textContent = '';", input_box),
                lambda: input_box.send_keys(Keys.CONTROL + "a", Keys.DELETE)
            ]

            for method in methods:
                try:
                    method()
                    await asyncio.sleep(0.2)
                    # فحص إذا تم المسح
                    content = self.driver.execute_script("return arguments[0].textContent || arguments[0].innerText;", input_box)
                    if not content.strip():
                        break
                except:
                    continue

        except Exception as e:
            logger.debug(f"❌ خطأ في المسح: {e}")

    async def _type_text_safely(self, input_box, text: str) -> bool:
        """كتابة النص بطريقة آمنة ومحاكاة الكتابة الطبيعية"""
        try:
            print(f"            ⌨️ [كتابة النص] بدء كتابة: {text[:30]}...")

            # طريقة 1: كتابة النص حرف بحرف (محاكاة الكتابة الطبيعية)
            try:
                print("            🔄 [المحاولة 1] كتابة حرف بحرف...")

                # مسح المحتوى
                input_box.clear()
                await asyncio.sleep(0.2)

                # كتابة النص حرف بحرف (محاكاة الكتابة الطبيعية)
                import random
                from selenium.webdriver.common.keys import Keys

                for char in text:
                    # إذا كان الحرف هو سطر جديد، استخدم Shift+Enter بدلاً من Enter
                    if char == '\n':
                        input_box.send_keys(Keys.SHIFT + Keys.ENTER)
                    else:
                        input_box.send_keys(char)

                    # تأخير عشوائي بين 0.05 و 0.15 ثانية لمحاكاة الكتابة البشرية
                    delay = random.uniform(0.05, 0.15)
                    await asyncio.sleep(delay)

                await asyncio.sleep(0.3)

                # فحص النتيجة
                content = self.driver.execute_script("return arguments[0].textContent || arguments[0].innerText;", input_box)
                if content.strip():
                    print("            ✅ [نجح] تم كتابة النص حرف بحرف")
                    return True

            except Exception as e:
                print(f"            ❌ [فشل] كتابة حرف بحرف: {e}")

            # طريقة 2: استخدام JavaScript كبديل
            try:
                print("            🔄 [المحاولة 2] استخدام JavaScript...")

                # مسح المحتوى أولاً
                self.driver.execute_script("arguments[0].innerHTML = '';", input_box)
                await asyncio.sleep(0.2)

                # كتابة النص بطريقة تحاكي الكتابة الطبيعية
                self.driver.execute_script("""
                    var element = arguments[0];
                    var text = arguments[1];

                    // التركيز على العنصر
                    element.focus();

                    // مسح المحتوى
                    element.innerHTML = '';
                    element.textContent = '';

                    // إنشاء نص جديد
                    var textNode = document.createTextNode(text);
                    element.appendChild(textNode);

                    // إطلاق أحداث الإدخال
                    var inputEvent = new Event('input', { bubbles: true, cancelable: true });
                    var changeEvent = new Event('change', { bubbles: true, cancelable: true });
                    var keyupEvent = new KeyboardEvent('keyup', { bubbles: true, cancelable: true });

                    element.dispatchEvent(inputEvent);
                    element.dispatchEvent(changeEvent);
                    element.dispatchEvent(keyupEvent);

                    // التأكد من التركيز
                    element.focus();
                """, input_box, text)

                await asyncio.sleep(0.5)

                # فحص إذا تم كتابة النص
                content = self.driver.execute_script("return arguments[0].textContent || arguments[0].innerText;", input_box)
                if content.strip() == text.strip():
                    print("            ✅ [نجح] تم كتابة النص باستخدام JavaScript")
                    return True

            except Exception as e:
                print(f"            ❌ [فشل] JavaScript: {e}")

            # طريقة 3: استخدام ActionChains كبديل أخير
            try:
                print("            🔄 [المحاولة 3] استخدام ActionChains...")

                actions = ActionChains(self.driver)
                actions.click(input_box)
                actions.key_down(Keys.CONTROL).send_keys('a').key_up(Keys.CONTROL)  # تحديد الكل
                actions.send_keys(Keys.DELETE)  # حذف
                actions.send_keys(text)  # كتابة النص الجديد
                actions.perform()

                await asyncio.sleep(0.5)

                # فحص النتيجة
                content = self.driver.execute_script("return arguments[0].textContent || arguments[0].innerText;", input_box)
                if content.strip():
                    print("            ✅ [نجح] تم كتابة النص باستخدام ActionChains")
                    return True

            except Exception as e:
                print(f"            ❌ [فشل] ActionChains: {e}")

            print("            ❌ [فشل تام] فشل في جميع طرق الكتابة")
            return False

        except Exception as e:
            print(f"            ❌ [خطأ عام] خطأ في كتابة النص: {e}")
            logger.error(f"❌ خطأ في كتابة النص: {e}")
            return False

    async def _send_message_safely(self, input_box) -> bool:
        """إرسال الرسالة بطريقة آمنة"""
        try:
            print("            📤 [إرسال] بدء إرسال الرسالة...")

            # فحص وجود النص قبل الإرسال
            content_before = self.driver.execute_script("return arguments[0].textContent || arguments[0].innerText;", input_box)
            if not content_before.strip():
                print("            ❌ [فارغ] مربع الإدخال فارغ - لا يوجد نص للإرسال")
                return False

            print(f"            📝 [المحتوى] النص الموجود: {content_before[:30]}...")

            # طرق متعددة للإرسال
            send_methods = [
                ("Enter Key", lambda: input_box.send_keys(Keys.ENTER)),
                ("زر الإرسال", lambda: self._click_send_button()),
                ("JavaScript Enter", lambda: self.driver.execute_script("""
                    var element = arguments[0];
                    element.focus();
                    var event = new KeyboardEvent('keydown', {
                        key: 'Enter',
                        code: 'Enter',
                        keyCode: 13,
                        which: 13,
                        bubbles: true,
                        cancelable: true
                    });
                    element.dispatchEvent(event);

                    var keyupEvent = new KeyboardEvent('keyup', {
                        key: 'Enter',
                        code: 'Enter',
                        keyCode: 13,
                        which: 13,
                        bubbles: true,
                        cancelable: true
                    });
                    element.dispatchEvent(keyupEvent);
                """, input_box)),
                ("ActionChains Enter", lambda: ActionChains(self.driver).send_keys(Keys.ENTER).perform())
            ]

            for i, (method_name, method) in enumerate(send_methods):
                try:
                    print(f"            🔄 [المحاولة {i+1}] {method_name}...")
                    method()
                    await asyncio.sleep(2)  # انتظار أطول للتأكد من الإرسال

                    # فحص إذا تم الإرسال (مربع الإدخال فارغ أو تغير المحتوى)
                    content_after = self.driver.execute_script("return arguments[0].textContent || arguments[0].innerText;", input_box)

                    if not content_after.strip() or content_after != content_before:
                        print(f"            ✅ [نجح] تم الإرسال بـ {method_name}")
                        print(f"            📊 [النتيجة] المحتوى قبل: '{content_before[:20]}...' بعد: '{content_after[:20]}...'")
                        return True
                    else:
                        print(f"            ⚠️ [لم يتغير] المحتوى لم يتغير بعد {method_name}")

                except Exception as e:
                    print(f"            ❌ [فشل] {method_name}: {e}")
                    continue

            print("            ❌ [فشل تام] فشل في جميع طرق الإرسال")
            return False

        except Exception as e:
            print(f"            ❌ [خطأ عام] خطأ في إرسال الرسالة: {e}")
            logger.error(f"❌ خطأ في إرسال الرسالة: {e}")
            return False

    def _click_send_button(self):
        """البحث عن زر الإرسال والنقر عليه"""
        try:
            print("                🔍 [زر الإرسال] البحث عن زر الإرسال...")

            send_selectors = [
                "button[data-testid='send']",
                "span[data-testid='send']",
                "button[aria-label*='Send']",
                "button[aria-label*='إرسال']",
                "div[role='button'][aria-label*='Send']",
                "div[role='button'][aria-label*='إرسال']",
                "button[data-tab='11']",  # زر الإرسال الجديد
                "span[data-icon='send']",  # أيقونة الإرسال
                "div[data-icon='send']",
                "button svg[data-icon='send']",  # SVG الإرسال
                "footer button:last-child",  # آخر زر في الفوتر
                "div[data-testid='compose-btn-send']"  # زر الإرسال البديل
            ]

            for i, selector in enumerate(send_selectors):
                try:
                    print(f"                🔄 [محاولة {i+1}] البحث بـ: {selector}")
                    send_buttons = self.driver.find_elements(By.CSS_SELECTOR, selector)

                    for button in send_buttons:
                        if button and button.is_displayed() and button.is_enabled():
                            print(f"                ✅ [وجد] تم العثور على زر الإرسال")
                            button.click()
                            print(f"                👆 [نقر] تم النقر على زر الإرسال")
                            return True

                except Exception as e:
                    print(f"                ❌ [فشل] {selector}: {e}")
                    continue

            print("                ❌ [لم يوجد] لم يتم العثور على زر الإرسال")
            return False

        except Exception as e:
            print(f"                ❌ [خطأ عام] خطأ في البحث عن زر الإرسال: {e}")
            return False

    async def _exit_current_chat(self):
        """الخروج من المحادثة بالضغط على ESC"""
        try:
            from selenium.webdriver.common.keys import Keys
            body = self.driver.find_element(By.TAG_NAME, "body")
            body.send_keys(Keys.ESCAPE)
            await asyncio.sleep(1)
            return True
        except Exception as e:
            logger.error(f"❌ خطأ في الخروج من المحادثة: {e}")
            return False

# إنشاء instance عام للخدمة
auto_reply_service = None

def get_auto_reply_service(whatsapp_manager):
    """الحصول على خدمة الردود التلقائية"""
    global auto_reply_service
    if auto_reply_service is None:
        auto_reply_service = AutoReplyService(whatsapp_manager)
        logger.info("🆕 تم إنشاء instance جديد من خدمة الردود التلقائية")
    else:
        # تحديث whatsapp_manager في حالة تغييره
        auto_reply_service.whatsapp_manager = whatsapp_manager
        logger.debug("♻️ استخدام instance موجود من خدمة الردود التلقائية")
    return auto_reply_service
