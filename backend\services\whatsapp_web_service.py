"""
WhatsApp Web Service
خدمة إرسال الرسائل عبر WhatsApp Web باستخدام Selenium
"""

import time
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import re

logger = logging.getLogger(__name__)

class WhatsAppWebService:
    """خدمة إرسال الرسائل عبر WhatsApp Web"""
    
    def __init__(self, whatsapp_manager):
        self.whatsapp_manager = whatsapp_manager
        self.driver = None

    async def test_connection(self) -> dict:
        """اختبار الاتصال مع WhatsApp Web"""
        try:
            logger.info("🔍 اختبار الاتصال مع WhatsApp Web...")

            # فحص حالة whatsapp_manager
            status = self.whatsapp_manager.get_status()

            result = {
                'success': False,
                'session_active': status.get('session_active', False),
                'logged_in': status.get('logged_in', False),
                'driver_available': self.whatsapp_manager.driver is not None,
                'message': ''
            }

            if not status.get('session_active', False):
                result['message'] = 'الجلسة غير نشطة'
                logger.warning("⚠️ الجلسة غير نشطة")
                return result

            if not status.get('logged_in', False):
                result['message'] = 'غير مسجل دخول'
                logger.warning("⚠️ غير مسجل دخول")
                return result

            if not self.whatsapp_manager.driver:
                result['message'] = 'المتصفح غير متاح'
                logger.warning("⚠️ المتصفح غير متاح")
                return result

            # تحديث driver
            self.driver = self.whatsapp_manager.driver

            # اختبار الوصول للصفحة
            try:
                current_url = self.driver.current_url
                if 'web.whatsapp.com' in current_url:
                    result['success'] = True
                    result['message'] = 'الاتصال ناجح'
                    logger.info("✅ اختبار الاتصال ناجح")
                else:
                    result['message'] = f'URL غير صحيح: {current_url}'
                    logger.warning(f"⚠️ URL غير صحيح: {current_url}")
            except Exception as e:
                result['message'] = f'خطأ في الوصول للمتصفح: {e}'
                logger.error(f"❌ خطأ في الوصول للمتصفح: {e}")

            return result

        except Exception as e:
            logger.error(f"❌ خطأ في اختبار الاتصال: {e}")
            return {
                'success': False,
                'session_active': False,
                'logged_in': False,
                'driver_available': False,
                'message': f'خطأ في الاختبار: {e}'
            }

    def _ensure_logged_in(self) -> bool:
        """التأكد من تسجيل الدخول"""
        # استخدام نفس منطق whatsapp_manager للتحقق من تسجيل الدخول
        status = self.whatsapp_manager.get_status()

        logger.info(f"🔍 فحص حالة تسجيل الدخول: session_active={status.get('session_active')}, logged_in={status.get('logged_in')}")

        # إذا كان whatsapp_manager يقول أنه مُسجل دخول، فهو كذلك
        if status.get('session_active', False) and status.get('logged_in', False):
            self.driver = self.whatsapp_manager.driver
            logger.info("✅ WhatsApp Web مُسجل دخول ونشط")
            return True

        logger.warning("⚠️ WhatsApp Web غير مُسجل دخول")
        return False
    
    def _format_phone_number(self, phone: str) -> str:
        """تنسيق رقم الهاتف لـ WhatsApp Web"""
        original_phone = phone

        # إزالة جميع الرموز غير الرقمية
        phone = re.sub(r'[^\d]', '', phone)

        # إزالة أي أصفار في البداية
        phone = phone.lstrip('0')

        # إضافة رمز الدولة إذا لم يكن موجوداً
        if not phone.startswith('966') and len(phone) == 9:
            phone = '966' + phone
        elif not phone.startswith('966') and len(phone) == 10 and phone.startswith('0'):
            phone = '966' + phone[1:]

        logger.info(f"📱 تنسيق الرقم: {original_phone} -> {phone}")
        return phone
    
    def _search_contact(self, phone: str) -> bool:
        """البحث عن جهة اتصال باستخدام URL مباشر"""
        try:
            logger.info(f"🔍 محاولة الوصول المباشر للرقم: {phone}")

            # طريقة 1: استخدام URL مباشر لفتح دردشة مع الرقم
            formatted_phone = phone.replace('+', '').replace(' ', '').replace('-', '')
            whatsapp_url = f"https://web.whatsapp.com/send?phone={formatted_phone}"

            logger.info(f"🌐 فتح URL مباشر: {whatsapp_url}")
            self.driver.get(whatsapp_url)

            # انتظار تحميل الصفحة
            time.sleep(5)

            # التحقق من وجود مربع الكتابة (يعني نجح فتح الدردشة)
            try:
                message_box = WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR,
                        "div[contenteditable='true'][data-tab='10'], div[data-testid='conversation-compose-box-input']"))
                )
                logger.info(f"✅ تم فتح الدردشة مع {phone} بنجاح")
                return True
            except TimeoutException:
                logger.warning(f"⚠️ فشل في فتح الدردشة مباشرة، محاولة الطريقة التقليدية...")

                # طريقة 2: العودة للصفحة الرئيسية والبحث التقليدي
                self.driver.get("https://web.whatsapp.com")
                time.sleep(3)

                return self._traditional_search(phone)

        except Exception as e:
            logger.error(f"❌ خطأ في البحث عن جهة الاتصال: {e}")
            return False

    def _traditional_search(self, phone: str) -> bool:
        """البحث التقليدي عن جهة الاتصال"""
        try:
            logger.info(f"🔍 البحث التقليدي عن الرقم: {phone}")

            # البحث عن مربع البحث
            search_selectors = [
                "div[contenteditable='true'][data-tab='3']",
                "[data-testid='chat-list-search']",
                "div[title='Search input textbox']",
                "div[role='textbox'][contenteditable='true']"
            ]

            search_input = None
            for selector in search_selectors:
                try:
                    search_input = WebDriverWait(self.driver, 5).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                    )
                    logger.info(f"✅ تم العثور على مربع البحث باستخدام: {selector}")
                    break
                except TimeoutException:
                    continue

            if not search_input:
                logger.error("❌ لم يتم العثور على مربع البحث")
                return self._create_new_chat(phone)

            # النقر وكتابة الرقم بطريقة طبيعية
            search_input.click()
            time.sleep(1)
            search_input.clear()

            # كتابة الرقم حرف بحرف لمحاكاة الكتابة البشرية
            import random
            for char in phone:
                search_input.send_keys(char)
                # تأخير عشوائي بين 0.05 و 0.15 ثانية
                delay = random.uniform(0.05, 0.15)
                time.sleep(delay)

            logger.info(f"⌨️ تم كتابة الرقم: {phone}")

            time.sleep(3)  # انتظار نتائج البحث

            # البحث عن النتائج
            result_selectors = [
                "div[data-testid='cell-frame-container']:first-child",
                "[data-testid='chat-list'] > div:first-child",
                "div[role='listitem']:first-child"
            ]

            for selector in result_selectors:
                try:
                    first_result = WebDriverWait(self.driver, 5).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                    )
                    first_result.click()
                    logger.info(f"✅ تم النقر على النتيجة الأولى")
                    return True
                except TimeoutException:
                    continue

            logger.warning(f"⚠️ لم توجد نتائج، محاولة إنشاء دردشة جديدة...")
            return self._create_new_chat(phone)

        except Exception as e:
            logger.error(f"❌ خطأ في البحث التقليدي: {e}")
            return False
    
    def _create_new_chat(self, phone: str) -> bool:
        """إنشاء دردشة جديدة باستخدام طرق متعددة"""
        try:
            logger.info(f"🆕 محاولة إنشاء دردشة جديدة مع {phone}...")

            # طريقة 1: استخدام URL مباشر للدردشة الجديدة
            formatted_phone = phone.replace('+', '').replace(' ', '').replace('-', '')
            new_chat_url = f"https://web.whatsapp.com/send?phone={formatted_phone}&text="

            logger.info(f"🌐 محاولة URL مباشر للدردشة الجديدة: {new_chat_url}")
            self.driver.get(new_chat_url)
            time.sleep(5)

            # التحقق من نجاح فتح الدردشة
            try:
                message_box = WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR,
                        "div[contenteditable='true'][data-tab='10'], div[data-testid='conversation-compose-box-input']"))
                )
                logger.info(f"✅ تم فتح دردشة جديدة مع {phone} بنجاح")
                return True
            except TimeoutException:
                logger.warning(f"⚠️ فشل في فتح الدردشة الجديدة مباشرة")

                # طريقة 2: استخدام زر الدردشة الجديدة
                return self._manual_new_chat(phone)

        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء دردشة جديدة: {e}")
            return False

    def _manual_new_chat(self, phone: str) -> bool:
        """إنشاء دردشة جديدة يدوياً"""
        try:
            logger.info(f"🔧 محاولة إنشاء دردشة جديدة يدوياً...")

            # العودة للصفحة الرئيسية
            self.driver.get("https://web.whatsapp.com")
            time.sleep(3)

            # البحث عن زر الدردشة الجديدة
            new_chat_selectors = [
                "div[title='New chat']",
                "[data-testid='compose-btn']",
                "div[role='button'][aria-label*='New']",
                "span[data-icon='new-chat-outline']"
            ]

            for selector in new_chat_selectors:
                try:
                    new_chat_button = WebDriverWait(self.driver, 5).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                    )
                    new_chat_button.click()
                    logger.info(f"✅ تم النقر على زر الدردشة الجديدة")
                    time.sleep(2)
                    break
                except TimeoutException:
                    continue
            else:
                logger.error("❌ لم يتم العثور على زر الدردشة الجديدة")
                return False

            # البحث عن مربع البحث في نافذة الدردشة الجديدة
            search_selectors = [
                "div[contenteditable='true'][data-tab='3']",
                "input[type='text']",
                "div[role='textbox']"
            ]

            for selector in search_selectors:
                try:
                    search_input = WebDriverWait(self.driver, 5).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                    )
                    search_input.click()
                    search_input.clear()

                    # كتابة الرقم حرف بحرف لمحاكاة الكتابة البشرية
                    import random
                    for char in phone:
                        search_input.send_keys(char)
                        # تأخير عشوائي بين 0.05 و 0.15 ثانية
                        delay = random.uniform(0.05, 0.15)
                        time.sleep(delay)

                    logger.info(f"⌨️ تم كتابة الرقم: {phone}")
                    time.sleep(3)
                    break
                except TimeoutException:
                    continue
            else:
                logger.error("❌ لم يتم العثور على مربع البحث")
                return False

            # البحث عن النتائج
            result_selectors = [
                "div[data-testid='cell-frame-container']:first-child",
                "div[role='listitem']:first-child",
                "div[data-testid='chat']:first-child"
            ]

            for selector in result_selectors:
                try:
                    first_result = WebDriverWait(self.driver, 5).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                    )
                    first_result.click()
                    logger.info(f"✅ تم النقر على النتيجة الأولى")
                    return True
                except TimeoutException:
                    continue

            # محاولة أخيرة: البحث عن خيار "إرسال رسالة إلى رقم جديد"
            try:
                logger.info(f"🔍 محاولة البحث عن خيار الرقم الجديد...")
                new_number_selectors = [
                    f"//span[contains(text(), '{phone}')]",
                    f"//div[contains(text(), '{phone}')]",
                    "//span[contains(text(), 'Send message to')]",
                    "//div[contains(text(), 'إرسال رسالة إلى')]"
                ]

                for xpath in new_number_selectors:
                    try:
                        new_number_option = WebDriverWait(self.driver, 3).until(
                            EC.element_to_be_clickable((By.XPATH, xpath))
                        )
                        new_number_option.click()
                        logger.info(f"✅ تم اختيار خيار الرقم الجديد")
                        return True
                    except TimeoutException:
                        continue

                logger.error(f"❌ فشل في العثور على أي خيار للرقم: {phone}")
                return False

            except Exception as e:
                logger.error(f"❌ خطأ في البحث عن خيار الرقم الجديد: {e}")
                return False

        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء دردشة جديدة يدوياً: {e}")
            return False
    
    def _send_message_to_current_chat(self, message: str) -> bool:
        """إرسال رسالة للدردشة الحالية"""
        try:
            logger.info(f"💬 البحث عن مربع الكتابة...")

            # محاولة عدة selectors لمربع الكتابة
            message_box_selectors = [
                "[data-testid='conversation-compose-box-input']",
                "div[contenteditable='true'][data-tab='10']",
                "div[role='textbox'][contenteditable='true']",
                "div[title='Type a message']",
                "div[data-testid='compose-box-input']"
            ]

            message_box = None
            for selector in message_box_selectors:
                try:
                    message_box = WebDriverWait(self.driver, 3).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                    )
                    logger.info(f"✅ تم العثور على مربع الكتابة باستخدام: {selector}")
                    break
                except TimeoutException:
                    logger.info(f"⚠️ لم يتم العثور على مربع الكتابة باستخدام: {selector}")
                    continue

            if not message_box:
                logger.error("❌ لم يتم العثور على مربع الكتابة")
                return False

            # كتابة الرسالة بطريقة طبيعية
            message_box.clear()

            # كتابة النص حرف بحرف لمحاكاة الكتابة البشرية
            import random
            for char in message:
                message_box.send_keys(char)
                # تأخير عشوائي بين 0.05 و 0.15 ثانية
                delay = random.uniform(0.05, 0.15)
                time.sleep(delay)

            logger.info(f"⌨️ تم كتابة الرسالة: {message[:50]}...")

            # إرسال الرسالة
            logger.info(f"🔍 البحث عن زر الإرسال...")

            # محاولة عدة selectors لزر الإرسال
            send_button_selectors = [
                "span[data-testid='send']",
                "button[data-testid='send']",
                "[data-testid='send']",
                "button[aria-label='Send']",
                "button[aria-label='إرسال']",
                "div[role='button'][title='Send']",
                "div[role='button'][aria-label='Send']",
                "span[data-icon='send']",
                "button[type='submit']",
                "div[data-tab='11']"
            ]

            send_button = None
            for selector in send_button_selectors:
                try:
                    send_button = WebDriverWait(self.driver, 5).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                    )
                    logger.info(f"✅ تم العثور على زر الإرسال باستخدام: {selector}")
                    break
                except TimeoutException:
                    logger.info(f"⚠️ لم يتم العثور على زر الإرسال باستخدام: {selector}")
                    continue

            if send_button:
                send_button.click()
                logger.info(f"✅ تم النقر على زر الإرسال")
            else:
                # إذا لم يتم العثور على زر الإرسال، جرب الضغط على Enter
                logger.warning("⚠️ لم يتم العثور على زر الإرسال، محاولة الضغط على Enter...")
                try:
                    from selenium.webdriver.common.keys import Keys
                    message_box.send_keys(Keys.ENTER)
                    logger.info(f"✅ تم الضغط على Enter لإرسال الرسالة")
                except Exception as e:
                    logger.error(f"❌ فشل في الضغط على Enter: {e}")
                    return False

            time.sleep(2)  # انتظار للتأكد من الإرسال
            logger.info(f"🎉 تم إرسال الرسالة بنجاح!")
            return True

        except Exception as e:
            logger.error(f"❌ خطأ في إرسال الرسالة: {e}")
            return False
    
    async def send_text_message(
        self,
        to_phone: str,
        message: str,
        **kwargs
    ) -> Dict[str, Any]:
        """إرسال رسالة نصية"""

        logger.info(f"🚀 بدء إرسال رسالة إلى {to_phone}")

        if not self._ensure_logged_in():
            logger.error("❌ WhatsApp Web غير مُسجل دخول")
            return {
                "success": False,
                "error": "غير مسجل دخول في WhatsApp Web",
                "message_id": None
            }
        
        try:
            formatted_phone = self._format_phone_number(to_phone)
            logger.info(f"📱 رقم مُنسق: {formatted_phone}")

            # البحث عن جهة الاتصال أو إنشاء دردشة جديدة
            logger.info(f"🔍 البحث عن جهة الاتصال...")
            if not self._search_contact(formatted_phone):
                logger.error(f"❌ لم يتم العثور على جهة اتصال للرقم: {formatted_phone}")
                return {
                    "success": False,
                    "error": f"لم يتم العثور على جهة اتصال للرقم: {formatted_phone}",
                    "message_id": None
                }

            logger.info(f"✅ تم العثور على جهة الاتصال، إرسال الرسالة...")
            # إرسال الرسالة
            if self._send_message_to_current_chat(message):
                logger.info(f"✅ تم إرسال الرسالة بنجاح إلى {formatted_phone}")
                return {
                    "success": True,
                    "message_id": f"web_{int(time.time())}",
                    "to": formatted_phone,
                    "message": message,
                    "timestamp": datetime.now().isoformat()
                }
            else:
                logger.error(f"❌ فشل في إرسال الرسالة إلى {formatted_phone}")
                return {
                    "success": False,
                    "error": "فشل في إرسال الرسالة",
                    "message_id": None
                }
                
        except Exception as e:
            logger.error(f"خطأ في إرسال الرسالة: {e}")
            return {
                "success": False,
                "error": f"خطأ: {str(e)}",
                "message_id": None
            }
    
    async def send_bulk_messages(
        self, 
        contacts: List[Dict[str, str]], 
        message_template: str,
        delay_between_messages: int = 3
    ) -> Dict[str, Any]:
        """إرسال رسائل جماعية"""
        
        if not self._ensure_logged_in():
            return {
                "success": False,
                "error": "غير مسجل دخول في WhatsApp Web",
                "sent_count": 0,
                "failed_count": 0,
                "results": []
            }
        
        results = []
        sent_count = 0
        failed_count = 0
        
        for contact in contacts:
            try:
                phone = contact.get('phone', '')
                name = contact.get('name', 'العميل')
                
                # تخصيص الرسالة
                personalized_message = message_template.replace('{الاسم}', name)
                personalized_message = personalized_message.replace('{name}', name)
                
                # إرسال الرسالة
                result = await self.send_text_message(phone, personalized_message)
                
                if result['success']:
                    sent_count += 1
                    logger.info(f"تم إرسال رسالة إلى {phone}")
                else:
                    failed_count += 1
                    logger.error(f"فشل إرسال رسالة إلى {phone}: {result.get('error')}")
                
                results.append({
                    "phone": phone,
                    "name": name,
                    "success": result['success'],
                    "error": result.get('error'),
                    "message_id": result.get('message_id')
                })
                
                # تأخير بين الرسائل لتجنب الحظر
                if delay_between_messages > 0:
                    time.sleep(delay_between_messages)
                    
            except Exception as e:
                failed_count += 1
                logger.error(f"خطأ في إرسال رسالة إلى {contact}: {e}")
                results.append({
                    "phone": contact.get('phone', ''),
                    "name": contact.get('name', ''),
                    "success": False,
                    "error": str(e),
                    "message_id": None
                })
        
        return {
            "success": True,
            "sent_count": sent_count,
            "failed_count": failed_count,
            "total_count": len(contacts),
            "results": results
        }
    
    def get_chat_list(self) -> List[Dict[str, str]]:
        """الحصول على قائمة الدردشات"""
        if not self._ensure_logged_in():
            return []
        
        try:
            chats = []
            chat_elements = self.driver.find_elements(By.CSS_SELECTOR, "[data-testid='chat-list'] [data-testid='cell-frame-container']")
            
            for chat_element in chat_elements[:10]:  # أول 10 دردشات
                try:
                    name_element = chat_element.find_element(By.CSS_SELECTOR, "[data-testid='conversation-info-header']")
                    name = name_element.text if name_element else "غير معروف"
                    
                    chats.append({
                        "name": name,
                        "type": "chat"
                    })
                except:
                    continue
            
            return chats
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على قائمة الدردشات: {e}")
            return []
