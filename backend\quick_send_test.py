#!/usr/bin/env python3
"""
اختبار سريع لإرسال حملة
"""

import requests
import json

def test_send_campaign():
    """اختبار إرسال حملة عبر API"""
    
    print("🧪 اختبار إرسال حملة عبر API")
    print("=" * 40)
    
    base_url = "http://localhost:8000"
    campaign_id = 3  # معرف الحملة
    
    try:
        # 1. فحص حالة WhatsApp Web
        print("🔍 فحص حالة WhatsApp Web...")
        response = requests.get(f"{base_url}/api/whatsapp/status")
        if response.status_code == 200:
            status = response.json()
            print(f"   📊 الجلسة نشطة: {status.get('session_active', False)}")
            print(f"   🔐 مسجل دخول: {status.get('logged_in', False)}")
        else:
            print(f"   ❌ خطأ في فحص الحالة: {response.status_code}")
        
        # 2. فحص تفاصيل الحملة
        print(f"\n📋 فحص تفاصيل الحملة {campaign_id}...")
        response = requests.get(f"{base_url}/api/campaigns/{campaign_id}")
        if response.status_code == 200:
            campaign = response.json()
            print(f"   📝 اسم الحملة: {campaign.get('name', 'غير محدد')}")
            print(f"   📊 الحالة: {campaign.get('status', 'غير محدد')}")
            print(f"   👥 عدد جهات الاتصال: {campaign.get('total_contacts', 0)}")
        else:
            print(f"   ❌ خطأ في جلب تفاصيل الحملة: {response.status_code}")
            return
        
        # 3. فحص جهات الاتصال
        print(f"\n👥 فحص جهات اتصال الحملة...")
        response = requests.get(f"{base_url}/api/campaigns/{campaign_id}/contacts")
        if response.status_code == 200:
            contacts = response.json()
            print(f"   📊 عدد جهات الاتصال: {len(contacts)}")
            for i, contact in enumerate(contacts[:3], 1):
                print(f"   {i}. {contact.get('name', 'غير محدد')} - {contact.get('phone_number', 'غير محدد')}")
        else:
            print(f"   ❌ خطأ في جلب جهات الاتصال: {response.status_code}")
        
        # 4. محاولة إرسال الحملة
        print(f"\n🚀 محاولة إرسال الحملة {campaign_id}...")
        response = requests.post(f"{base_url}/api/campaigns/{campaign_id}/send")
        
        print(f"📊 نتيجة الإرسال:")
        print(f"   📈 كود الاستجابة: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ نجح: {result.get('message', 'تم الإرسال')}")
            print(f"   📊 معرف الحملة: {result.get('campaign_id', 'غير محدد')}")
            print(f"   👥 عدد جهات الاتصال: {result.get('total_contacts', 0)}")
        else:
            try:
                error_data = response.json()
                print(f"   ❌ خطأ: {error_data.get('detail', 'خطأ غير معروف')}")
            except:
                print(f"   ❌ خطأ: {response.text}")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
    
    print("\n" + "=" * 40)
    print("انتهى اختبار الإرسال")

if __name__ == "__main__":
    test_send_campaign()
