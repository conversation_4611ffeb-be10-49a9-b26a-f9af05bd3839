Defaulting to user installation because normal site-packages is not writeable
Requirement already satisfied: webdriver-manager in c:\users\<USER>\appdata\roaming\python\python313\site-packages (4.0.2)
Requirement already satisfied: requests in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from webdriver-manager) (2.31.0)
Requirement already satisfied: python-dotenv in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from webdriver-manager) (1.0.0)
Requirement already satisfied: packaging in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from webdriver-manager) (24.2)
Requirement already satisfied: charset-normalizer<4,>=2 in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from requests->webdriver-manager) (3.4.1)
Requirement already satisfied: idna<4,>=2.5 in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from requests->webdriver-manager) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from requests->webdriver-manager) (2.5.0)
Requirement already satisfied: certifi>=2017.4.17 in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from requests->webdriver-manager) (2025.7.14)
