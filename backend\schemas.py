from pydantic import BaseModel, validator
from typing import Optional, List, Dict, Any
from datetime import datetime

# Schemas للحملات
class CampaignBase(BaseModel):
    name: str
    message_text: str
    image_path: Optional[str] = None
    scheduled_time: Optional[datetime] = None

class CampaignCreate(CampaignBase):
    pass

class CampaignUpdate(BaseModel):
    name: Optional[str] = None
    message_text: Optional[str] = None
    image_path: Optional[str] = None
    status: Optional[str] = None
    scheduled_time: Optional[datetime] = None

class CampaignResponse(CampaignBase):
    id: int
    status: str
    total_contacts: int
    sent_count: int
    delivered_count: int
    failed_count: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

# Schemas للعملاء
class ContactBase(BaseModel):
    name: str
    phone_number: str
    email: Optional[str] = None
    custom_fields: Optional[Dict[str, Any]] = None

class ContactCreate(ContactBase):
    campaign_id: Optional[int] = None

class ContactResponse(ContactBase):
    id: int
    campaign_id: Optional[int] = None
    created_at: datetime
    
    class Config:
        from_attributes = True

# Schemas للرسائل
class MessageBase(BaseModel):
    message_text: str
    message_type: str = "campaign"

class MessageCreate(MessageBase):
    campaign_id: Optional[int] = None
    contact_id: int

class MessageResponse(MessageBase):
    id: int
    campaign_id: Optional[int] = None
    contact_id: int
    status: str
    whatsapp_message_id: Optional[str] = None
    error_message: Optional[str] = None
    sent_at: Optional[datetime] = None
    delivered_at: Optional[datetime] = None
    read_at: Optional[datetime] = None
    created_at: datetime
    
    class Config:
        from_attributes = True

# Schemas للردود التلقائية
class AIResponseBase(BaseModel):
    contact_phone: str
    incoming_message: str
    ai_response: str

class AIResponseCreate(AIResponseBase):
    gpt_model_used: str = "gpt-3.5-turbo"
    response_time_ms: Optional[int] = None
    confidence_score: Optional[float] = None

class AIResponseResponse(AIResponseBase):
    id: int
    gpt_model_used: str
    response_time_ms: Optional[int] = None
    confidence_score: Optional[float] = None
    created_at: datetime
    
    class Config:
        from_attributes = True

# Schemas للإعدادات
class SettingsBase(BaseModel):
    whatsapp_api_url: Optional[str] = None
    whatsapp_token: Optional[str] = None
    whatsapp_phone_number_id: Optional[str] = None
    openai_api_key: Optional[str] = None
    gpt_model: str = "gpt-3.5-turbo"
    gpt_prompt: Optional[str] = None
    auto_reply_enabled: bool = False
    response_delay: int = 2
    max_daily_responses: int = 1000
    campaign_response_delay: int = 40
    company_name: str = "شركتي"
    support_phone: Optional[str] = None
    support_email: Optional[str] = None

class SettingsCreate(SettingsBase):
    pass

class SettingsUpdate(BaseModel):
    whatsapp_api_url: Optional[str] = None
    whatsapp_token: Optional[str] = None
    whatsapp_phone_number_id: Optional[str] = None
    openai_api_key: Optional[str] = None
    gpt_model: Optional[str] = None
    gpt_prompt: Optional[str] = None
    auto_reply_enabled: Optional[bool] = None
    response_delay: Optional[int] = None
    max_daily_responses: Optional[int] = None
    campaign_response_delay: Optional[int] = None
    company_name: Optional[str] = None
    support_phone: Optional[str] = None
    support_email: Optional[str] = None

class SettingsResponse(SettingsBase):
    id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

# Schemas للإحصائيات
class DashboardStats(BaseModel):
    total_messages_sent: int
    auto_replies_count: int
    active_customers: int
    success_rate: float
    recent_activity: List[Dict[str, Any]]

# Schemas لرفع الملفات
class FileUploadResponse(BaseModel):
    filename: str
    file_path: str
    contacts_count: int
    message: str

# Schema لاختبار الرد التلقائي
class TestAIResponse(BaseModel):
    test_message: str

class TestAIResponseResult(BaseModel):
    original_message: str
    ai_response: str
    response_time_ms: int
    model_used: str
