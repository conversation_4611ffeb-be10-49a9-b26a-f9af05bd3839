"""
إنشاء ملف Excel نموذجي لاستيراد العملاء
"""

import pandas as pd
from datetime import datetime
import os

def create_excel_template():
    """إنشاء ملف Excel نموذجي مع بيانات تجريبية"""
    
    # البيانات النموذجية
    sample_data = [
        {
            "الاسم": "أحمد محمد",
            "رقم الهاتف": "+966501234567",
            "البريد الإلكتروني": "<EMAIL>",
            "الشركة": "شركة التقنية المتقدمة",
            "المدينة": "الرياض",
            "ملاحظات": "عميل مهتم بالخدمات التقنية"
        },
        {
            "الاسم": "فاطمة أحمد",
            "رقم الهاتف": "966502345678",
            "البريد الإلكتروني": "<EMAIL>",
            "الشركة": "مؤسسة الإبداع",
            "المدينة": "جدة",
            "ملاحظات": "تحتاج عرض سعر للحملات الإعلانية"
        },
        {
            "الاسم": "محمد علي",
            "رقم الهاتف": "0503456789",
            "البريد الإلكتروني": "<EMAIL>",
            "الشركة": "شركة النجاح التجارية",
            "المدينة": "الدمام",
            "ملاحظات": "عميل حالي - تجديد الخدمة"
        },
        {
            "الاسم": "نورا سالم",
            "رقم الهاتف": "+966504567890",
            "البريد الإلكتروني": "<EMAIL>",
            "الشركة": "ستارت أب للتكنولوجيا",
            "المدينة": "المدينة المنورة",
            "ملاحظات": "شركة ناشئة - خصم خاص"
        },
        {
            "الاسم": "خالد الأحمد",
            "رقم الهاتف": "966505678901",
            "البريد الإلكتروني": "<EMAIL>",
            "الشركة": "مجموعة الأحمد التجارية",
            "المدينة": "مكة المكرمة",
            "ملاحظات": "عميل VIP - أولوية عالية"
        },
        {
            "الاسم": "سارة محمود",
            "رقم الهاتف": "0506789012",
            "البريد الإلكتروني": "<EMAIL>",
            "الشركة": "استشارات الأعمال المتطورة",
            "المدينة": "الطائف",
            "ملاحظات": "مهتمة بحلول الذكاء الاصطناعي"
        },
        {
            "الاسم": "عبدالله الزهراني",
            "رقم الهاتف": "+966507890123",
            "البريد الإلكتروني": "<EMAIL>",
            "الشركة": "سلسلة متاجر الزهراني",
            "المدينة": "أبها",
            "ملاحظات": "يحتاج حملة تسويقية للعيد"
        },
        {
            "الاسم": "ريم الفيصل",
            "رقم الهاتف": "966508901234",
            "البريد الإلكتروني": "<EMAIL>",
            "الشركة": "بوتيك الأناقة",
            "المدينة": "الخبر",
            "ملاحظات": "عميلة جديدة - تجربة مجانية"
        },
        {
            "الاسم": "يوسف الغامدي",
            "رقم الهاتف": "0509012345",
            "البريد الإلكتروني": "<EMAIL>",
            "الشركة": "مقاولات الغامدي",
            "المدينة": "تبوك",
            "ملاحظات": "مشروع كبير - متابعة أسبوعية"
        },
        {
            "الاسم": "هند العتيبي",
            "رقم الهاتف": "+966500123456",
            "البريد الإلكتروني": "<EMAIL>",
            "الشركة": "معهد التعليم المتقدم",
            "المدينة": "القصيم",
            "ملاحظات": "قطاع تعليمي - خصم 20%"
        }
    ]
    
    # إنشاء DataFrame
    df = pd.DataFrame(sample_data)
    
    # إنشاء مجلد للملفات النموذجية
    templates_dir = "templates"
    if not os.path.exists(templates_dir):
        os.makedirs(templates_dir)
    
    # حفظ الملف
    filename = f"{templates_dir}/نموذج_استيراد_العملاء.xlsx"
    
    # إنشاء ExcelWriter مع تنسيق
    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
        # كتابة البيانات
        df.to_excel(writer, sheet_name='العملاء', index=False)
        
        # الحصول على workbook و worksheet
        workbook = writer.book
        worksheet = writer.sheets['العملاء']
        
        # تنسيق الرأس
        from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
        
        # تنسيق خلايا الرأس
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        header_alignment = Alignment(horizontal="center", vertical="center")
        
        # تطبيق التنسيق على الرأس
        for col in range(1, len(df.columns) + 1):
            cell = worksheet.cell(row=1, column=col)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = header_alignment
        
        # تعديل عرض الأعمدة
        column_widths = {
            'A': 20,  # الاسم
            'B': 18,  # رقم الهاتف
            'C': 30,  # البريد الإلكتروني
            'D': 25,  # الشركة
            'E': 15,  # المدينة
            'F': 40   # ملاحظات
        }
        
        for col, width in column_widths.items():
            worksheet.column_dimensions[col].width = width
        
        # إضافة حدود للجدول
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        for row in range(1, len(df) + 2):
            for col in range(1, len(df.columns) + 1):
                worksheet.cell(row=row, column=col).border = thin_border
        
        # إضافة ورقة التعليمات
        instructions_data = [
            ["تعليمات استيراد ملف العملاء", ""],
            ["", ""],
            ["الأعمدة المطلوبة:", ""],
            ["الاسم", "اسم العميل (مطلوب)"],
            ["رقم الهاتف", "رقم الهاتف مع رمز الدولة (مطلوب)"],
            ["البريد الإلكتروني", "عنوان البريد الإلكتروني (اختياري)"],
            ["الشركة", "اسم الشركة أو المؤسسة (اختياري)"],
            ["المدينة", "المدينة أو المنطقة (اختياري)"],
            ["ملاحظات", "أي ملاحظات إضافية (اختياري)"],
            ["", ""],
            ["تنسيق رقم الهاتف:", ""],
            ["+966501234567", "مع رمز الدولة والعلامة +"],
            ["966501234567", "مع رمز الدولة بدون علامة +"],
            ["0501234567", "رقم محلي (سيتم إضافة 966 تلقائياً)"],
            ["", ""],
            ["نصائح مهمة:", ""],
            ["1. تأكد من صحة أرقام الهواتف", ""],
            ["2. استخدم أرقام هواتف سعودية فقط", ""],
            ["3. تجنب الأرقام المكررة", ""],
            ["4. املأ عمود الاسم لجميع الصفوف", ""],
            ["5. يمكن ترك الأعمدة الاختيارية فارغة", ""],
            ["", ""],
            ["أمثلة على الأرقام الصحيحة:", ""],
            ["+966501234567", "رقم موبايلي"],
            ["+966550123456", "رقم STC"],
            ["+966560987654", "رقم زين"],
            ["", ""],
            ["تاريخ الإنشاء:", datetime.now().strftime("%Y-%m-%d %H:%M:%S")]
        ]
        
        instructions_df = pd.DataFrame(instructions_data, columns=["البند", "التفاصيل"])
        instructions_df.to_excel(writer, sheet_name='التعليمات', index=False)
        
        # تنسيق ورقة التعليمات
        instructions_sheet = writer.sheets['التعليمات']
        
        # تنسيق العنوان الرئيسي
        title_cell = instructions_sheet.cell(row=1, column=1)
        title_cell.font = Font(bold=True, size=16, color="FFFFFF")
        title_cell.fill = PatternFill(start_color="D32F2F", end_color="D32F2F", fill_type="solid")
        title_cell.alignment = Alignment(horizontal="center", vertical="center")
        
        # دمج خلايا العنوان
        instructions_sheet.merge_cells('A1:B1')
        
        # تعديل عرض الأعمدة
        instructions_sheet.column_dimensions['A'].width = 30
        instructions_sheet.column_dimensions['B'].width = 50
        
        # تنسيق العناوين الفرعية
        for row in [3, 11, 16, 23]:
            cell = instructions_sheet.cell(row=row, column=1)
            cell.font = Font(bold=True, color="FFFFFF")
            cell.fill = PatternFill(start_color="1976D2", end_color="1976D2", fill_type="solid")
    
    print(f"✅ تم إنشاء الملف النموذجي: {filename}")
    return filename

def create_csv_template():
    """إنشاء ملف CSV نموذجي أيضاً"""
    
    # البيانات النموذجية (مبسطة للـ CSV)
    csv_data = [
        ["أحمد محمد", "+966501234567", "<EMAIL>"],
        ["فاطمة أحمد", "966502345678", "<EMAIL>"],
        ["محمد علي", "0503456789", "<EMAIL>"],
        ["نورا سالم", "+966504567890", "<EMAIL>"],
        ["خالد الأحمد", "966505678901", "<EMAIL>"]
    ]
    
    # إنشاء DataFrame
    df = pd.DataFrame(csv_data, columns=["الاسم", "رقم الهاتف", "البريد الإلكتروني"])
    
    # إنشاء مجلد للملفات النموذجية
    templates_dir = "templates"
    if not os.path.exists(templates_dir):
        os.makedirs(templates_dir)
    
    # حفظ الملف
    filename = f"{templates_dir}/نموذج_استيراد_العملاء.csv"
    df.to_csv(filename, index=False, encoding='utf-8-sig')
    
    print(f"✅ تم إنشاء ملف CSV النموذجي: {filename}")
    return filename

if __name__ == "__main__":
    print("🔧 إنشاء ملفات نموذجية لاستيراد العملاء...")
    print("=" * 60)
    
    try:
        # إنشاء ملف Excel
        excel_file = create_excel_template()
        
        # إنشاء ملف CSV
        csv_file = create_csv_template()
        
        print("\n🎉 تم إنشاء الملفات النموذجية بنجاح!")
        print(f"📁 المجلد: templates/")
        print(f"📊 ملف Excel: {excel_file}")
        print(f"📄 ملف CSV: {csv_file}")
        
        print("\n📋 كيفية الاستخدام:")
        print("1. افتح الملف النموذجي")
        print("2. عدل البيانات حسب عملائك")
        print("3. احفظ الملف")
        print("4. ارفعه في صفحة الحملات")
        
        print("\n💡 نصائح:")
        print("- اقرأ ورقة 'التعليمات' في ملف Excel")
        print("- تأكد من صحة أرقام الهواتف")
        print("- استخدم الترميز UTF-8 للنصوص العربية")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الملفات: {e}")
        print("تأكد من تثبيت pandas و openpyxl:")
        print("pip install pandas openpyxl")
    
    print("=" * 60)
    input("اضغط Enter للخروج...")
