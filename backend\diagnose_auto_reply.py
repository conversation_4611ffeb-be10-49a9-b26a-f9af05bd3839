#!/usr/bin/env python3
"""
تشخيص شامل لنظام الردود التلقائية
"""

import asyncio
import time
from database import SessionLocal
from models import Settings, AIResponse
from whatsapp_selenium import whatsapp_manager
from services.auto_reply_service import get_auto_reply_service

def check_database():
    """فحص قاعدة البيانات والإعدادات"""
    print("🔍 فحص قاعدة البيانات...")
    
    try:
        db = SessionLocal()
        
        # فحص الإعدادات
        settings = db.query(Settings).first()
        if not settings:
            print("   ❌ لا توجد إعدادات في قاعدة البيانات")
            return False
        
        print(f"   ✅ الإعدادات موجودة - ID: {settings.id}")
        print(f"   🤖 نموذج GPT: {settings.gpt_model}")
        print(f"   🔑 مفتاح OpenAI: {'موجود' if settings.openai_api_key else 'غير موجود'}")
        print(f"   🔄 الرد التلقائي: {'مفعل' if settings.auto_reply_enabled else 'معطل'}")
        print(f"   ⏱️ تأخير الرد: {settings.response_delay} ثانية")
        print(f"   📊 الحد الأقصى اليومي: {settings.max_daily_responses}")
        
        # فحص الردود السابقة
        responses_count = db.query(AIResponse).count()
        print(f"   📨 عدد الردود المحفوظة: {responses_count}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في قاعدة البيانات: {e}")
        return False

def check_whatsapp_status():
    """فحص حالة WhatsApp Web"""
    print("\n📱 فحص حالة WhatsApp Web...")
    
    try:
        status = whatsapp_manager.get_status()
        print(f"   📊 حالة الجلسة: {'نشطة' if status.get('session_active') else 'غير نشطة'}")
        print(f"   🔐 تسجيل الدخول: {'مُسجل' if status.get('logged_in') else 'غير مُسجل'}")
        print(f"   🌐 المتصفح: {'يعمل' if status.get('driver_active') else 'متوقف'}")
        
        if status.get('session_active') and status.get('logged_in'):
            print("   ✅ WhatsApp Web جاهز للاستخدام")
            return True
        else:
            print("   ❌ WhatsApp Web غير جاهز")
            print("   💡 يجب تسجيل الدخول أولاً من صفحة الإعدادات")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ في فحص WhatsApp: {e}")
        return False

async def test_auto_reply_service():
    """اختبار خدمة الردود التلقائية"""
    print("\n🤖 اختبار خدمة الردود التلقائية...")
    
    try:
        auto_reply = get_auto_reply_service(whatsapp_manager)
        
        # فحص الحالة الحالية
        print(f"   📊 حالة الخدمة: {'تعمل' if auto_reply.is_running else 'متوقفة'}")
        print(f"   🧠 خدمة AI: {'جاهزة' if auto_reply.ai_service else 'غير جاهزة'}")
        print(f"   🌐 المتصفح: {'متصل' if auto_reply.driver else 'غير متصل'}")
        
        # محاولة بدء الخدمة
        if not auto_reply.is_running:
            print("   🚀 محاولة بدء الخدمة...")
            try:
                await auto_reply.start_auto_reply()
                print("   ✅ تم بدء الخدمة بنجاح")
                
                # انتظار قصير ثم فحص المراقبة
                await asyncio.sleep(2)
                
                if auto_reply.monitor_task:
                    print(f"   🔄 مهمة المراقبة: {'تعمل' if not auto_reply.monitor_task.done() else 'متوقفة'}")
                else:
                    print("   ❌ مهمة المراقبة غير موجودة")
                
                return True
                
            except Exception as e:
                print(f"   ❌ فشل في بدء الخدمة: {e}")
                return False
        else:
            print("   ✅ الخدمة تعمل بالفعل")
            return True
            
    except Exception as e:
        print(f"   ❌ خطأ في اختبار الخدمة: {e}")
        return False

async def test_message_detection():
    """اختبار كشف الرسائل"""
    print("\n🔍 اختبار كشف الرسائل...")
    
    try:
        auto_reply = get_auto_reply_service(whatsapp_manager)
        
        if not auto_reply.driver:
            print("   ❌ المتصفح غير متصل")
            return False
        
        # فحص الاتصال
        connection_ok = auto_reply._check_whatsapp_connection()
        print(f"   📡 حالة الاتصال: {'متصل' if connection_ok else 'منقطع'}")
        
        if not connection_ok:
            return False
        
        # محاولة البحث عن رسائل غير مقروءة
        print("   🔍 البحث عن رسائل غير مقروءة...")
        new_messages = await auto_reply._get_new_messages()
        
        print(f"   📊 عدد الرسائل الجديدة: {len(new_messages)}")
        
        if new_messages:
            for i, msg in enumerate(new_messages[:3]):  # أول 3 رسائل
                print(f"   📨 رسالة {i+1}:")
                print(f"      👤 من: {msg.get('contact_name', 'غير معروف')}")
                print(f"      📱 الرقم: {msg.get('phone_number', 'غير معروف')}")
                print(f"      💬 النص: {msg.get('message_text', '')[:50]}...")
        else:
            print("   ℹ️ لا توجد رسائل جديدة")
            print("   💡 جرب إرسال رسالة لنفسك من هاتف آخر")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار كشف الرسائل: {e}")
        return False

async def full_diagnosis():
    """تشخيص شامل للنظام"""
    print("🏥 تشخيص شامل لنظام الردود التلقائية")
    print("=" * 60)
    
    # 1. فحص قاعدة البيانات
    db_ok = check_database()
    
    # 2. فحص WhatsApp Web
    whatsapp_ok = check_whatsapp_status()
    
    # 3. اختبار خدمة الردود التلقائية
    service_ok = await test_auto_reply_service()
    
    # 4. اختبار كشف الرسائل
    detection_ok = await test_message_detection()
    
    # النتيجة النهائية
    print(f"\n" + "=" * 60)
    print("📋 ملخص التشخيص:")
    print(f"   🗄️ قاعدة البيانات: {'✅ تعمل' if db_ok else '❌ مشكلة'}")
    print(f"   📱 WhatsApp Web: {'✅ متصل' if whatsapp_ok else '❌ غير متصل'}")
    print(f"   🤖 خدمة الردود: {'✅ تعمل' if service_ok else '❌ مشكلة'}")
    print(f"   🔍 كشف الرسائل: {'✅ يعمل' if detection_ok else '❌ مشكلة'}")
    
    if all([db_ok, whatsapp_ok, service_ok, detection_ok]):
        print("\n🎉 النظام يعمل بشكل صحيح!")
        print("💡 إذا لم تحصل على ردود، تأكد من:")
        print("   - إرسال رسالة من رقم مختلف")
        print("   - انتظار 30-90 ثانية للرد")
        print("   - فحص logs الخادم للأخطاء")
    else:
        print("\n⚠️ يوجد مشاكل في النظام!")
        print("🔧 خطوات الإصلاح:")
        if not db_ok:
            print("   - تحقق من إعدادات قاعدة البيانات")
        if not whatsapp_ok:
            print("   - سجل دخول WhatsApp Web من الإعدادات")
        if not service_ok:
            print("   - تحقق من مفتاح OpenAI API")
        if not detection_ok:
            print("   - تحقق من اتصال المتصفح")

if __name__ == "__main__":
    asyncio.run(full_diagnosis())
