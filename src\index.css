@tailwind base;
@tailwind components;
@tailwind utilities;

/* تصميم نظام WhatsApp الذكي - جميع الألوان بصيغة HSL */

@layer base {
  :root {
    /* خلفيات */
    --background: 120 8% 97%;
    --foreground: 120 15% 15%;

    /* كروت */
    --card: 0 0% 100%;
    --card-foreground: 120 15% 15%;

    /* نوافذ منبثقة */
    --popover: 0 0% 100%;
    --popover-foreground: 120 15% 15%;

    /* الألوان الأساسية - أخضر WhatsApp */
    --primary: 142 70% 49%;
    --primary-foreground: 0 0% 100%;
    --primary-light: 142 70% 60%;
    --primary-dark: 142 70% 35%;

    /* ألوان ثانوية */
    --secondary: 120 20% 95%;
    --secondary-foreground: 120 15% 25%;

    /* ألوان خافتة */
    --muted: 120 10% 95%;
    --muted-foreground: 120 8% 45%;

    /* ألوان التمييز */
    --accent: 142 50% 92%;
    --accent-foreground: 142 70% 25%;

    /* ألوان التحذير والخطر */
    --destructive: 0 70% 55%;
    --destructive-foreground: 0 0% 100%;
    --warning: 45 90% 60%;
    --warning-foreground: 45 20% 20%;
    --success: 142 70% 49%;
    --success-foreground: 0 0% 100%;

    /* حدود وإدخالات */
    --border: 120 15% 88%;
    --input: 120 15% 95%;
    --ring: 142 70% 49%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    /* الوضع الليلي - خلفيات داكنة */
    --background: 120 15% 8%;
    --foreground: 120 10% 95%;

    /* كروت */
    --card: 120 12% 12%;
    --card-foreground: 120 10% 95%;

    /* نوافذ منبثقة */
    --popover: 120 12% 12%;
    --popover-foreground: 120 10% 95%;

    /* الألوان الأساسية - أخضر WhatsApp في الوضع الليلي */
    --primary: 142 70% 45%;
    --primary-foreground: 0 0% 100%;
    --primary-light: 142 70% 55%;
    --primary-dark: 142 70% 30%;

    /* ألوان ثانوية */
    --secondary: 120 12% 18%;
    --secondary-foreground: 120 10% 85%;

    /* ألوان خافتة */
    --muted: 120 12% 15%;
    --muted-foreground: 120 8% 65%;

    /* ألوان التمييز */
    --accent: 120 12% 18%;
    --accent-foreground: 142 70% 45%;

    /* ألوان التحذير والخطر */
    --destructive: 0 70% 50%;
    --destructive-foreground: 0 0% 100%;
    --warning: 45 90% 55%;
    --warning-foreground: 0 0% 100%;
    --success: 142 70% 45%;
    --success-foreground: 0 0% 100%;

    /* حدود وإدخالات */
    --border: 120 12% 22%;
    --input: 120 12% 18%;
    --ring: 142 70% 45%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

/* تدرجات وتأثيرات جميلة */
:root {
  --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-light)));
  --gradient-success: linear-gradient(135deg, hsl(var(--success)), hsl(142 70% 60%));
  --gradient-card: linear-gradient(135deg, hsl(var(--card)), hsl(var(--accent)));
  --shadow-soft: 0 4px 20px -2px hsl(var(--primary) / 0.1);
  --shadow-glow: 0 0 30px hsl(var(--primary) / 0.2);
  --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.dark {
  --gradient-card: linear-gradient(135deg, hsl(var(--card)), hsl(120 12% 15%));
  --shadow-soft: 0 8px 25px -5px hsl(0 0% 0% / 0.3);
  --shadow-glow: 0 0 40px hsl(var(--primary) / 0.3);
}

/* خطوط عربية */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Cairo', system-ui, -apple-system, sans-serif;
    direction: rtl;
  }

  html {
    direction: rtl;
  }
}