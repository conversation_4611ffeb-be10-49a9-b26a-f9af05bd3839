import os
import uuid
from typing import Dict, List, Any, Optional
from fastapi import UploadFile
from sqlalchemy.orm import Session
import logging
import csv
import json

# تحقق من توفر pandas
try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False

from models import Contact

logger = logging.getLogger(__name__)

class FileService:
    """خدمة معالجة الملفات"""
    
    def __init__(self):
        self.upload_dir = "uploads"
        self.allowed_extensions = {'.xlsx', '.xls', '.csv'}
        self._ensure_upload_dir()
    
    def _ensure_upload_dir(self):
        """التأكد من وجود مجلد الرفع"""
        if not os.path.exists(self.upload_dir):
            os.makedirs(self.upload_dir)
    
    async def save_uploaded_file(self, file: UploadFile) -> str:
        """حفظ الملف المرفوع"""
        
        # التحقق من امتداد الملف
        file_extension = os.path.splitext(file.filename)[1].lower()
        if file_extension not in self.allowed_extensions:
            raise ValueError(f"نوع الملف غير مدعوم: {file_extension}")
        
        # إنشاء اسم ملف فريد
        unique_filename = f"{uuid.uuid4()}{file_extension}"
        file_path = os.path.join(self.upload_dir, unique_filename)
        
        try:
            # حفظ الملف
            with open(file_path, "wb") as buffer:
                content = await file.read()
                buffer.write(content)
            
            logger.info(f"تم حفظ الملف: {file_path}")
            return file_path
            
        except Exception as e:
            logger.error(f"خطأ في حفظ الملف: {str(e)}")
            raise e
    
    def read_excel_file(self, file_path: str):
        """قراءة ملف Excel أو CSV"""

        if not PANDAS_AVAILABLE:
            return self._read_csv_simple(file_path)

        try:
            if file_path.endswith('.csv'):
                # قراءة ملف CSV
                df = pd.read_csv(file_path, encoding='utf-8')
            else:
                # قراءة ملف Excel
                df = pd.read_excel(file_path)

            # تنظيف البيانات
            df = df.dropna(how='all')  # حذف الصفوف الفارغة
            df = df.fillna('')  # استبدال القيم الفارغة بنص فارغ

            return df

        except Exception as e:
            logger.error(f"خطأ في قراءة الملف {file_path}: {str(e)}")
            raise e

    def _read_csv_simple(self, file_path: str) -> Dict[str, Any]:
        """قراءة ملف CSV بدون pandas"""

        if not file_path.endswith('.csv'):
            raise ValueError("يمكن قراءة ملفات CSV فقط بدون pandas")

        try:
            data = []
            with open(file_path, 'r', encoding='utf-8') as file:
                reader = csv.DictReader(file)
                columns = reader.fieldnames
                for row in reader:
                    # تنظيف البيانات
                    cleaned_row = {k: v.strip() if v else '' for k, v in row.items()}
                    data.append(cleaned_row)

            return {
                'columns': columns,
                'data': data,
                'shape': (len(data), len(columns))
            }

        except Exception as e:
            logger.error(f"خطأ في قراءة ملف CSV {file_path}: {str(e)}")
            raise e
    
    def validate_contacts_data(self, data) -> Dict[str, Any]:
        """التحقق من صحة بيانات العملاء"""

        errors = []
        warnings = []

        # التحقق من نوع البيانات
        if PANDAS_AVAILABLE and hasattr(data, 'columns'):
            # استخدام pandas
            columns = data.columns.tolist()
            total_rows = len(data)
        else:
            # استخدام البيانات البسيطة
            columns = data.get('columns', [])
            total_rows = data.get('shape', (0, 0))[0]

        # التحقق من وجود الأعمدة المطلوبة
        required_columns = ['name', 'phone']
        alternative_columns = {
            'name': ['الاسم', 'اسم', 'Name', 'الاسم الكامل', 'name', 'NAME'],
            'phone': ['الهاتف', 'هاتف', 'Phone', 'رقم الهاتف', 'phone_number', 'PHONE', 'رقم_الهاتف', 'phone', 'mobile', 'موبايل']
        }

        # تطبيع أسماء الأعمدة
        normalized_columns = {}
        for col in columns:
            col_lower = col.lower().strip()
            col_clean = col.strip()

            # البحث عن عمود الاسم
            if 'name' not in normalized_columns:
                for alt in alternative_columns['name']:
                    if alt.lower() == col_lower or alt in col_clean:
                        normalized_columns['name'] = col
                        break

            # البحث عن عمود الهاتف
            if 'phone' not in normalized_columns:
                for alt in alternative_columns['phone']:
                    if alt.lower() == col_lower or alt in col_clean:
                        normalized_columns['phone'] = col
                        break

            # البحث عن عمود الايميل
            if 'email' not in normalized_columns:
                if ('email' in col_lower or 'ايميل' in col_lower or 'بريد' in col_lower or
                    'إلكتروني' in col_clean or 'الإلكتروني' in col_clean):
                    normalized_columns['email'] = col
        
        # تسجيل تشخيصي
        logger.info(f"الأعمدة الموجودة في الملف: {columns}")
        logger.info(f"الأعمدة المطابقة: {normalized_columns}")

        # التحقق من وجود الأعمدة المطلوبة
        for required_col in required_columns:
            if required_col not in normalized_columns:
                errors.append(f"العمود المطلوب '{required_col}' غير موجود. الأعمدة الموجودة: {columns}")

        if errors:
            logger.error(f"أخطاء في التحقق من الأعمدة: {errors}")
            return {"valid": False, "errors": errors, "warnings": warnings}
        
        # التحقق من البيانات إذا كان لدينا pandas DataFrame
        if PANDAS_AVAILABLE and hasattr(data, 'columns'):
            # استخدام pandas
            df = data
            name_col = normalized_columns['name']
            phone_col = normalized_columns['phone']

            # التحقق من الأسماء الفارغة
            empty_names = df[df[name_col].astype(str).str.strip() == ''].index.tolist()
            if empty_names:
                warnings.append(f"يوجد {len(empty_names)} صف بأسماء فارغة")

            # التحقق من أرقام الهاتف
            invalid_phones = []
            for idx, phone in df[phone_col].items():
                phone_str = str(phone).strip()
                if not self._validate_phone_number(phone_str):
                    invalid_phones.append(idx + 1)  # رقم الصف (1-based)

            if invalid_phones:
                if len(invalid_phones) > 10:
                    warnings.append(f"يوجد {len(invalid_phones)} رقم هاتف غير صحيح")
                else:
                    warnings.append(f"أرقام هاتف غير صحيحة في الصفوف: {invalid_phones}")

            # التحقق من التكرار
            duplicate_phones = df[df[phone_col].duplicated()].index.tolist()
            if duplicate_phones:
                warnings.append(f"يوجد {len(duplicate_phones)} رقم هاتف مكرر")

            return {
                "valid": True,
                "errors": errors,
                "warnings": warnings,
                "normalized_columns": normalized_columns,
                "total_rows": len(df),
                "valid_phones": len(df) - len(invalid_phones),
                "invalid_phones": len(invalid_phones)
            }
        else:
            # للبيانات البسيطة بدون pandas
            return {
                "valid": True,
                "errors": errors,
                "warnings": warnings,
                "normalized_columns": normalized_columns,
                "total_rows": total_rows,
                "valid_phones": total_rows,  # افتراض أن جميع الأرقام صحيحة
                "invalid_phones": 0
            }
    
    def _validate_phone_number(self, phone: str) -> bool:
        """التحقق من صحة رقم الهاتف"""
        if not phone or phone == 'nan':
            return False
        
        # إزالة الرموز غير الرقمية
        phone_digits = ''.join(filter(str.isdigit, phone))
        
        # التحقق من الطول
        if len(phone_digits) < 9 or len(phone_digits) > 15:
            return False
        
        # التحقق من أرقام الهاتف السعودية
        if phone_digits.startswith('966'):
            return len(phone_digits) == 12
        elif phone_digits.startswith('05'):
            return len(phone_digits) == 10
        elif len(phone_digits) == 9 and phone_digits.startswith('5'):
            return True
        
        return True
    
    async def process_contacts_file(
        self, 
        file_path: str, 
        campaign_id: int, 
        db: Session
    ) -> int:
        """معالجة ملف العملاء وإضافتهم لقاعدة البيانات"""
        
        try:
            # قراءة الملف
            df = self.read_excel_file(file_path)
            
            # التحقق من صحة البيانات
            validation_result = self.validate_contacts_data(df)
            if not validation_result["valid"]:
                raise ValueError(f"بيانات غير صحيحة: {validation_result['errors']}")
            
            normalized_columns = validation_result["normalized_columns"]
            
            # معالجة البيانات وإضافة العملاء
            contacts_added = 0
            
            for idx, row in df.iterrows():
                try:
                    # استخراج البيانات الأساسية
                    name = str(row[normalized_columns['name']]).strip()
                    phone = str(row[normalized_columns['phone']]).strip()
                    
                    # تخطي الصفوف الفارغة
                    if not name or not phone or phone == 'nan':
                        continue
                    
                    # تنسيق رقم الهاتف
                    formatted_phone = self._format_phone_number(phone)
                    if not self._validate_phone_number(formatted_phone):
                        continue
                    
                    # استخراج الايميل إذا كان متوفراً
                    email = None
                    if 'email' in normalized_columns:
                        email_value = str(row[normalized_columns['email']]).strip()
                        if email_value and email_value != 'nan' and '@' in email_value:
                            email = email_value
                    
                    # استخراج البيانات المخصصة
                    custom_fields = {}
                    for col in df.columns:
                        if col not in normalized_columns.values():
                            value = str(row[col]).strip()
                            if value and value != 'nan':
                                custom_fields[col] = value
                    
                    # التحقق من عدم وجود العميل مسبقاً
                    existing_contact = db.query(Contact).filter(
                        Contact.phone_number == formatted_phone,
                        Contact.campaign_id == campaign_id
                    ).first()
                    
                    if existing_contact:
                        continue  # تخطي العميل المكرر
                    
                    # إنشاء العميل الجديد
                    new_contact = Contact(
                        name=name,
                        phone_number=formatted_phone,
                        email=email,
                        campaign_id=campaign_id,
                        custom_fields=custom_fields if custom_fields else None
                    )
                    
                    db.add(new_contact)
                    contacts_added += 1
                    
                except Exception as e:
                    logger.warning(f"خطأ في معالجة الصف {idx + 1}: {str(e)}")
                    continue
            
            # حفظ التغييرات
            db.commit()
            
            # حذف الملف المؤقت
            try:
                os.remove(file_path)
            except:
                pass
            
            logger.info(f"تم إضافة {contacts_added} عميل من الملف")
            return contacts_added
            
        except Exception as e:
            logger.error(f"خطأ في معالجة ملف العملاء: {str(e)}")
            db.rollback()
            raise e
    
    def _format_phone_number(self, phone: str) -> str:
        """تنسيق رقم الهاتف"""
        # إزالة الرموز غير الرقمية
        phone_digits = ''.join(filter(str.isdigit, phone))
        
        # تنسيق الأرقام السعودية
        if phone_digits.startswith('966'):
            return phone_digits
        elif phone_digits.startswith('05') and len(phone_digits) == 10:
            return '966' + phone_digits[1:]
        elif len(phone_digits) == 9 and phone_digits.startswith('5'):
            return '966' + phone_digits
        
        return phone_digits
    
    def get_file_info(self, file_path: str) -> Dict[str, Any]:
        """الحصول على معلومات الملف"""
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError("الملف غير موجود")
            
            file_size = os.path.getsize(file_path)
            file_extension = os.path.splitext(file_path)[1].lower()
            
            # قراءة عينة من البيانات
            df = self.read_excel_file(file_path)
            
            return {
                "file_path": file_path,
                "file_size": file_size,
                "file_extension": file_extension,
                "total_rows": len(df),
                "columns": df.columns.tolist(),
                "sample_data": df.head(3).to_dict('records')
            }
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على معلومات الملف: {str(e)}")
            raise e
