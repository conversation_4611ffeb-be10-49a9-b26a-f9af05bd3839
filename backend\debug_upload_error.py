"""
تشخيص مشكلة خطأ 500 في رفع الملفات
"""

import os
import sys
import traceback
from fastapi import UploadFile
from io import BytesIO

# إضافة مسار المشروع
sys.path.insert(0, os.getcwd())

from services.file_service import FileService
from database import get_db, init_db
from models import Campaign

def test_file_service():
    """اختبار FileService"""
    
    print("🔧 اختبار FileService...")
    print("=" * 50)
    
    try:
        # إنشاء FileService
        file_service = FileService()
        print(f"✅ تم إنشاء FileService")
        print(f"📁 مجلد الرفع: {file_service.upload_dir}")
        print(f"📋 الامتدادات المدعومة: {file_service.allowed_extensions}")
        
        # التحقق من مجلد الرفع
        if os.path.exists(file_service.upload_dir):
            print(f"✅ مجلد الرفع موجود")
        else:
            print(f"❌ مجلد الرفع غير موجود")
            print(f"🔧 إنشاء مجلد الرفع...")
            file_service._ensure_upload_dir()
            print(f"✅ تم إنشاء مجلد الرفع")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في FileService: {e}")
        traceback.print_exc()
        return False

def test_csv_upload():
    """اختبار رفع ملف CSV"""
    
    print("\n📄 اختبار رفع ملف CSV...")
    print("=" * 50)
    
    try:
        # إنشاء ملف CSV تجريبي
        csv_content = """الاسم,رقم الهاتف,البريد الإلكتروني,الشركة,المدينة,ملاحظات
أحمد محمد,+966501234567,<EMAIL>,شركة التقنية,الرياض,عميل VIP
فاطمة علي,0502345678,<EMAIL>,مؤسسة الإبداع,جدة,عميلة جديدة"""
        
        # إنشاء UploadFile تجريبي
        csv_bytes = csv_content.encode('utf-8')
        csv_file = BytesIO(csv_bytes)
        
        # محاكاة UploadFile
        class MockUploadFile:
            def __init__(self, content, filename):
                self.file = BytesIO(content)
                self.filename = filename
            
            async def read(self):
                return self.file.getvalue()
        
        mock_file = MockUploadFile(csv_bytes, "test.csv")
        
        # اختبار حفظ الملف
        file_service = FileService()
        
        print(f"🧪 اختبار حفظ ملف CSV...")
        
        # حفظ الملف
        import asyncio
        file_path = asyncio.run(file_service.save_uploaded_file(mock_file))
        
        print(f"✅ تم حفظ الملف: {file_path}")
        
        # التحقق من وجود الملف
        if os.path.exists(file_path):
            print(f"✅ الملف موجود على القرص")
            
            # قراءة الملف
            df = file_service.read_excel_file(file_path)
            print(f"📊 عدد الصفوف: {len(df)}")
            print(f"📋 الأعمدة: {list(df.columns)}")
            
            # اختبار التحقق من صحة البيانات
            validation_result = file_service.validate_contacts_data(df)
            print(f"✅ نتيجة التحقق: {validation_result['valid']}")
            
            if validation_result['valid']:
                print(f"📝 الأعمدة المطابقة: {validation_result['normalized_columns']}")
            else:
                print(f"❌ الأخطاء: {validation_result['errors']}")
            
            # حذف الملف التجريبي
            os.remove(file_path)
            print(f"🗑️ تم حذف الملف التجريبي")
            
        else:
            print(f"❌ الملف غير موجود على القرص")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار رفع CSV: {e}")
        traceback.print_exc()
        return False

def test_database_integration():
    """اختبار التكامل مع قاعدة البيانات"""
    
    print("\n💾 اختبار التكامل مع قاعدة البيانات...")
    print("=" * 50)
    
    try:
        # تهيئة قاعدة البيانات
        init_db()
        db = next(get_db())
        
        # البحث عن حملة للاختبار
        campaign = db.query(Campaign).first()
        
        if not campaign:
            print("❌ لا توجد حملات للاختبار")
            return False
        
        print(f"🎯 اختبار الحملة: {campaign.name} (ID: {campaign.id})")
        
        # إنشاء ملف CSV تجريبي
        csv_content = """الاسم,رقم الهاتف,البريد الإلكتروني,الشركة,المدينة,ملاحظات
أحمد محمد,+966501234567,<EMAIL>,شركة التقنية,الرياض,عميل VIP
فاطمة علي,0502345678,<EMAIL>,مؤسسة الإبداع,جدة,عميلة جديدة"""
        
        # حفظ الملف مؤقتاً
        temp_file = "temp_test.csv"
        with open(temp_file, 'w', encoding='utf-8') as f:
            f.write(csv_content)
        
        print(f"📄 تم إنشاء ملف تجريبي: {temp_file}")
        
        # اختبار معالجة الملف
        file_service = FileService()
        
        import asyncio
        contacts_count = asyncio.run(
            file_service.process_contacts_file(temp_file, campaign.id, db)
        )
        
        print(f"✅ تم معالجة الملف بنجاح")
        print(f"📞 عدد جهات الاتصال المضافة: {contacts_count}")
        
        # حذف الملف التجريبي
        os.remove(temp_file)
        print(f"🗑️ تم حذف الملف التجريبي")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {e}")
        traceback.print_exc()
        return False

def test_imports():
    """اختبار الاستيرادات المطلوبة"""
    
    print("📦 اختبار الاستيرادات...")
    print("=" * 50)
    
    try:
        # اختبار pandas
        try:
            import pandas as pd
            print("✅ pandas متوفر")
        except ImportError:
            print("❌ pandas غير متوفر")
            return False
        
        # اختبار fastapi
        try:
            from fastapi import UploadFile
            print("✅ FastAPI متوفر")
        except ImportError:
            print("❌ FastAPI غير متوفر")
            return False
        
        # اختبار sqlalchemy
        try:
            from sqlalchemy.orm import Session
            print("✅ SQLAlchemy متوفر")
        except ImportError:
            print("❌ SQLAlchemy غير متوفر")
            return False
        
        # اختبار models
        try:
            from models import Contact, Campaign
            print("✅ Models متوفرة")
        except ImportError as e:
            print(f"❌ Models غير متوفرة: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الاستيرادات: {e}")
        return False

def run_all_tests():
    """تشغيل جميع الاختبارات"""
    
    print("🔍 تشخيص شامل لمشكلة خطأ 500 في رفع الملفات")
    print("=" * 60)
    
    tests = [
        ("اختبار الاستيرادات", test_imports),
        ("اختبار FileService", test_file_service),
        ("اختبار رفع CSV", test_csv_upload),
        ("اختبار قاعدة البيانات", test_database_integration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ خطأ في {test_name}: {e}")
            results.append((test_name, False))
    
    print("\n📊 نتائج التشخيص:")
    print("=" * 60)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"  {test_name}: {status}")
        if not result:
            all_passed = False
    
    print(f"\n🎯 النتيجة النهائية:")
    if all_passed:
        print("✅ جميع الاختبارات نجحت!")
        print("💡 المشكلة قد تكون في إعدادات الخادم أو الشبكة")
    else:
        print("❌ بعض الاختبارات فشلت")
        print("💡 راجع الأخطاء أعلاه لتحديد المشكلة")
    
    return all_passed

if __name__ == "__main__":
    print("🔧 تشخيص مشكلة خطأ 500 في رفع الملفات...")
    print("=" * 60)
    
    # تشغيل التشخيص
    result = run_all_tests()
    
    if result:
        print("\n🎉 النظام يبدو سليماً!")
        print("💡 المشكلة قد تكون:")
        print("   - إعدادات CORS")
        print("   - مشكلة في الشبكة")
        print("   - خطأ في logs الخادم")
    else:
        print("\n⚠️ تم العثور على مشاكل")
        print("💡 راجع الأخطاء وأصلحها")
    
    print("=" * 60)
    input("اضغط Enter للخروج...")
