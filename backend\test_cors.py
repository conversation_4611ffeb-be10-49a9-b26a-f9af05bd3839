"""
اختبار إعدادات CORS
"""

import requests
import time

def test_cors():
    """اختبار CORS"""
    
    print("🔍 اختبار إعدادات CORS...")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    try:
        # اختبار 1: GET request عادي
        print("1. اختبار GET request...")
        response = requests.get(f"{base_url}/api/campaigns")
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ GET request نجح")
            
            # فحص CORS headers
            cors_headers = {
                'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
                'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
                'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers'),
            }
            
            print("   CORS Headers:")
            for header, value in cors_headers.items():
                if value:
                    print(f"     ✅ {header}: {value}")
                else:
                    print(f"     ❌ {header}: مفقود")
        else:
            print("   ❌ GET request فشل")
        
        # اختبار 2: OPTIONS request (preflight)
        print("\n2. اختبار OPTIONS request...")
        
        # محاكاة preflight request
        headers = {
            'Origin': 'http://localhost:5173',
            'Access-Control-Request-Method': 'POST',
            'Access-Control-Request-Headers': 'content-type',
        }
        
        response = requests.options(f"{base_url}/api/campaigns/1/upload-contacts", headers=headers)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ OPTIONS request نجح")
            
            # فحص preflight headers
            preflight_headers = {
                'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
                'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
                'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers'),
                'Access-Control-Allow-Credentials': response.headers.get('Access-Control-Allow-Credentials'),
            }
            
            print("   Preflight Headers:")
            for header, value in preflight_headers.items():
                if value:
                    print(f"     ✅ {header}: {value}")
                else:
                    print(f"     ❌ {header}: مفقود")
        else:
            print("   ❌ OPTIONS request فشل")
        
        # اختبار 3: محاكاة رفع ملف
        print("\n3. اختبار محاكاة رفع ملف...")
        
        # إنشاء ملف تجريبي
        files = {
            'file': ('test.csv', 'الاسم,رقم الهاتف\nأحمد,+966501234567', 'text/csv')
        }
        
        headers = {
            'Origin': 'http://localhost:5173',
        }
        
        try:
            response = requests.post(f"{base_url}/api/campaigns/1/upload-contacts", 
                                   files=files, headers=headers, timeout=10)
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                print("   ✅ رفع الملف نجح")
                result = response.json()
                print(f"   📊 النتيجة: {result}")
            elif response.status_code == 404:
                print("   ⚠️ الحملة غير موجودة (متوقع)")
            else:
                print(f"   ❌ رفع الملف فشل: {response.text}")
                
        except requests.exceptions.Timeout:
            print("   ⏰ انتهت مهلة الطلب")
        except Exception as e:
            print(f"   ❌ خطأ في رفع الملف: {e}")
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ لا يمكن الاتصال بالخادم على localhost:8000")
        print("💡 تأكد من تشغيل الباك إند")
        return False
    except Exception as e:
        print(f"❌ خطأ في اختبار CORS: {e}")
        return False

def test_frontend_simulation():
    """محاكاة طلبات الفرونت إند"""
    
    print("\n🌐 محاكاة طلبات الفرونت إند...")
    print("=" * 50)
    
    try:
        # محاكاة fetch من الفرونت إند
        import urllib.request
        import urllib.parse
        import json
        
        # إنشاء طلب مع headers مشابهة للفرونت إند
        url = "http://localhost:8000/api/campaigns"
        
        req = urllib.request.Request(url)
        req.add_header('Origin', 'http://localhost:5173')
        req.add_header('User-Agent', 'Mozilla/5.0 (Frontend Test)')
        
        with urllib.request.urlopen(req) as response:
            print(f"✅ Frontend simulation نجح: {response.status}")
            
            # فحص headers
            headers = dict(response.headers)
            cors_headers = [h for h in headers.keys() if 'access-control' in h.lower()]
            
            if cors_headers:
                print("   CORS Headers موجودة:")
                for header in cors_headers:
                    print(f"     {header}: {headers[header]}")
            else:
                print("   ❌ لا توجد CORS headers")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في محاكاة الفرونت إند: {e}")
        return False

if __name__ == "__main__":
    print("🔧 اختبار شامل لإعدادات CORS")
    print("=" * 60)
    
    # انتظار قصير للتأكد من تشغيل الخادم
    time.sleep(1)
    
    # تشغيل الاختبارات
    cors_test = test_cors()
    frontend_test = test_frontend_simulation()
    
    print("\n📊 نتائج الاختبار:")
    print("=" * 60)
    
    if cors_test:
        print("✅ اختبار CORS: نجح")
    else:
        print("❌ اختبار CORS: فشل")
    
    if frontend_test:
        print("✅ محاكاة الفرونت إند: نجح")
    else:
        print("❌ محاكاة الفرونت إند: فشل")
    
    if cors_test and frontend_test:
        print("\n🎉 جميع اختبارات CORS نجحت!")
        print("💡 يجب أن يعمل رفع الملفات الآن")
    else:
        print("\n⚠️ بعض اختبارات CORS فشلت")
        print("💡 راجع إعدادات الخادم")
    
    print("=" * 60)
    input("اضغط Enter للخروج...")
