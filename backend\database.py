from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import os

# إعداد قاعدة البيانات SQLite
DATABASE_URL = "sqlite:///./smart_whatsapp.db"

# إنشاء محرك قاعدة البيانات
engine = create_engine(
    DATABASE_URL, 
    connect_args={"check_same_thread": False},
    echo=True  # لطباعة استعلامات SQL في وضع التطوير
)

# إنشاء جلسة قاعدة البيانات
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# إنشاء الفئة الأساسية للنماذج
Base = declarative_base()

# دالة للحصول على جلسة قاعدة البيانات
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# دالة لتهيئة قاعدة البيانات
def init_db():
    """إنشاء جميع الجداول في قاعدة البيانات"""
    from models import Campaign, Contact, Message, Settings, AIResponse
    Base.metadata.create_all(bind=engine)
    
    # إنشاء الإعدادات الافتراضية إذا لم تكن موجودة
    db = SessionLocal()
    try:
        existing_settings = db.query(Settings).first()
        if not existing_settings:
            default_settings = Settings(
                whatsapp_api_url="https://api.whatsapp.com/send",
                whatsapp_token="",
                openai_api_key="",
                auto_reply_enabled=False,
                response_delay=2,
                gpt_model="gpt-3.5-turbo",
                gpt_prompt="""أنت مساعد خدمة عملاء ودود ومهني. مهمتك:

1. الرد على استفسارات العملاء بطريقة مهذبة وسريعة
2. تقديم معلومات مفيدة حول المنتجات والخدمات
3. توجيه العملاء للحصول على المساعدة المناسبة
4. استخدام اللغة العربية بشكل طبيعي ومفهوم

تذكر أن تكون دائماً مساعداً ومتعاوناً."""
            )
            db.add(default_settings)
            db.commit()
            print("✅ تم إنشاء الإعدادات الافتراضية")
    except Exception as e:
        print(f"❌ خطأ في إنشاء الإعدادات الافتراضية: {e}")
        db.rollback()
    finally:
        db.close()
