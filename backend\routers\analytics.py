from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_, case
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta, date

from database import get_db
from models import Campaign, Contact, Message, AIResponse, Settings
from schemas import DashboardStats

router = APIRouter(prefix="/api/analytics", tags=["analytics"])

@router.get("/dashboard", response_model=DashboardStats)
async def get_dashboard_stats(db: Session = Depends(get_db)):
    """الحصول على إحصائيات لوحة التحكم الرئيسية"""
    
    # إجمالي الرسائل المرسلة
    total_messages_sent = db.query(Message).filter(
        Message.status.in_(["sent", "delivered"])
    ).count()
    
    # عدد الردود التلقائية
    auto_replies_count = db.query(AIResponse).count()
    
    # العملاء النشطين (الذين تم إرسال رسائل لهم في آخر 30 يوم)
    thirty_days_ago = datetime.now() - timedelta(days=30)
    active_customers = db.query(Contact).join(Message).filter(
        Message.created_at >= thirty_days_ago
    ).distinct().count()
    
    # معدل النجاح
    total_messages = db.query(Message).count()
    delivered_messages = db.query(Message).filter(Message.status == "delivered").count()
    success_rate = (delivered_messages / total_messages * 100) if total_messages > 0 else 0
    
    # النشاط الأخير
    recent_activity = []
    
    # آخر الحملات
    recent_campaigns = db.query(Campaign).order_by(Campaign.created_at.desc()).limit(3).all()
    for campaign in recent_campaigns:
        time_diff = datetime.now() - campaign.created_at
        if time_diff.days > 0:
            time_str = f"منذ {time_diff.days} يوم"
        elif time_diff.seconds > 3600:
            hours = time_diff.seconds // 3600
            time_str = f"منذ {hours} ساعة"
        else:
            minutes = time_diff.seconds // 60
            time_str = f"منذ {minutes} دقيقة"
        
        recent_activity.append({
            "action": "تم إرسال حملة ترويجية",
            "target": f"{campaign.total_contacts} عميل",
            "time": time_str,
            "status": "success" if campaign.status == "completed" else "info"
        })
    
    # آخر الردود التلقائية
    recent_ai_responses = db.query(AIResponse).order_by(AIResponse.created_at.desc()).limit(2).all()
    for response in recent_ai_responses:
        time_diff = datetime.now() - response.created_at
        if time_diff.days > 0:
            time_str = f"منذ {time_diff.days} يوم"
        elif time_diff.seconds > 3600:
            hours = time_diff.seconds // 3600
            time_str = f"منذ {hours} ساعة"
        else:
            minutes = time_diff.seconds // 60
            time_str = f"منذ {minutes} دقيقة"
        
        recent_activity.append({
            "action": "رد تلقائي جديد",
            "target": response.contact_phone,
            "time": time_str,
            "status": "info"
        })
    
    return DashboardStats(
        total_messages_sent=total_messages_sent,
        auto_replies_count=auto_replies_count,
        active_customers=active_customers,
        success_rate=round(success_rate, 1),
        recent_activity=recent_activity[:5]  # أحدث 5 أنشطة
    )

@router.get("/campaigns/performance")
async def get_campaigns_performance(
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    db: Session = Depends(get_db)
):
    """الحصول على أداء الحملات"""
    
    query = db.query(Campaign)
    
    if start_date:
        query = query.filter(Campaign.created_at >= start_date)
    if end_date:
        query = query.filter(Campaign.created_at <= end_date)
    
    campaigns = query.all()
    
    performance_data = []
    for campaign in campaigns:
        success_rate = 0
        if campaign.sent_count > 0:
            success_rate = (campaign.delivered_count / campaign.sent_count) * 100
        
        performance_data.append({
            "id": campaign.id,
            "name": campaign.name,
            "status": campaign.status,
            "total_contacts": campaign.total_contacts,
            "sent_count": campaign.sent_count,
            "delivered_count": campaign.delivered_count,
            "failed_count": campaign.failed_count,
            "success_rate": round(success_rate, 2),
            "created_at": campaign.created_at.isoformat(),
            "cost_per_message": 0.05,  # تكلفة افتراضية
            "total_cost": campaign.sent_count * 0.05
        })
    
    return {
        "campaigns": performance_data,
        "summary": {
            "total_campaigns": len(campaigns),
            "total_messages_sent": sum([c.sent_count for c in campaigns]),
            "total_delivered": sum([c.delivered_count for c in campaigns]),
            "average_success_rate": round(
                sum([c.delivered_count for c in campaigns]) / 
                sum([c.sent_count for c in campaigns]) * 100, 2
            ) if sum([c.sent_count for c in campaigns]) > 0 else 0
        }
    }

@router.get("/messages/timeline")
async def get_messages_timeline(
    days: int = Query(30, ge=1, le=365),
    db: Session = Depends(get_db)
):
    """الحصول على الجدول الزمني للرسائل"""
    
    start_date = datetime.now() - timedelta(days=days)
    
    # استعلام للحصول على عدد الرسائل يومياً
    daily_messages = db.query(
        func.date(Message.created_at).label('date'),
        func.count(Message.id).label('total'),
        func.sum(case((Message.status == 'delivered', 1), else_=0)).label('delivered'),
        func.sum(case((Message.status == 'failed', 1), else_=0)).label('failed')
    ).filter(
        Message.created_at >= start_date
    ).group_by(
        func.date(Message.created_at)
    ).order_by(
        func.date(Message.created_at)
    ).all()
    
    timeline_data = []
    for day in daily_messages:
        success_rate = (day.delivered / day.total * 100) if day.total > 0 else 0
        timeline_data.append({
            "date": day.date.isoformat(),
            "total_messages": day.total,
            "delivered_messages": day.delivered,
            "failed_messages": day.failed,
            "success_rate": round(success_rate, 2)
        })
    
    return {
        "timeline": timeline_data,
        "period_summary": {
            "total_days": days,
            "total_messages": sum([d["total_messages"] for d in timeline_data]),
            "total_delivered": sum([d["delivered_messages"] for d in timeline_data]),
            "total_failed": sum([d["failed_messages"] for d in timeline_data]),
            "average_daily_messages": round(
                sum([d["total_messages"] for d in timeline_data]) / len(timeline_data), 2
            ) if timeline_data else 0
        }
    }

@router.get("/ai-responses/analytics")
async def get_ai_responses_analytics(
    days: int = Query(30, ge=1, le=365),
    db: Session = Depends(get_db)
):
    """الحصول على تحليلات الردود التلقائية"""
    
    start_date = datetime.now() - timedelta(days=days)
    
    # إجمالي الردود في الفترة
    total_responses = db.query(AIResponse).filter(
        AIResponse.created_at >= start_date
    ).count()
    
    # متوسط وقت الاستجابة
    avg_response_time = db.query(
        func.avg(AIResponse.response_time_ms)
    ).filter(
        AIResponse.created_at >= start_date,
        AIResponse.response_time_ms.isnot(None)
    ).scalar()
    
    # الردود حسب النموذج المستخدم
    responses_by_model = db.query(
        AIResponse.gpt_model_used,
        func.count(AIResponse.id).label('count')
    ).filter(
        AIResponse.created_at >= start_date
    ).group_by(AIResponse.gpt_model_used).all()
    
    # الردود اليومية
    daily_responses = db.query(
        func.date(AIResponse.created_at).label('date'),
        func.count(AIResponse.id).label('count'),
        func.avg(AIResponse.response_time_ms).label('avg_time')
    ).filter(
        AIResponse.created_at >= start_date
    ).group_by(
        func.date(AIResponse.created_at)
    ).order_by(
        func.date(AIResponse.created_at)
    ).all()
    
    # أكثر العملاء تفاعلاً
    top_customers = db.query(
        AIResponse.contact_phone,
        func.count(AIResponse.id).label('response_count')
    ).filter(
        AIResponse.created_at >= start_date
    ).group_by(
        AIResponse.contact_phone
    ).order_by(
        func.count(AIResponse.id).desc()
    ).limit(10).all()
    
    return {
        "summary": {
            "total_responses": total_responses,
            "avg_response_time_ms": round(avg_response_time, 2) if avg_response_time else 0,
            "period_days": days
        },
        "models_usage": [
            {"model": model, "count": count, "percentage": round(count/total_responses*100, 2)}
            for model, count in responses_by_model
        ] if total_responses > 0 else [],
        "daily_timeline": [
            {
                "date": day.date.isoformat(),
                "responses_count": day.count,
                "avg_response_time_ms": round(day.avg_time, 2) if day.avg_time else 0
            }
            for day in daily_responses
        ],
        "top_customers": [
            {"phone": customer.contact_phone, "responses_count": customer.response_count}
            for customer in top_customers
        ]
    }

@router.get("/export/campaigns")
async def export_campaigns_data(
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    format: str = Query("json", regex="^(json|csv)$"),
    db: Session = Depends(get_db)
):
    """تصدير بيانات الحملات"""
    
    query = db.query(Campaign)
    
    if start_date:
        query = query.filter(Campaign.created_at >= start_date)
    if end_date:
        query = query.filter(Campaign.created_at <= end_date)
    
    campaigns = query.all()
    
    export_data = []
    for campaign in campaigns:
        export_data.append({
            "id": campaign.id,
            "name": campaign.name,
            "status": campaign.status,
            "total_contacts": campaign.total_contacts,
            "sent_count": campaign.sent_count,
            "delivered_count": campaign.delivered_count,
            "failed_count": campaign.failed_count,
            "success_rate": round(
                (campaign.delivered_count / campaign.sent_count * 100), 2
            ) if campaign.sent_count > 0 else 0,
            "created_at": campaign.created_at.isoformat(),
            "updated_at": campaign.updated_at.isoformat()
        })
    
    if format == "csv":
        # TODO: تنفيذ تصدير CSV
        return {"message": "تصدير CSV سيتم تنفيذه قريباً"}
    
    return {
        "data": export_data,
        "total_records": len(export_data),
        "export_date": datetime.now().isoformat(),
        "format": format
    }
