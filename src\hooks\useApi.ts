import { useState, useEffect } from 'react';

// Hook عام لاستخدام API
export function useApi<T>(
  apiFunction: () => Promise<T>,
  dependencies: any[] = []
) {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);
      const result = await apiFunction();
      setData(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'حدث خطأ غير متوقع');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, dependencies);

  const refetch = () => {
    fetchData();
  };

  return { data, loading, error, refetch };
}

// Hook للبيانات مع إعادة التحديث التلقائي
export function useApiWithRefresh<T>(
  apiFunction: () => Promise<T>,
  refreshInterval: number = 30000, // 30 ثانية
  dependencies: any[] = []
) {
  const { data, loading, error, refetch } = useApi(apiFunction, dependencies);

  useEffect(() => {
    if (refreshInterval > 0) {
      const interval = setInterval(refetch, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [refetch, refreshInterval]);

  return { data, loading, error, refetch };
}

// Hook للعمليات (POST, PUT, DELETE)
export function useApiMutation<T, P = any>(
  apiFunction: (params: P) => Promise<T>
) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const mutate = async (params: P): Promise<T | null> => {
    try {
      setLoading(true);
      setError(null);
      const result = await apiFunction(params);
      return result;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'حدث خطأ غير متوقع');
      return null;
    } finally {
      setLoading(false);
    }
  };

  return { mutate, loading, error };
}
