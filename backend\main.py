from fastapi import FastAPI, HTTPException, Depends, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
import uvicorn
from typing import List, Optional
import os
from datetime import datetime

# تحقق من توفر pandas
try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False
    print("⚠️ تحذير: pandas غير متوفر - ميزات رفع الملفات معطلة")

from database import get_db, init_db
from models import Campaign, Contact, Message, Settings, AIResponse
from schemas import (
    CampaignCreate, CampaignResponse, CampaignUpdate,
    ContactCreate, ContactResponse,
    MessageCreate, MessageResponse,
    SettingsCreate, SettingsResponse,
    AIResponseCreate, AIResponseResponse,
    DashboardStats
)
from services.whatsapp_web_service import WhatsAppWebService
from services.ai_service import AIService
from services.campaign_service import CampaignService
from whatsapp_routes import router as whatsapp_router
from services.analytics_service import AnalyticsService

# استيراد الـ routers
from routers import campaigns, ai_responses, analytics

# إنشاء تطبيق FastAPI
app = FastAPI(
    title="Smart WhatsApp Campaigner API",
    description="API لإدارة حملات WhatsApp الذكية مع الردود التلقائية",
    version="1.0.0"
)

# إعداد CORS للسماح للفرونت إند بالوصول
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:5173",  # Vite dev server
        "http://localhost:3000",  # React dev server
        "http://127.0.0.1:5173",
        "http://127.0.0.1:3000"
    ],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
    expose_headers=["*"],
)

# تضمين مسارات WhatsApp
app.include_router(whatsapp_router)

# إنشاء قاعدة البيانات عند بدء التطبيق
@app.on_event("startup")
async def startup_event():
    init_db()
    print("✅ تم تهيئة قاعدة البيانات بنجاح")



# الصفحة الرئيسية
@app.get("/")
async def root():
    return {
        "message": "مرحباً بك في Smart WhatsApp Campaigner API",
        "version": "1.0.0",
        "docs": "/docs"
    }

# فحص حالة الخدمة
@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "database": "connected"
    }

# تضمين الـ routers
app.include_router(campaigns.router)
app.include_router(ai_responses.router)
app.include_router(analytics.router)

# إحصائيات لوحة التحكم
@app.get("/api/dashboard/stats", response_model=DashboardStats)
async def get_dashboard_stats(db: Session = Depends(get_db)):
    analytics_service = AnalyticsService(db)
    return analytics_service.get_dashboard_stats()

# تشغيل الخادم
if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
