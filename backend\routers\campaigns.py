from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form
from sqlalchemy.orm import Session
from typing import List, Optional
import pandas as pd
import os
from datetime import datetime
import uuid
import logging

from database import get_db
from models import Campaign, Contact, Message
from schemas import (
    CampaignCreate, CampaignResponse, CampaignUpdate,
    ContactCreate, ContactResponse,
    FileUploadResponse
)
from services.campaign_service import CampaignService
from services.file_service import FileService

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/api/campaigns", tags=["campaigns"])

@router.get("/", response_model=List[CampaignResponse])
async def get_campaigns(
    skip: int = 0,
    limit: int = 100,
    status: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """الحصول على قائمة الحملات"""
    query = db.query(Campaign)
    
    if status:
        query = query.filter(Campaign.status == status)
    
    campaigns = query.offset(skip).limit(limit).all()
    return campaigns

@router.get("/{campaign_id}", response_model=CampaignResponse)
async def get_campaign(campaign_id: int, db: Session = Depends(get_db)):
    """الحصول على حملة محددة"""
    campaign = db.query(Campaign).filter(Campaign.id == campaign_id).first()
    if not campaign:
        raise HTTPException(status_code=404, detail="الحملة غير موجودة")
    return campaign

@router.post("/", response_model=CampaignResponse)
async def create_campaign(
    campaign: CampaignCreate,
    db: Session = Depends(get_db)
):
    """إنشاء حملة جديدة"""
    db_campaign = Campaign(
        name=campaign.name,
        message_text=campaign.message_text,
        image_path=campaign.image_path,
        scheduled_time=campaign.scheduled_time,
        status="pending"
    )
    
    db.add(db_campaign)
    db.commit()
    db.refresh(db_campaign)
    
    return db_campaign

@router.put("/{campaign_id}", response_model=CampaignResponse)
async def update_campaign(
    campaign_id: int,
    campaign_update: CampaignUpdate,
    db: Session = Depends(get_db)
):
    """تحديث حملة موجودة"""
    db_campaign = db.query(Campaign).filter(Campaign.id == campaign_id).first()
    if not db_campaign:
        raise HTTPException(status_code=404, detail="الحملة غير موجودة")
    
    update_data = campaign_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_campaign, field, value)
    
    db_campaign.updated_at = datetime.now()
    db.commit()
    db.refresh(db_campaign)
    
    return db_campaign

@router.delete("/{campaign_id}")
async def delete_campaign(campaign_id: int, db: Session = Depends(get_db)):
    """حذف حملة"""
    db_campaign = db.query(Campaign).filter(Campaign.id == campaign_id).first()
    if not db_campaign:
        raise HTTPException(status_code=404, detail="الحملة غير موجودة")
    
    # حذف الرسائل المرتبطة
    db.query(Message).filter(Message.campaign_id == campaign_id).delete()
    # حذف جهات الاتصال المرتبطة
    db.query(Contact).filter(Contact.campaign_id == campaign_id).delete()
    # حذف الحملة
    db.delete(db_campaign)
    db.commit()
    
    return {"message": "تم حذف الحملة بنجاح"}

@router.options("/{campaign_id}/upload-contacts")
async def upload_contacts_options(campaign_id: int):
    """OPTIONS request للتعامل مع CORS preflight"""
    return {"message": "OK"}

@router.post("/{campaign_id}/upload-contacts", response_model=FileUploadResponse)
async def upload_contacts_file(
    campaign_id: int,
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """رفع ملف العملاء للحملة"""
    # التحقق من وجود الحملة
    campaign = db.query(Campaign).filter(Campaign.id == campaign_id).first()
    if not campaign:
        raise HTTPException(status_code=404, detail="الحملة غير موجودة")
    
    # التحقق من نوع الملف
    if not file.filename.endswith(('.xlsx', '.xls', '.csv')):
        raise HTTPException(
            status_code=400, 
            detail="نوع الملف غير مدعوم. يرجى رفع ملف Excel أو CSV"
        )
    
    try:
        # تسجيل تشخيصي
        logger.info(f"بدء رفع ملف للحملة {campaign_id}: {file.filename}")

        # حفظ الملف مؤقتاً
        file_service = FileService()
        logger.info("إنشاء FileService...")

        file_path = await file_service.save_uploaded_file(file)
        logger.info(f"تم حفظ الملف في: {file_path}")

        # قراءة الملف وإضافة العملاء
        logger.info("بدء معالجة الملف...")
        contacts_count = await file_service.process_contacts_file(
            file_path, campaign_id, db
        )
        logger.info(f"تم معالجة {contacts_count} جهة اتصال")

        # تحديث عدد العملاء في الحملة
        campaign.total_contacts = contacts_count
        db.commit()
        logger.info("تم تحديث الحملة في قاعدة البيانات")

        return FileUploadResponse(
            filename=file.filename,
            file_path=file_path,
            contacts_count=contacts_count,
            message=f"تم رفع {contacts_count} عميل بنجاح"
        )

    except Exception as e:
        logger.error(f"خطأ في رفع ملف للحملة {campaign_id}: {str(e)}")
        logger.error(f"تفاصيل الخطأ: {type(e).__name__}")
        import traceback
        logger.error(f"Stack trace: {traceback.format_exc()}")

        # إرجاع خطأ مفصل
        raise HTTPException(
            status_code=500,
            detail=f"خطأ في معالجة الملف: {str(e)}"
        )

@router.post("/{campaign_id}/send")
async def send_campaign(
    campaign_id: int,
    db: Session = Depends(get_db)
):
    """إرسال الحملة"""
    logger.info(f"🚀 طلب إرسال الحملة {campaign_id}")

    campaign = db.query(Campaign).filter(Campaign.id == campaign_id).first()
    if not campaign:
        logger.error(f"❌ الحملة {campaign_id} غير موجودة")
        raise HTTPException(status_code=404, detail="الحملة غير موجودة")

    logger.info(f"📋 تفاصيل الحملة: {campaign.name} - الحالة: {campaign.status}")

    if campaign.status != "pending":
        logger.error(f"❌ لا يمكن إرسال الحملة. الحالة الحالية: {campaign.status}")
        raise HTTPException(
            status_code=400,
            detail="لا يمكن إرسال الحملة. الحالة الحالية: " + campaign.status
        )

    # التحقق من وجود عملاء
    contacts_count = db.query(Contact).filter(Contact.campaign_id == campaign_id).count()
    logger.info(f"👥 عدد جهات الاتصال: {contacts_count}")

    if contacts_count == 0:
        logger.error("❌ لا توجد جهات اتصال للحملة")
        raise HTTPException(
            status_code=400,
            detail="لا يمكن إرسال الحملة بدون عملاء"
        )
    
    try:
        # بدء إرسال الحملة
        campaign_service = CampaignService(db)
        result = await campaign_service.send_campaign(campaign_id)

        return {
            "message": "تم بدء إرسال الحملة بنجاح",
            "campaign_id": campaign_id,
            "total_contacts": result["total_contacts"],
            "status": "running"
        }

    except ValueError as e:
        # أخطاء منطقية (مثل WhatsApp غير مُسجل دخول)
        error_message = str(e)
        if "WhatsApp Web" in error_message:
            raise HTTPException(
                status_code=400,
                detail=f"❌ {error_message}\n\n💡 الحلول:\n1. اذهب إلى الإعدادات\n2. انتقل لقسم 'WhatsApp Web'\n3. اضغط 'بدء جلسة جديدة'\n4. امسح QR Code بهاتفك"
            )
        else:
            raise HTTPException(status_code=400, detail=error_message)
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"خطأ في إرسال الحملة: {str(e)}"
        )

@router.get("/{campaign_id}/contacts", response_model=List[ContactResponse])
async def get_campaign_contacts(
    campaign_id: int,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """الحصول على عملاء الحملة"""
    contacts = db.query(Contact).filter(
        Contact.campaign_id == campaign_id
    ).offset(skip).limit(limit).all()

    return contacts

@router.post("/{campaign_id}/retry-failed")
async def retry_failed_messages(
    campaign_id: int,
    db: Session = Depends(get_db)
):
    """إعادة إرسال الرسائل الفاشلة للحملة"""
    campaign = db.query(Campaign).filter(Campaign.id == campaign_id).first()
    if not campaign:
        raise HTTPException(status_code=404, detail="الحملة غير موجودة")

    # البحث عن الرسائل الفاشلة
    failed_messages = db.query(Message).filter(
        Message.campaign_id == campaign_id,
        Message.status == "failed"
    ).all()

    if not failed_messages:
        raise HTTPException(
            status_code=400,
            detail="لا توجد رسائل فاشلة لإعادة إرسالها"
        )

    try:
        # بدء إعادة الإرسال
        campaign_service = CampaignService(db)
        result = await campaign_service.retry_failed_messages(campaign_id)

        return {
            "message": "تم بدء إعادة إرسال الرسائل الفاشلة بنجاح",
            "campaign_id": campaign_id,
            "failed_messages_count": len(failed_messages),
            "retry_result": result
        }

    except ValueError as e:
        # أخطاء منطقية (مثل WhatsApp غير مُسجل دخول)
        error_message = str(e)
        if "WhatsApp Web" in error_message:
            raise HTTPException(
                status_code=400,
                detail=f"❌ {error_message}\n\n💡 الحلول:\n1. اذهب إلى الإعدادات\n2. انتقل لقسم 'WhatsApp Web'\n3. اضغط 'تسجيل دخول'\n4. امسح QR Code بهاتفك"
            )
        else:
            raise HTTPException(status_code=400, detail=error_message)
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"خطأ في إعادة إرسال الرسائل الفاشلة: {str(e)}"
        )

@router.get("/{campaign_id}/failed-contacts")
async def get_failed_contacts(
    campaign_id: int,
    db: Session = Depends(get_db)
):
    """الحصول على جهات الاتصال التي فشل إرسال الرسائل إليها"""
    failed_messages = db.query(Message).join(Contact).filter(
        Message.campaign_id == campaign_id,
        Message.status == "failed"
    ).all()

    failed_contacts = []
    for message in failed_messages:
        contact = db.query(Contact).filter(Contact.id == message.contact_id).first()
        if contact:
            failed_contacts.append({
                "contact_id": contact.id,
                "name": contact.name,
                "phone_number": contact.phone_number,
                "error_message": message.error_message,
                "failed_at": message.created_at
            })

    return {
        "campaign_id": campaign_id,
        "failed_contacts": failed_contacts,
        "total_failed": len(failed_contacts)
    }

@router.get("/{campaign_id}/stats")
async def get_campaign_stats(campaign_id: int, db: Session = Depends(get_db)):
    """الحصول على إحصائيات الحملة"""
    campaign = db.query(Campaign).filter(Campaign.id == campaign_id).first()
    if not campaign:
        raise HTTPException(status_code=404, detail="الحملة غير موجودة")
    
    # إحصائيات الرسائل
    total_messages = db.query(Message).filter(Message.campaign_id == campaign_id).count()
    sent_messages = db.query(Message).filter(
        Message.campaign_id == campaign_id,
        Message.status == "sent"
    ).count()
    delivered_messages = db.query(Message).filter(
        Message.campaign_id == campaign_id,
        Message.status == "delivered"
    ).count()
    failed_messages = db.query(Message).filter(
        Message.campaign_id == campaign_id,
        Message.status == "failed"
    ).count()
    
    success_rate = (delivered_messages / total_messages * 100) if total_messages > 0 else 0
    
    return {
        "campaign_id": campaign_id,
        "campaign_name": campaign.name,
        "status": campaign.status,
        "total_contacts": campaign.total_contacts,
        "total_messages": total_messages,
        "sent_messages": sent_messages,
        "delivered_messages": delivered_messages,
        "failed_messages": failed_messages,
        "success_rate": round(success_rate, 2),
        "created_at": campaign.created_at,
        "updated_at": campaign.updated_at
    }

@router.get("/{campaign_id}/messages")
async def get_campaign_messages(
    campaign_id: int,
    db: Session = Depends(get_db)
):
    """الحصول على رسائل الحملة"""
    campaign = db.query(Campaign).filter(Campaign.id == campaign_id).first()
    if not campaign:
        raise HTTPException(status_code=404, detail="الحملة غير موجودة")

    messages = db.query(Message).filter(Message.campaign_id == campaign_id).all()

    # إضافة اسم جهة الاتصال لكل رسالة
    result = []
    for message in messages:
        message_dict = {
            "id": message.id,
            "campaign_id": message.campaign_id,
            "contact_id": message.contact_id,
            "message_text": message.message_text,
            "status": message.status,
            "sent_at": message.sent_at,
            "delivered_at": message.delivered_at,
            "error_message": message.error_message,
            "contact_name": None
        }

        if message.contact_id:
            contact = db.query(Contact).filter(Contact.id == message.contact_id).first()
            if contact:
                message_dict["contact_name"] = contact.name

        result.append(message_dict)

    return result
