@echo off
echo ========================================
echo   Smart WhatsApp Campaigner
echo ========================================
echo.

echo � تشغيل الباك إند...
start "Backend" cmd /k "cd /d "%~dp0backend" && python run_whatsapp_web.py"

echo ⏳ انتظار بدء الباك إند...
timeout /t 3 /nobreak >nul

echo 🌐 تشغيل الفرونت إند...
echo.
echo � الروابط:
echo    Frontend: http://localhost:5173
echo    Backend:  http://localhost:8000
echo    API Docs: http://localhost:8000/docs
echo.

npm run dev
