#!/usr/bin/env python3
"""
اختبار الحل الجذري الموحد للردود الذكية
"""

import asyncio
import sys
import os
from pathlib import Path

# إضافة مسار المشروع
sys.path.append(str(Path(__file__).parent))

from whatsapp_selenium import whatsapp_manager
from services.auto_reply_service import get_auto_reply_service
from services.whatsapp_web_service import WhatsAppWebService
import logging

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_unified_solution():
    """اختبار الحل الجذري الموحد"""
    
    print("🎯 اختبار الحل الجذري الموحد للردود الذكية")
    print("=" * 70)
    
    try:
        # 1. فحص حالة النظام الأساسية
        print("1️⃣ فحص حالة النظام الأساسية...")
        status = whatsapp_manager.get_status()
        print(f"   📊 الجلسة نشطة: {status.get('session_active', False)}")
        print(f"   🔐 مسجل دخول: {status.get('logged_in', False)}")
        print(f"   🌐 المتصفح: {'متاح' if whatsapp_manager.driver else 'غير متاح'}")
        
        if not status.get('session_active') or not status.get('logged_in'):
            print("   ❌ WhatsApp Web غير مُسجل دخول")
            print("   💡 يرجى تسجيل الدخول من الإعدادات أولاً")
            return False
        
        # 2. اختبار خدمة WhatsApp Web الموحدة
        print("\n2️⃣ اختبار خدمة WhatsApp Web الموحدة...")
        whatsapp_service = WhatsAppWebService(whatsapp_manager)
        
        connection_test = await whatsapp_service.test_connection()
        print(f"   📊 نتيجة اختبار الاتصال: {connection_test}")
        
        if not connection_test.get('success', False):
            print("   ❌ فشل في اختبار الاتصال")
            return False
        
        print("   ✅ خدمة WhatsApp Web الموحدة تعمل بنجاح")
        
        # 3. إنشاء خدمة الردود الذكية المحسنة
        print("\n3️⃣ إنشاء خدمة الردود الذكية المحسنة...")
        auto_reply = get_auto_reply_service(whatsapp_manager)
        
        print(f"   📊 حالة الخدمة: {'تعمل' if auto_reply.is_running else 'متوقفة'}")
        print(f"   🧠 خدمة AI: {'جاهزة' if auto_reply.ai_service else 'غير جاهزة'}")
        
        # 4. اختبار بدء الخدمة المحسنة
        print("\n4️⃣ اختبار بدء الخدمة المحسنة...")
        
        try:
            await auto_reply.start_auto_reply()
            print("   ✅ تم بدء خدمة الردود الذكية بنجاح!")
            
            # فحص الحالة بعد البدء
            print(f"   📊 حالة الخدمة بعد البدء: {'تعمل' if auto_reply.is_running else 'متوقفة'}")
            print(f"   🎯 مهمة المراقبة: {'موجودة' if auto_reply.monitor_task else 'غير موجودة'}")
            print(f"   🔗 الخدمة الموحدة: {'متاحة' if auto_reply.whatsapp_service else 'غير متاحة'}")
            
            if auto_reply.monitor_task:
                print(f"   🔄 حالة المهمة: {'تعمل' if not auto_reply.monitor_task.done() else 'متوقفة'}")
            
            # 5. اختبار البحث عن الرسائل
            print("\n5️⃣ اختبار البحث عن الرسائل...")
            messages = await auto_reply._get_new_messages()
            print(f"   📨 تم العثور على {len(messages)} رسالة جديدة")
            
            if messages:
                for i, msg in enumerate(messages):
                    print(f"   📱 رسالة {i+1}: من {msg['contact_name']}: {msg['message_text'][:30]}...")
            
            # 6. اختبار البحث الموحد
            print("\n6️⃣ اختبار البحث الموحد...")
            unread_chats = await auto_reply._find_unread_chats_unified()
            print(f"   💬 تم العثور على {len(unread_chats)} محادثة غير مقروءة")
            
            # 7. اختبار الفحص النهائي
            print("\n7️⃣ اختبار الفحص النهائي...")
            final_check = await auto_reply._final_system_check()
            print(f"   🔍 نتيجة الفحص النهائي: {'نجح' if final_check else 'فشل'}")
            
            # إيقاف الخدمة
            await auto_reply.stop_auto_reply()
            print("   ✅ تم إيقاف الخدمة بنجاح")
            
            return True
            
        except Exception as e:
            print(f"   ❌ خطأ في اختبار الخدمة: {e}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_session_consistency():
    """اختبار تطابق الجلسات"""
    
    print("\n🔗 اختبار تطابق الجلسات")
    print("=" * 70)
    
    try:
        auto_reply = get_auto_reply_service(whatsapp_manager)
        
        # مقارنة المتصفحات
        manager_driver = whatsapp_manager.driver
        service_driver = auto_reply.driver if hasattr(auto_reply, 'driver') else None
        
        print(f"   🌐 Manager driver: {'متاح' if manager_driver else 'غير متاح'}")
        print(f"   🌐 Service driver: {'متاح' if service_driver else 'غير متاح'}")
        print(f"   🔍 نفس المتصفح: {manager_driver is service_driver}")
        
        if manager_driver and service_driver:
            try:
                manager_url = manager_driver.current_url
                service_url = service_driver.current_url
                print(f"   🔗 Manager URL: {manager_url}")
                print(f"   🔗 Service URL: {service_url}")
                print(f"   ✅ نفس URL: {manager_url == service_url}")
            except Exception as e:
                print(f"   ❌ خطأ في مقارنة URLs: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الجلسات: {e}")
        return False

async def test_message_detection():
    """اختبار اكتشاف الرسائل"""
    
    print("\n📨 اختبار اكتشاف الرسائل")
    print("=" * 70)
    
    try:
        auto_reply = get_auto_reply_service(whatsapp_manager)
        
        # اختبار البحث الموحد
        print("   🔍 اختبار البحث الموحد...")
        unread_chats = await auto_reply._find_unread_chats_unified()
        print(f"   💬 محادثات غير مقروءة: {len(unread_chats)}")
        
        # اختبار قراءة الرسائل
        if unread_chats:
            print("   📖 اختبار قراءة الرسائل...")
            for i, chat in enumerate(unread_chats[:2]):  # اختبار أول محادثتين
                try:
                    success = await auto_reply._click_chat_unified(chat)
                    if success:
                        await asyncio.sleep(2)
                        message = await auto_reply._read_last_message_unified()
                        if message:
                            print(f"   ✅ رسالة {i+1}: {message['contact_name']}: {message['message_text'][:30]}...")
                        else:
                            print(f"   ⚠️ لم يتم العثور على رسالة في المحادثة {i+1}")
                    else:
                        print(f"   ❌ فشل في فتح المحادثة {i+1}")
                except Exception as e:
                    print(f"   ❌ خطأ في معالجة المحادثة {i+1}: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار اكتشاف الرسائل: {e}")
        return False

async def main():
    """الدالة الرئيسية"""
    
    print("🚀 بدء اختبار الحل الجذري الموحد")
    print("=" * 70)
    
    # اختبار الحل الموحد
    unified_ok = await test_unified_solution()
    
    # اختبار تطابق الجلسات
    session_ok = await test_session_consistency()
    
    # اختبار اكتشاف الرسائل
    detection_ok = await test_message_detection()
    
    print("\n" + "=" * 70)
    print("📊 نتائج الاختبار الشامل:")
    print(f"   🎯 الحل الموحد: {'✅ نجح' if unified_ok else '❌ فشل'}")
    print(f"   🔗 تطابق الجلسات: {'✅ نجح' if session_ok else '❌ فشل'}")
    print(f"   📨 اكتشاف الرسائل: {'✅ نجح' if detection_ok else '❌ فشل'}")
    
    if unified_ok and session_ok and detection_ok:
        print("\n🎉 تم حل المشكلة بنجاح! الحل الجذري يعمل!")
        print("💡 الآن يمكنك تشغيل الردود الذكية من الواجهة")
        print("🔧 التحسينات المطبقة:")
        print("   ✅ استخدام خدمة WhatsApp Web موحدة")
        print("   ✅ نفس منطق البحث المستخدم في الحملات")
        print("   ✅ نفس منطق إرسال الرسائل")
        print("   ✅ فحص شامل للنظام قبل البدء")
        print("   ✅ معالجة أخطاء محسنة")
    else:
        print("\n⚠️ ما زالت هناك مشاكل تحتاج إلى إصلاح")
        print("💡 تحقق من:")
        print("   - تسجيل الدخول في WhatsApp Web")
        print("   - إعدادات OpenAI API")
        print("   - حالة المتصفح")

if __name__ == "__main__":
    asyncio.run(main())
