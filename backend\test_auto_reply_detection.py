#!/usr/bin/env python3
"""
اختبار اكتشاف الرسائل الجديدة في نظام الردود الذكية
"""

import asyncio
import sys
import os
from pathlib import Path

# إضافة مسار المشروع
sys.path.append(str(Path(__file__).parent))

from whatsapp_selenium import whatsapp_manager
from services.auto_reply_service import get_auto_reply_service
import logging

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_message_detection():
    """اختبار اكتشاف الرسائل الجديدة"""

    print("🧪 اختبار اكتشاف الرسائل الجديدة")
    print("=" * 60)

    try:
        # إنشاء خدمة الردود التلقائية
        auto_reply = get_auto_reply_service(whatsapp_manager)

        # التحقق من حالة WhatsApp Web
        print("🔍 فحص حالة WhatsApp Web...")
        status = whatsapp_manager.get_status()
        print(f"📊 حالة WhatsApp: {status}")

        if not status.get('session_active') or not status.get('logged_in'):
            print("❌ WhatsApp Web غير مُسجل دخول")
            print("💡 تأكد من تسجيل الدخول في WhatsApp Web أولاً")
            return False

        print("✅ WhatsApp Web متصل")

        # اختبار فحص الاتصال
        print("\n🔗 اختبار فحص الاتصال...")
        connection_ok = auto_reply._check_whatsapp_connection()
        print(f"📡 حالة الاتصال: {'✅ متصل' if connection_ok else '❌ غير متصل'}")

        # اختبار فحص انشغال النظام
        print("\n⚙️ اختبار فحص انشغال النظام...")
        is_busy = await auto_reply._is_system_busy()
        print(f"🔄 حالة النظام: {'⏸️ مشغول' if is_busy else '✅ متاح'}")

        # اختبار اكتشاف الرسائل الجديدة
        print("\n🔍 البحث عن رسائل جديدة...")
        print("💡 تأكد من وجود رسائل غير مقروءة في WhatsApp Web")

        new_messages = await auto_reply._get_new_messages()

        if new_messages:
            print(f"✅ تم العثور على {len(new_messages)} رسالة جديدة:")
            for i, msg in enumerate(new_messages, 1):
                print(f"  {i}. من: {msg['contact_name']}")
                print(f"     الرسالة: {msg['message_text'][:50]}...")
                print(f"     الهاتف: {msg['phone_number']}")
                print(f"     الوقت: {msg['message_time']}")
                print(f"     معرف الرسالة: {msg['message_id']}")
                print()

            # اختبار معالجة أول رسالة
            if len(new_messages) > 0:
                print("🧪 اختبار معالجة أول رسالة...")
                first_message = new_messages[0]
                await auto_reply._process_message(first_message)
                print("✅ تم اختبار معالجة الرسالة")
        else:
            print("ℹ️ لا توجد رسائل جديدة حالياً")
            print("💡 لاختبار النظام:")
            print("   1. أرسل رسالة لنفسك من هاتف آخر")
            print("   2. تأكد من ظهور عداد الرسائل غير المقروءة")
            print("   3. أعد تشغيل الاختبار")

        return True

    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_manual_message_processing():
    """اختبار معالجة رسالة يدوياً"""
    
    print("\n🧪 اختبار معالجة رسالة يدوياً")
    print("=" * 60)
    
    try:
        # إنشاء خدمة الردود التلقائية
        auto_reply = get_auto_reply_service(whatsapp_manager)
        
        # رسالة تجريبية
        test_message = {
            "contact_name": "عميل تجريبي",
            "phone_number": "+966501234567",
            "message_text": "مرحباً، أريد معلومات عن خدماتكم",
            "message_id": "test_123",
            "message_time": "12:00",
            "timestamp": "2024-01-01 12:00:00"
        }
        
        print(f"📨 معالجة رسالة تجريبية من: {test_message['contact_name']}")
        print(f"📝 نص الرسالة: {test_message['message_text']}")
        
        # معالجة الرسالة
        await auto_reply._process_message(test_message)
        
        print("✅ تم اختبار معالجة الرسالة")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار معالجة الرسالة: {e}")
        return False

async def main():
    """الدالة الرئيسية"""
    
    print("🚀 بدء اختبار نظام الردود الذكية")
    print("=" * 60)
    
    # اختبار اكتشاف الرسائل
    detection_ok = await test_message_detection()
    
    # اختبار معالجة الرسائل
    processing_ok = await test_manual_message_processing()
    
    print("\n" + "=" * 60)
    print("📊 ملخص الاختبارات:")
    print(f"   🔍 اكتشاف الرسائل: {'✅ نجح' if detection_ok else '❌ فشل'}")
    print(f"   ⚙️ معالجة الرسائل: {'✅ نجح' if processing_ok else '❌ فشل'}")
    
    if detection_ok and processing_ok:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("💡 يمكنك الآن تشغيل نظام الردود الذكية")
    else:
        print("\n⚠️ هناك مشاكل تحتاج إلى إصلاح")
        print("💡 تحقق من:")
        print("   - تسجيل الدخول في WhatsApp Web")
        print("   - إعدادات OpenAI API")
        print("   - اتصال الإنترنت")

if __name__ == "__main__":
    asyncio.run(main())
