#!/bin/bash

echo "========================================"
echo "   Smart WhatsApp Campaigner"
echo "   تشغيل المشروع الكامل"
echo "========================================"
echo

echo "🔧 تحضير البيئة..."

# التحقق من وجود Python
if ! command -v python3 &> /dev/null; then
    echo "❌ Python غير مثبت. يرجى تثبيت Python 3.8+ أولاً"
    exit 1
fi

# التحقق من وجود Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js غير مثبت. يرجى تثبيت Node.js أولاً"
    exit 1
fi

echo "✅ Python و Node.js متوفران"

# تثبيت مكتبات Python
echo
echo "📦 تثبيت مكتبات Python..."
cd backend

# إنشاء بيئة افتراضية إذا لم تكن موجودة
if [ ! -d "venv" ]; then
    echo "🔧 إنشاء البيئة الافتراضية..."
    python3 -m venv venv
fi

# تفعيل البيئة الافتراضية
source venv/bin/activate

# تثبيت المكتبات
echo "🔧 تثبيت المكتبات الأساسية..."
pip install -r requirements-minimal.txt
if [ $? -ne 0 ]; then
    echo "⚠️ فشل في تثبيت بعض المكتبات، محاولة تثبيت فردي..."
    pip install fastapi uvicorn sqlalchemy pydantic aiohttp python-dotenv
    if [ $? -ne 0 ]; then
        echo "❌ فشل في تثبيت المكتبات الأساسية"
        exit 1
    fi
fi

# إنشاء ملف .env إذا لم يكن موجوداً
if [ ! -f ".env" ]; then
    echo "📝 إنشاء ملف الإعدادات..."
    cp .env.example .env
    echo "⚠️  يرجى تعديل ملف backend/.env وإضافة مفاتيح API الخاصة بك"
fi

cd ..

# تثبيت مكتبات Node.js
echo
echo "📦 تثبيت مكتبات Node.js..."
npm install
if [ $? -ne 0 ]; then
    echo "❌ فشل في تثبيت مكتبات Node.js"
    exit 1
fi

echo
echo "✅ تم تحضير البيئة بنجاح!"
echo
echo "🚀 بدء تشغيل الخوادم..."
echo

# تشغيل الباك إند في الخلفية
echo "🔧 تشغيل الباك إند..."
cd backend
source venv/bin/activate
python run.py &
BACKEND_PID=$!

cd ..

# انتظار قليل لبدء الباك إند
sleep 3

# تشغيل الفرونت إند
echo "🌐 تشغيل الفرونت إند..."
npm run dev

# إيقاف الباك إند عند إنهاء الفرونت إند
kill $BACKEND_PID 2>/dev/null
