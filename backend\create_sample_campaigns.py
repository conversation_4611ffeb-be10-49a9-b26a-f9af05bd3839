"""
إنشاء حملات تجريبية لاختبار النظام
"""

from sqlalchemy.orm import Session
from datetime import datetime, timedelta
from database import get_db, init_db
from models import Campaign, Contact, Message

def create_sample_campaigns():
    """إنشاء حملات تجريبية"""
    
    # تهيئة قاعدة البيانات
    init_db()
    
    # الحصول على جلسة قاعدة البيانات
    db = next(get_db())
    
    try:
        # التحقق من وجود حملات
        existing_campaigns = db.query(Campaign).count()
        
        if existing_campaigns > 0:
            print(f"✅ توجد {existing_campaigns} حملة في قاعدة البيانات")
            
            # عرض الحملات الموجودة
            campaigns = db.query(Campaign).all()
            for campaign in campaigns:
                contacts_count = db.query(Contact).filter(Contact.campaign_id == campaign.id).count()
                messages_count = db.query(Message).filter(Message.campaign_id == campaign.id).count()
                print(f"  - {campaign.name} (ID: {campaign.id})")
                print(f"    الحالة: {campaign.status}")
                print(f"    جهات الاتصال: {contacts_count}")
                print(f"    الرسائل: {messages_count}")
            
            return campaigns
        
        print("🔧 إنشاء حملات تجريبية...")
        
        # إنشاء حملات تجريبية
        sample_campaigns = [
            {
                "name": "حملة الصيف الحراقي",
                "message_text": "🌞 عروض الصيف الحراقي! خصومات تصل إلى 70% على جميع المنتجات. لا تفوت الفرصة! 🔥",
                "status": "pending"
            },
            {
                "name": "حملة العودة للمدارس",
                "message_text": "📚 استعد للعام الدراسي الجديد مع عروضنا الخاصة! خصم 30% على جميع الأدوات المدرسية.",
                "status": "completed"
            },
            {
                "name": "حملة اليوم الوطني",
                "message_text": "🇸🇦 كل عام وأنتم بخير بمناسبة اليوم الوطني السعودي! عروض خاصة لمدة محدودة.",
                "status": "running"
            },
            {
                "name": "حملة الجمعة البيضاء",
                "message_text": "⚡ الجمعة البيضاء وصلت! خصومات هائلة على جميع المنتجات. تسوق الآن قبل نفاد الكمية!",
                "status": "pending"
            },
            {
                "name": "حملة رمضان كريم",
                "message_text": "🌙 رمضان كريم! عروض خاصة طوال الشهر الفضيل. بارك الله لكم في هذا الشهر المبارك.",
                "status": "completed"
            }
        ]
        
        created_campaigns = []
        
        for i, campaign_data in enumerate(sample_campaigns):
            # إنشاء الحملة
            campaign = Campaign(
                name=campaign_data["name"],
                message_text=campaign_data["message_text"],
                status=campaign_data["status"],
                created_at=datetime.now() - timedelta(days=i*7),  # حملات في أوقات مختلفة
                updated_at=datetime.now() - timedelta(days=i*7)
            )
            
            db.add(campaign)
            db.commit()
            db.refresh(campaign)
            
            created_campaigns.append(campaign)
            print(f"✅ تم إنشاء حملة: {campaign.name} (ID: {campaign.id})")
            
            # إضافة جهات اتصال تجريبية لبعض الحملات
            if i < 3:  # أول 3 حملات فقط
                sample_contacts = [
                    {"name": "أحمد محمد", "phone": "+966501234567", "email": "<EMAIL>"},
                    {"name": "فاطمة علي", "phone": "+966502345678", "email": "<EMAIL>"},
                    {"name": "محمد سالم", "phone": "+966503456789", "email": "<EMAIL>"},
                    {"name": "نورا أحمد", "phone": "+966504567890", "email": "<EMAIL>"},
                    {"name": "خالد الغامدي", "phone": "+966505678901", "email": "<EMAIL>"}
                ]
                
                for contact_data in sample_contacts:
                    contact = Contact(
                        campaign_id=campaign.id,
                        name=contact_data["name"],
                        phone_number=contact_data["phone"],
                        email=contact_data["email"],
                        created_at=datetime.now()
                    )
                    
                    db.add(contact)
                
                db.commit()
                print(f"  📞 تم إضافة {len(sample_contacts)} جهة اتصال")
                
                # إضافة رسائل تجريبية للحملات المكتملة
                if campaign_data["status"] == "completed":
                    contacts = db.query(Contact).filter(Contact.campaign_id == campaign.id).all()
                    
                    for contact in contacts:
                        message = Message(
                            campaign_id=campaign.id,
                            contact_id=contact.id,
                            message_text=campaign.message_text,
                            status="sent",
                            sent_at=datetime.now() - timedelta(days=i*7, hours=2),
                            delivered_at=datetime.now() - timedelta(days=i*7, hours=1)
                        )
                        
                        db.add(message)
                    
                    db.commit()
                    print(f"  📨 تم إضافة {len(contacts)} رسالة مرسلة")
        
        print(f"\n🎉 تم إنشاء {len(created_campaigns)} حملة تجريبية بنجاح!")
        return created_campaigns
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الحملات التجريبية: {e}")
        db.rollback()
        return None
    finally:
        db.close()

def show_campaigns_summary():
    """عرض ملخص الحملات"""
    
    db = next(get_db())
    
    try:
        campaigns = db.query(Campaign).all()
        
        print("\n📊 ملخص الحملات:")
        print("=" * 50)
        
        if not campaigns:
            print("❌ لا توجد حملات في قاعدة البيانات")
            return
        
        status_counts = {}
        total_contacts = 0
        total_messages = 0
        
        for campaign in campaigns:
            contacts_count = db.query(Contact).filter(Contact.campaign_id == campaign.id).count()
            messages_count = db.query(Message).filter(Message.campaign_id == campaign.id).count()
            
            total_contacts += contacts_count
            total_messages += messages_count
            
            if campaign.status in status_counts:
                status_counts[campaign.status] += 1
            else:
                status_counts[campaign.status] = 1
            
            print(f"📢 {campaign.name}")
            print(f"   الحالة: {campaign.status}")
            print(f"   جهات الاتصال: {contacts_count}")
            print(f"   الرسائل: {messages_count}")
            print(f"   تاريخ الإنشاء: {campaign.created_at}")
            print()
        
        print("📈 الإحصائيات العامة:")
        print(f"   إجمالي الحملات: {len(campaigns)}")
        print(f"   إجمالي جهات الاتصال: {total_contacts}")
        print(f"   إجمالي الرسائل: {total_messages}")
        print()
        
        print("📊 توزيع الحملات حسب الحالة:")
        for status, count in status_counts.items():
            print(f"   {status}: {count}")
        
    except Exception as e:
        print(f"❌ خطأ في عرض ملخص الحملات: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    print("🔧 إنشاء حملات تجريبية لاختبار النظام...")
    print("=" * 60)
    
    # إنشاء الحملات التجريبية
    campaigns = create_sample_campaigns()
    
    if campaigns:
        # عرض ملخص الحملات
        show_campaigns_summary()
        
        print("\n✅ النظام جاهز للاختبار!")
        print("🚀 يمكنك الآن:")
        print("   1. فتح صفحة الحملات")
        print("   2. مشاهدة الحملات التجريبية")
        print("   3. اختبار إرسال الحملات")
        print("   4. إنشاء حملات جديدة")
    else:
        print("\n❌ فشل في إنشاء الحملات التجريبية")
    
    print("=" * 60)
    input("اضغط Enter للخروج...")
