#!/usr/bin/env python3
"""
اختبار مخصص لاكتشاف الرسائل الجديدة
"""

import asyncio
import sys
import os
from pathlib import Path

# إضافة مسار المشروع
sys.path.append(str(Path(__file__).parent))

from whatsapp_selenium import whatsapp_manager
from services.auto_reply_service import get_auto_reply_service
import logging

# إعداد التسجيل
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_detection_methods():
    """اختبار طرق اكتشاف الرسائل المختلفة"""
    
    print("🧪 اختبار طرق اكتشاف الرسائل الجديدة")
    print("=" * 60)
    
    try:
        # إنشاء خدمة الردود التلقائية
        auto_reply = get_auto_reply_service(whatsapp_manager)
        
        # التحقق من حالة WhatsApp Web
        print("🔍 فحص حالة WhatsApp Web...")
        status = whatsapp_manager.get_status()
        
        if not status.get('session_active') or not status.get('logged_in'):
            print("❌ WhatsApp Web غير مُسجل دخول")
            print("💡 يرجى تسجيل الدخول في WhatsApp Web أولاً")
            return False
        
        print("✅ WhatsApp Web متصل ومُسجل دخول")
        
        # اختبار الطريقة الأولى: البحث المتقدم
        print("\n🔍 اختبار الطريقة الأولى: البحث المتقدم...")
        advanced_chats = await auto_reply._find_unread_chats_advanced()
        print(f"📊 النتيجة: {len(advanced_chats)} محادثة")
        
        # اختبار الطريقة الثانية: البحث التقليدي
        print("\n🔍 اختبار الطريقة الثانية: البحث التقليدي...")
        traditional_chats = await auto_reply._find_unread_chats_traditional()
        print(f"📊 النتيجة: {len(traditional_chats)} محادثة")
        
        # اختبار الطريقة الثالثة: الفحص الشامل
        print("\n🔍 اختبار الطريقة الثالثة: الفحص الشامل...")
        scan_chats = await auto_reply._find_unread_chats_scan_all()
        print(f"📊 النتيجة: {len(scan_chats)} محادثة")
        
        # اختبار الطريقة المدمجة
        print("\n🔍 اختبار الطريقة المدمجة...")
        all_messages = await auto_reply._get_new_messages()
        print(f"📊 النتيجة النهائية: {len(all_messages)} رسالة جديدة")
        
        if all_messages:
            print("\n📨 تفاصيل الرسائل المكتشفة:")
            for i, msg in enumerate(all_messages, 1):
                print(f"  {i}. من: {msg['contact_name']}")
                print(f"     الرسالة: {msg['message_text'][:50]}...")
                print(f"     الهاتف: {msg['phone_number']}")
                print()
        
        # اختبار إضافي: فحص العناصر الموجودة في الصفحة
        print("\n🔍 فحص العناصر الموجودة في الصفحة...")
        await test_page_elements(auto_reply)
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_page_elements(auto_reply):
    """فحص العناصر الموجودة في الصفحة"""
    try:
        driver = auto_reply.driver
        
        # فحص المحادثات العامة
        all_chats = driver.find_elements("css selector", "div[data-testid='cell-frame-container']")
        print(f"📊 إجمالي المحادثات في الصفحة: {len(all_chats)}")
        
        # فحص عدادات الرسائل
        counters = driver.find_elements("css selector", "span[data-testid='icon-unread-count']")
        print(f"📊 عدادات الرسائل الموجودة: {len(counters)}")
        
        # فحص النقاط الخضراء
        green_dots = driver.find_elements("css selector", "span[aria-label*='unread']")
        print(f"📊 النقاط الخضراء: {len(green_dots)}")
        
        # فحص العناصر مع خلفية
        bg_elements = driver.find_elements("css selector", "div[data-testid='cell-frame-container'] *[style*='background']")
        print(f"📊 عناصر مع خلفية: {len(bg_elements)}")
        
        # فحص أول 3 محادثات بالتفصيل
        print("\n🔍 فحص أول 3 محادثات بالتفصيل:")
        for i, chat in enumerate(all_chats[:3]):
            print(f"\n  المحادثة #{i+1}:")
            try:
                # النص الكامل
                chat_text = chat.text
                print(f"    النص: {chat_text[:100]}...")
                
                # البحث عن عدادات داخلية
                internal_counters = chat.find_elements("css selector", "span[data-testid='icon-unread-count']")
                print(f"    عدادات داخلية: {len(internal_counters)}")
                
                # البحث عن نقاط خضراء داخلية
                internal_dots = chat.find_elements("css selector", "span[aria-label*='unread']")
                print(f"    نقاط خضراء داخلية: {len(internal_dots)}")
                
                # فحص الخلفية
                style = chat.get_attribute("style")
                print(f"    الستايل: {style[:50] if style else 'لا يوجد'}...")
                
            except Exception as e:
                print(f"    خطأ في فحص المحادثة: {e}")
        
    except Exception as e:
        print(f"❌ خطأ في فحص العناصر: {e}")

async def main():
    """الدالة الرئيسية"""
    
    print("🚀 بدء اختبار اكتشاف الرسائل الجديدة")
    print("=" * 60)
    print("💡 تأكد من وجود رسائل غير مقروءة في WhatsApp Web")
    print("=" * 60)
    
    success = await test_detection_methods()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 تم الانتهاء من الاختبار!")
        print("💡 راجع النتائج أعلاه لمعرفة أي طريقة تعمل بشكل أفضل")
    else:
        print("⚠️ فشل الاختبار")
        print("💡 تحقق من:")
        print("   - تسجيل الدخول في WhatsApp Web")
        print("   - وجود رسائل غير مقروءة")
        print("   - اتصال الإنترنت")

if __name__ == "__main__":
    asyncio.run(main())
