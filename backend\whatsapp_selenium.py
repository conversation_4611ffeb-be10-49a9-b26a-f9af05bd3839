"""
WhatsApp Web Selenium Integration
إدارة تسجيل الدخول والجلسات لـ WhatsApp Web
"""

import os
import json
import time
import pickle
from typing import Optional, Dict, Any
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException
from webdriver_manager.chrome import ChromeDriverManager
import logging

logger = logging.getLogger(__name__)

class WhatsAppWebManager:
    def __init__(self, headless: bool = False, user_data_dir: str = None):
        self.driver: Optional[webdriver.Chrome] = None
        self.headless = headless
        self.user_data_dir = user_data_dir or os.path.join(os.getcwd(), "whatsapp_session")
        self.session_file = os.path.join(self.user_data_dir, "session.pkl")
        self.is_logged_in = False
        
        # إنشاء مجلد الجلسة إذا لم يكن موجوداً
        os.makedirs(self.user_data_dir, exist_ok=True)
    
    def setup_driver(self) -> webdriver.Chrome:
        """إعداد متصفح Chrome مع الخيارات المطلوبة"""
        chrome_options = Options()
        
        # إعدادات أساسية
        chrome_options.add_argument(f"--user-data-dir={self.user_data_dir}")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # تشغيل في الخلفية إذا كان مطلوباً
        if self.headless:
            chrome_options.add_argument("--headless")
        
        # إعدادات إضافية لتحسين الأداء
        chrome_options.add_argument("--disable-extensions")
        chrome_options.add_argument("--disable-plugins")
        chrome_options.add_argument("--disable-images")
        
        try:
            # تحميل ChromeDriver تلقائياً
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=chrome_options)
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            return driver
        except Exception as e:
            logger.error(f"فشل في إعداد المتصفح: {e}")
            raise
    
    def start_session(self) -> Dict[str, Any]:
        """بدء جلسة WhatsApp Web"""
        try:
            self.driver = self.setup_driver()
            self.driver.get("https://web.whatsapp.com")
            
            # التحقق من وجود جلسة محفوظة
            if self.check_existing_session():
                return {
                    "success": True,
                    "message": "تم تسجيل الدخول تلقائياً باستخدام الجلسة المحفوظة",
                    "requires_qr": False,
                    "session_active": True
                }
            else:
                return {
                    "success": True,
                    "message": "يرجى مسح رمز QR لتسجيل الدخول",
                    "requires_qr": True,
                    "session_active": False
                }
                
        except Exception as e:
            logger.error(f"خطأ في بدء الجلسة: {e}")
            return {
                "success": False,
                "message": f"خطأ في بدء الجلسة: {str(e)}",
                "requires_qr": False,
                "session_active": False
            }
    
    def check_existing_session(self) -> bool:
        """التحقق من وجود جلسة نشطة بدقة عالية"""
        try:
            import time

            # انتظار تحميل الصفحة
            logger.info("انتظار تحميل WhatsApp Web...")
            WebDriverWait(self.driver, 20).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )

            # انتظار إضافي لتحميل WhatsApp Web بالكامل
            time.sleep(8)

            # أولاً: فحص وجود QR Code (أولوية عالية)
            qr_indicators = [
                "[data-ref]",
                "canvas[aria-label]",
                ".qr-code",
                "[data-testid='qr-code']",
                "div[data-ref]"
            ]

            for qr_indicator in qr_indicators:
                try:
                    qr_elements = self.driver.find_elements(By.CSS_SELECTOR, qr_indicator)
                    for qr_element in qr_elements:
                        if qr_element and qr_element.is_displayed():
                            logger.info(f"تم العثور على QR Code: {qr_indicator} - يتطلب تسجيل دخول")
                            return False
                except Exception as e:
                    continue

            # ثانياً: فحص النص في الصفحة للتأكد من عدم وجود رسائل تسجيل الدخول
            try:
                page_text = self.driver.find_element(By.TAG_NAME, "body").text.lower()
                login_phrases = [
                    "scan this qr code",
                    "use whatsapp on your phone",
                    "point your phone",
                    "open whatsapp",
                    "scan the qr code",
                    "to use whatsapp on your computer"
                ]

                for phrase in login_phrases:
                    if phrase in page_text:
                        logger.info(f"تم العثور على نص تسجيل الدخول: '{phrase}' - يتطلب QR Code")
                        return False
            except:
                pass

            # ثالثاً: فحص مؤشرات تسجيل الدخول الناجح
            login_indicators = [
                "[data-testid='chat-list']",
                "[data-testid='sidebar']",
                "[data-testid='menu']",
                ".app-wrapper-web",
                "[data-testid='conversation-compose-box-input']",
                "[data-testid='search']",
                "[data-testid='new-chat-btn']"
            ]

            successful_indicators = 0
            for indicator in login_indicators:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, indicator)
                    for element in elements:
                        if element and element.is_displayed():
                            successful_indicators += 1
                            logger.info(f"تم العثور على مؤشر تسجيل الدخول: {indicator}")
                            break
                except:
                    continue

            # إذا وُجد أكثر من مؤشرين، نعتبر أن تسجيل الدخول ناجح
            if successful_indicators >= 2:
                self.is_logged_in = True
                self.save_session()
                logger.info(f"تم تأكيد تسجيل الدخول - عدد المؤشرات: {successful_indicators}")
                return True

            # رابعاً: فحص عنوان الصفحة
            try:
                page_title = self.driver.title.lower()
                if "whatsapp" in page_title and "web" in page_title:
                    # إذا كان العنوان يحتوي على WhatsApp Web ولم نجد QR Code
                    logger.info("عنوان الصفحة يشير إلى WhatsApp Web ولا يوجد QR Code")
                    # فحص إضافي للتأكد
                    time.sleep(3)
                    # إعادة فحص QR Code
                    qr_found = False
                    for qr_indicator in qr_indicators:
                        try:
                            qr_elements = self.driver.find_elements(By.CSS_SELECTOR, qr_indicator)
                            if qr_elements and any(el.is_displayed() for el in qr_elements):
                                qr_found = True
                                break
                        except:
                            continue

                    if not qr_found:
                        logger.info("لا يوجد QR Code - قد يكون تم تسجيل الدخول")
                        self.is_logged_in = True
                        self.save_session()
                        return True
            except:
                pass

            # إذا لم نجد مؤشرات واضحة، نفترض عدم تسجيل الدخول للأمان
            logger.warning("لم يتم العثور على مؤشرات واضحة - يتطلب تسجيل دخول")
            return False

        except Exception as e:
            logger.error(f"خطأ في التحقق من الجلسة: {e}")
            return False
    
    def wait_for_login(self, timeout: int = 60) -> Dict[str, Any]:
        """انتظار تسجيل الدخول عبر QR Code"""
        try:
            import time

            # انتظار مع فحص دوري
            start_time = time.time()
            check_interval = 2  # فحص كل ثانيتين

            while time.time() - start_time < timeout:
                try:
                    # فحص وجود قائمة الدردشات
                    chat_list = self.driver.find_element(By.CSS_SELECTOR, "[data-testid='chat-list']")
                    if chat_list:
                        self.is_logged_in = True
                        self.save_session()
                        return {
                            "success": True,
                            "message": "تم تسجيل الدخول بنجاح!",
                            "session_active": True
                        }
                except:
                    # فحص وجود الشريط الجانبي (علامة أخرى على تسجيل الدخول)
                    try:
                        sidebar = self.driver.find_element(By.CSS_SELECTOR, "[data-testid='sidebar']")
                        if sidebar:
                            self.is_logged_in = True
                            self.save_session()
                            return {
                                "success": True,
                                "message": "تم تسجيل الدخول بنجاح!",
                                "session_active": True
                            }
                    except:
                        pass

                # فحص وجود عنصر الإعدادات (علامة أخرى)
                try:
                    settings_btn = self.driver.find_element(By.CSS_SELECTOR, "[data-testid='menu']")
                    if settings_btn:
                        self.is_logged_in = True
                        self.save_session()
                        return {
                            "success": True,
                            "message": "تم تسجيل الدخول بنجاح!",
                            "session_active": True
                        }
                except:
                    pass

                # فحص عدم وجود QR Code (يعني تم تسجيل الدخول)
                try:
                    qr_code = self.driver.find_element(By.CSS_SELECTOR, "[data-ref]")
                    # إذا وُجد QR Code، استمر في الانتظار
                except:
                    # إذا لم يوجد QR Code، قد يكون تم تسجيل الدخول
                    try:
                        # فحص وجود أي عنصر يدل على تسجيل الدخول
                        logged_in_elements = self.driver.find_elements(By.CSS_SELECTOR,
                            "[data-testid='chat-list'], [data-testid='sidebar'], [data-testid='menu'], .app-wrapper-web")
                        if logged_in_elements:
                            self.is_logged_in = True
                            self.save_session()
                            return {
                                "success": True,
                                "message": "تم تسجيل الدخول بنجاح!",
                                "session_active": True
                            }
                    except:
                        pass

                # انتظار قبل الفحص التالي
                time.sleep(check_interval)

            # انتهت المهلة
            return {
                "success": False,
                "message": "انتهت مهلة انتظار تسجيل الدخول. يرجى المحاولة مرة أخرى.",
                "session_active": False
            }

        except Exception as e:
            logger.error(f"خطأ في انتظار تسجيل الدخول: {e}")
            return {
                "success": False,
                "message": f"خطأ: {str(e)}",
                "session_active": False
            }
    
    def save_session(self):
        """حفظ بيانات الجلسة"""
        try:
            session_data = {
                "cookies": self.driver.get_cookies(),
                "local_storage": self.driver.execute_script("return window.localStorage;"),
                "session_storage": self.driver.execute_script("return window.sessionStorage;"),
                "timestamp": time.time()
            }
            
            with open(self.session_file, 'wb') as f:
                pickle.dump(session_data, f)
                
            logger.info("تم حفظ بيانات الجلسة")
            
        except Exception as e:
            logger.error(f"خطأ في حفظ الجلسة: {e}")
    
    def load_session(self) -> bool:
        """تحميل بيانات الجلسة المحفوظة"""
        try:
            if not os.path.exists(self.session_file):
                return False
                
            with open(self.session_file, 'rb') as f:
                session_data = pickle.load(f)
            
            # التحقق من عمر الجلسة (30 يوم)
            if time.time() - session_data.get('timestamp', 0) > 30 * 24 * 60 * 60:
                logger.info("انتهت صلاحية الجلسة المحفوظة")
                return False
            
            # استعادة الكوكيز
            for cookie in session_data.get('cookies', []):
                try:
                    self.driver.add_cookie(cookie)
                except Exception as e:
                    logger.warning(f"فشل في إضافة كوكي: {e}")
            
            # استعادة Local Storage
            for key, value in session_data.get('local_storage', {}).items():
                try:
                    self.driver.execute_script(f"window.localStorage.setItem('{key}', '{value}');")
                except Exception as e:
                    logger.warning(f"فشل في إضافة local storage: {e}")
            
            # استعادة Session Storage
            for key, value in session_data.get('session_storage', {}).items():
                try:
                    self.driver.execute_script(f"window.sessionStorage.setItem('{key}', '{value}');")
                except Exception as e:
                    logger.warning(f"فشل في إضافة session storage: {e}")
            
            return True
            
        except Exception as e:
            logger.error(f"خطأ في تحميل الجلسة: {e}")
            return False
    
    def logout(self) -> Dict[str, Any]:
        """تسجيل الخروج وحذف الجلسة"""
        try:
            if self.driver:
                # حذف بيانات الجلسة من المتصفح
                self.driver.delete_all_cookies()
                self.driver.execute_script("window.localStorage.clear();")
                self.driver.execute_script("window.sessionStorage.clear();")
            
            # حذف ملف الجلسة المحفوظ
            if os.path.exists(self.session_file):
                os.remove(self.session_file)
            
            self.is_logged_in = False
            
            return {
                "success": True,
                "message": "تم تسجيل الخروج بنجاح"
            }
            
        except Exception as e:
            logger.error(f"خطأ في تسجيل الخروج: {e}")
            return {
                "success": False,
                "message": f"خطأ في تسجيل الخروج: {str(e)}"
            }
    
    def get_status(self) -> Dict[str, Any]:
        """الحصول على حالة الجلسة الحالية"""
        try:
            if not self.driver:
                return {
                    "session_active": False,
                    "logged_in": False,
                    "message": "لم يتم بدء الجلسة"
                }

            # التحقق من حالة تسجيل الدخول بطرق متعددة
            logged_in = False
            status_message = "غير مسجل دخول"

            # فحص مؤشرات تسجيل الدخول
            login_indicators = [
                "[data-testid='chat-list']",
                "[data-testid='sidebar']",
                "[data-testid='menu']",
                ".app-wrapper-web"
            ]

            for indicator in login_indicators:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, indicator)
                    if element and element.is_displayed():
                        logged_in = True
                        status_message = "مسجل دخول ونشط"
                        break
                except:
                    continue

            # فحص وجود QR Code
            if not logged_in:
                try:
                    qr_element = self.driver.find_element(By.CSS_SELECTOR, "[data-ref]")
                    if qr_element and qr_element.is_displayed():
                        status_message = "في انتظار مسح QR Code"
                except:
                    pass

            # تحديث حالة الكائن
            self.is_logged_in = logged_in

            return {
                "session_active": True,
                "logged_in": logged_in,
                "headless": self.headless,
                "message": status_message
            }

        except Exception as e:
            logger.error(f"خطأ في الحصول على الحالة: {e}")
            return {
                "session_active": False,
                "logged_in": False,
                "message": f"خطأ: {str(e)}"
            }
    
    def clear_session(self) -> Dict[str, Any]:
        """حذف الجلسة المحفوظة بالكامل"""
        try:
            # إغلاق المتصفح إذا كان مفتوحاً
            if self.driver:
                try:
                    self.driver.quit()
                except:
                    pass
                self.driver = None

            # حذف مجلد الجلسة
            session_dir = os.path.join(os.getcwd(), "whatsapp_session")
            if os.path.exists(session_dir):
                import shutil
                import time
                try:
                    # محاولة حذف المجلد عدة مرات
                    for attempt in range(3):
                        try:
                            shutil.rmtree(session_dir)
                            break
                        except PermissionError:
                            time.sleep(1)
                            continue

                    # إنشاء مجلد فارغ جديد
                    os.makedirs(session_dir, exist_ok=True)
                    logger.info("تم حذف وإعادة إنشاء مجلد الجلسة")

                except Exception as e:
                    logger.warning(f"لم يتم حذف مجلد الجلسة بالكامل: {e}")

            self.is_logged_in = False

            return {
                "success": True,
                "message": "تم حذف الجلسة بنجاح. يمكنك الآن تسجيل دخول جديد."
            }

        except Exception as e:
            logger.error(f"خطأ في حذف الجلسة: {e}")
            return {
                "success": False,
                "message": f"خطأ: {str(e)}"
            }

    def close(self):
        """إغلاق المتصفح"""
        try:
            if self.driver:
                self.driver.quit()
                self.driver = None
        except Exception as e:
            logger.error(f"خطأ في إغلاق المتصفح: {e}")

    def __del__(self):
        """تنظيف الموارد عند حذف الكائن"""
        self.close()


# مثيل عام لإدارة WhatsApp Web
whatsapp_manager = WhatsAppWebManager()
