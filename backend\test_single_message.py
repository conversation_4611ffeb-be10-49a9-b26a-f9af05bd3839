#!/usr/bin/env python3
"""
اختبار إرسال رسالة واحدة عبر WhatsApp Web
"""

import sys
import os
import asyncio

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.getcwd())

from whatsapp_selenium import whatsapp_manager
from services.whatsapp_web_service import WhatsAppWebService

async def test_single_message():
    """اختبار إرسال رسالة واحدة"""
    
    print("🧪 اختبار إرسال رسالة واحدة عبر WhatsApp Web")
    print("=" * 60)
    
    try:
        # فحص حالة WhatsApp Web
        print("🔍 فحص حالة WhatsApp Web...")
        status = whatsapp_manager.get_status()
        
        print(f"📊 الجلسة نشطة: {status.get('session_active', False)}")
        print(f"🔐 مسجل دخول: {status.get('logged_in', False)}")
        
        if not status.get('session_active') or not status.get('logged_in'):
            print("❌ WhatsApp Web غير جاهز!")
            print("💡 يرجى تسجيل الدخول من الإعدادات أولاً")
            print("🌐 http://localhost:5173/settings")
            return
        
        print("✅ WhatsApp Web جاهز!")
        
        # إنشاء خدمة WhatsApp Web
        print("\n🔧 إنشاء خدمة WhatsApp Web...")
        whatsapp_service = WhatsAppWebService(whatsapp_manager)
        
        # رقم اختبار (يمكنك تغييره)
        test_phone = "+966501234567"  # غير هذا الرقم إلى رقم حقيقي للاختبار
        test_message = "مرحباً! هذه رسالة اختبار من نظام الحملات 🚀"
        
        print(f"\n📱 إرسال رسالة اختبار إلى: {test_phone}")
        print(f"💬 الرسالة: {test_message}")
        
        # إرسال الرسالة
        result = await whatsapp_service.send_text_message(test_phone, test_message)
        
        print(f"\n📊 نتيجة الإرسال:")
        print(f"   ✅ نجح: {result.get('success', False)}")
        
        if result.get('success'):
            print(f"   🆔 معرف الرسالة: {result.get('message_id')}")
            print(f"   📱 إلى: {result.get('to')}")
            print(f"   ⏰ الوقت: {result.get('timestamp')}")
            print("\n🎉 تم إرسال الرسالة بنجاح!")
        else:
            print(f"   ❌ خطأ: {result.get('error')}")
            print("\n💡 تحقق من:")
            print("   - أن الرقم صحيح ومُسجل في WhatsApp")
            print("   - أن WhatsApp Web مُسجل دخول")
            print("   - أن المتصفح لم يُغلق")
        
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
    
    print("\n" + "=" * 60)
    print("انتهى اختبار الرسالة الواحدة")

if __name__ == "__main__":
    asyncio.run(test_single_message())
