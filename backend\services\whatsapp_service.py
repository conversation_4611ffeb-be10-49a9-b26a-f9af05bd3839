import aiohttp
import asyncio
import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
import re

logger = logging.getLogger(__name__)

class WhatsAppService:
    """خدمة إرسال الرسائل عبر WhatsApp Business API"""
    
    def __init__(self, api_url: str, access_token: str, phone_number_id: str):
        self.api_url = api_url.rstrip('/')
        self.access_token = access_token
        self.phone_number_id = phone_number_id
        self.base_url = f"https://graph.facebook.com/v18.0/{phone_number_id}"
        
        self.headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json"
        }
    
    def _format_phone_number(self, phone: str) -> str:
        """تنسيق رقم الهاتف للتوافق مع WhatsApp API"""
        # إزالة جميع الرموز غير الرقمية
        phone = re.sub(r'[^\d]', '', phone)
        
        # إضافة رمز الدولة إذا لم يكن موجوداً
        if not phone.startswith('966') and len(phone) == 9:
            phone = '966' + phone
        elif not phone.startswith('966') and len(phone) == 10 and phone.startswith('0'):
            phone = '966' + phone[1:]
        
        return phone
    
    async def send_text_message(
        self, 
        to_phone: str, 
        message: str,
        preview_url: bool = False
    ) -> Dict[str, Any]:
        """إرسال رسالة نصية"""
        
        formatted_phone = self._format_phone_number(to_phone)
        
        payload = {
            "messaging_product": "whatsapp",
            "to": formatted_phone,
            "type": "text",
            "text": {
                "body": message,
                "preview_url": preview_url
            }
        }
        
        return await self._send_request("messages", payload)
    
    async def send_image_message(
        self, 
        to_phone: str, 
        image_url: str, 
        caption: Optional[str] = None
    ) -> Dict[str, Any]:
        """إرسال رسالة مع صورة"""
        
        formatted_phone = self._format_phone_number(to_phone)
        
        image_data = {"link": image_url}
        if caption:
            image_data["caption"] = caption
        
        payload = {
            "messaging_product": "whatsapp",
            "to": formatted_phone,
            "type": "image",
            "image": image_data
        }
        
        return await self._send_request("messages", payload)
    
    async def send_template_message(
        self, 
        to_phone: str, 
        template_name: str,
        language_code: str = "ar",
        parameters: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """إرسال رسالة باستخدام قالب معتمد"""
        
        formatted_phone = self._format_phone_number(to_phone)
        
        template_data = {
            "name": template_name,
            "language": {"code": language_code}
        }
        
        if parameters:
            template_data["components"] = [
                {
                    "type": "body",
                    "parameters": [
                        {"type": "text", "text": param} for param in parameters
                    ]
                }
            ]
        
        payload = {
            "messaging_product": "whatsapp",
            "to": formatted_phone,
            "type": "template",
            "template": template_data
        }
        
        return await self._send_request("messages", payload)
    
    async def send_bulk_messages(
        self, 
        messages: List[Dict[str, Any]],
        delay_seconds: float = 1.0
    ) -> List[Dict[str, Any]]:
        """إرسال رسائل متعددة مع تأخير بينها"""
        
        results = []
        
        for i, message_data in enumerate(messages):
            try:
                if message_data.get("type") == "text":
                    result = await self.send_text_message(
                        message_data["to_phone"],
                        message_data["message"],
                        message_data.get("preview_url", False)
                    )
                elif message_data.get("type") == "image":
                    result = await self.send_image_message(
                        message_data["to_phone"],
                        message_data["image_url"],
                        message_data.get("caption")
                    )
                elif message_data.get("type") == "template":
                    result = await self.send_template_message(
                        message_data["to_phone"],
                        message_data["template_name"],
                        message_data.get("language_code", "ar"),
                        message_data.get("parameters")
                    )
                else:
                    result = {"error": "نوع الرسالة غير مدعوم"}
                
                results.append({
                    "index": i,
                    "to_phone": message_data["to_phone"],
                    "result": result,
                    "success": "error" not in result
                })
                
                # تأخير بين الرسائل لتجنب Rate Limiting
                if i < len(messages) - 1:
                    await asyncio.sleep(delay_seconds)
                    
            except Exception as e:
                logger.error(f"خطأ في إرسال الرسالة {i}: {str(e)}")
                results.append({
                    "index": i,
                    "to_phone": message_data.get("to_phone", "unknown"),
                    "result": {"error": str(e)},
                    "success": False
                })
        
        return results
    
    async def get_message_status(self, message_id: str) -> Dict[str, Any]:
        """الحصول على حالة رسالة محددة"""
        
        endpoint = f"messages/{message_id}"
        return await self._send_request(endpoint, method="GET")
    
    async def mark_message_as_read(self, message_id: str) -> Dict[str, Any]:
        """تمييز الرسالة كمقروءة"""
        
        payload = {
            "messaging_product": "whatsapp",
            "status": "read",
            "message_id": message_id
        }
        
        return await self._send_request("messages", payload)
    
    async def get_business_profile(self) -> Dict[str, Any]:
        """الحصول على معلومات الملف التجاري"""
        
        endpoint = f"whatsapp_business_profile"
        return await self._send_request(endpoint, method="GET")
    
    async def update_business_profile(self, profile_data: Dict[str, Any]) -> Dict[str, Any]:
        """تحديث الملف التجاري"""
        
        endpoint = f"whatsapp_business_profile"
        return await self._send_request(endpoint, profile_data, method="POST")
    
    async def _send_request(
        self, 
        endpoint: str, 
        payload: Optional[Dict[str, Any]] = None,
        method: str = "POST"
    ) -> Dict[str, Any]:
        """إرسال طلب HTTP إلى WhatsApp API"""
        
        url = f"{self.base_url}/{endpoint}"
        
        try:
            async with aiohttp.ClientSession() as session:
                if method == "GET":
                    async with session.get(url, headers=self.headers) as response:
                        response_data = await response.json()
                else:
                    async with session.post(
                        url, 
                        headers=self.headers, 
                        json=payload
                    ) as response:
                        response_data = await response.json()
                
                if response.status == 200:
                    logger.info(f"نجح إرسال الطلب إلى {endpoint}")
                    return response_data
                else:
                    logger.error(f"فشل الطلب إلى {endpoint}: {response.status}")
                    return {
                        "error": f"HTTP {response.status}",
                        "details": response_data
                    }
                    
        except aiohttp.ClientError as e:
            logger.error(f"خطأ في الاتصال بـ WhatsApp API: {str(e)}")
            return {"error": f"خطأ في الاتصال: {str(e)}"}
        except Exception as e:
            logger.error(f"خطأ غير متوقع: {str(e)}")
            return {"error": f"خطأ غير متوقع: {str(e)}"}
    
    def validate_phone_number(self, phone: str) -> bool:
        """التحقق من صحة رقم الهاتف"""
        formatted_phone = self._format_phone_number(phone)
        
        # التحقق من أن الرقم يبدأ برمز دولة صحيح وله طول مناسب
        if len(formatted_phone) >= 10 and formatted_phone.isdigit():
            return True
        return False
    
    async def test_connection(self) -> Dict[str, Any]:
        """اختبار الاتصال بـ WhatsApp API"""
        try:
            result = await self.get_business_profile()
            if "error" not in result:
                return {
                    "status": "success",
                    "message": "تم الاتصال بنجاح مع WhatsApp API",
                    "profile": result
                }
            else:
                return {
                    "status": "error",
                    "message": "فشل في الاتصال مع WhatsApp API",
                    "error": result["error"]
                }
        except Exception as e:
            return {
                "status": "error",
                "message": "خطأ في اختبار الاتصال",
                "error": str(e)
            }
