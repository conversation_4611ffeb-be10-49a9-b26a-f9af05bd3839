# استخدام Python 3.11 كصورة أساسية
FROM python:3.11-slim

# تعيين متغيرات البيئة
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

# تعيين مجلد العمل
WORKDIR /app

# تثبيت متطلبات النظام
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# نسخ ملف المتطلبات
COPY requirements.txt .

# تثبيت المكتبات Python
RUN pip install --no-cache-dir -r requirements.txt

# نسخ كود التطبيق
COPY . .

# إنشاء مجلد الرفع
RUN mkdir -p uploads

# إنشاء مجلد اللوجز
RUN mkdir -p logs

# تعيين الصلاحيات
RUN chmod +x run.py

# فتح المنفذ
EXPOSE 8000

# تشغيل التطبيق
CMD ["python", "run.py"]
