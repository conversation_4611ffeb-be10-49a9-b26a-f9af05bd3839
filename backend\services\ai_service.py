import openai
import asyncio
import logging
import time
import json
import re
from typing import Dict, List, Optional, Any
from datetime import datetime

logger = logging.getLogger(__name__)

class AIService:
    """خدمة الذكاء الاصطناعي للردود التلقائية"""
    
    def __init__(self, api_key: str, model: str = "gpt-4o-mini", system_prompt: Optional[str] = None):
        self.client = openai.AsyncOpenAI(api_key=api_key)
        self.model = model
        self.system_prompt = system_prompt or self._get_default_prompt()
        self.conversation_history = {}  # تخزين تاريخ المحادثات
        
    def _get_default_prompt(self) -> str:
        """الحصول على النص التوجيهي الافتراضي"""
        return """أنت مساعد ذكاء اصطناعي متخصص في تحليل محادثات العملاء وفهم السياق. مهمتك:

1. تحليل محتوى الرسائل المتبادلة بين العميل ومساعد المبيعات
2. فهم السياق من الرسائل السابقة لتقديم إجابات دقيقة
3. تحديد هوية العميل من خلال تحليل المحادثة
4. استخدام اللغة العربية بشكل طبيعي ومفهوم

إرشادات مهمة:
- ركز على فهم محتوى المحادثة والسياق
- استخدم الرسائل السابقة لفهم طبيعة الاستفسار الحالي
- عند سؤال العميل عن هويته، راجع المحادثة السابقة لاكتشاف الاسم
- عند سؤال العميل عن معلومات تم مناقشتها سابقاً، استعرض المحادثة لاكتشاف التفاصيل
- كن دقيقاً في تحليل المحتوى وتجنب التخمين

مثال على تنسيق المحادثة التي سيتم تحليلها:
العميل: مرحبا انا احمد
ai bot: مرحبا أحمد، كيف يمكنني مساعدتك اليوم؟
العميل: من انا هل تعرف اسمي
ai bot: اسمك أحمد كما ذكرته في رسالتك الأولى

تعليمات إضافية مهمة:
- سيتم إرسال سياق المحادثة كرسائل منظمة مسبقاً في تاريخ المحادثة
- ابحث دائماً في تاريخ المحادثة للحصول على المعلومات المطلوبة
- عند وجود سؤال يتطلب مراجعة المحادثة السابقة، استخدم الرسائل السابقة للاستجابة بدقة
- لا تفترض معلومات غير موجودة في المحادثة"""

    async def generate_response(
        self, 
        user_message: str, 
        contact_phone: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> str:
        """توليد رد تلقائي للرسالة"""
        
        try:
            # إعداد الرسائل للمحادثة
            messages = [
                {"role": "system", "content": self.system_prompt}
            ]
            
            # إضافة السياق إذا كان متوفراً
            if context:
                # تنسيق السياق بشكل أفضل
                context_parts = []
                
                # إضافة معلومات الشركة
                company_info = []
                if context.get("company_name"):
                    company_info.append(f"اسم الشركة: {context['company_name']}")
                if context.get("support_phone"):
                    company_info.append(f"رقم الدعم: {context['support_phone']}")
                if context.get("support_email"):
                    company_info.append(f"البريد الإلكتروني للدعم: {context['support_email']}")
                
                if company_info:
                    context_parts.append("معلومات الشركة:\n" + "\n".join(company_info))
                
                # إضافة اسم جهة الاتصال (لكن مع تحذير)
                if context.get("contact_name") and context.get("contact_name") != "مجهول":
                    context_parts.append(f"ملاحظة: اسم جهة الاتصال في الواتساب هو '{context['contact_name']}', لكن العميل قد يكون ذكر اسمًا مختلفًا في المحادثة")
                
                # إضافة سياق المحادثة كرسائل منظمة
                if "conversation_context" in context and context["conversation_context"]:
                    # تقسيم سياق المحادثة إلى رسائل منفردة
                    conversation_lines = context["conversation_context"].strip().split('\n')
                    for line in conversation_lines:
                        line = line.strip()
                        if line:
                            if line.startswith("ai bot:"):
                                # رسالة من البوت
                                content = line[7:].strip()  # إزالة "ai bot:" من البداية
                                messages.append({"role": "assistant", "content": content})
                            elif line.startswith("العميل:"):
                                # رسالة من العميل
                                content = line[7:].strip()  # إزالة "العميل:" من البداية
                                messages.append({"role": "user", "content": content})
                
                if context_parts:
                    context_message = "معلومات إضافية:\n" + "\n\n".join(context_parts)
                    messages.append({"role": "system", "content": context_message})
            
            # إضافة رسالة المستخدم
            messages.append({"role": "user", "content": user_message})
            
            # إرسال الطلب إلى OpenAI
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                max_tokens=300,
                temperature=0.7,
                top_p=0.9,
                frequency_penalty=0.1,
                presence_penalty=0.1
            )
            
            ai_response = response.choices[0].message.content.strip()
            
            # لا نحفظ المحادثة لأن السياق يُرسل بشكل صريح
            
            return ai_response
            
        except openai.RateLimitError:
            logger.warning("تم تجاوز حد الاستخدام لـ OpenAI API")
            return "عذراً، نواجه ضغطاً كبيراً حالياً. يرجى المحاولة مرة أخرى خلال دقائق قليلة."
            
        except openai.APIError as e:
            logger.error(f"خطأ في OpenAI API: {str(e)}")
            return "عذراً، نواجه مشكلة تقنية مؤقتة. يرجى المحاولة مرة أخرى لاحقاً."
            
        except Exception as e:
            logger.error(f"خطأ غير متوقع في AI Service: {str(e)}")
            return "عذراً، حدث خطأ غير متوقع. يرجى التواصل مع فريق الدعم."

    async def generate_bulk_responses(
        self, 
        messages: List[Dict[str, str]],
        delay_seconds: float = 1.0
    ) -> List[Dict[str, Any]]:
        """توليد ردود متعددة مع تأخير بينها"""
        
        results = []
        
        for i, message_data in enumerate(messages):
            try:
                start_time = time.time()
                
                response = await self.generate_response(
                    message_data["message"],
                    message_data.get("contact_phone"),
                    message_data.get("context")
                )
                
                end_time = time.time()
                response_time_ms = int((end_time - start_time) * 1000)
                
                results.append({
                    "index": i,
                    "contact_phone": message_data.get("contact_phone"),
                    "original_message": message_data["message"],
                    "ai_response": response,
                    "response_time_ms": response_time_ms,
                    "success": True
                })
                
                # تأخير بين الطلبات
                if i < len(messages) - 1:
                    await asyncio.sleep(delay_seconds)
                    
            except Exception as e:
                logger.error(f"خطأ في توليد الرد {i}: {str(e)}")
                results.append({
                    "index": i,
                    "contact_phone": message_data.get("contact_phone"),
                    "original_message": message_data["message"],
                    "ai_response": "عذراً، حدث خطأ في توليد الرد.",
                    "error": str(e),
                    "success": False
                })
        
        return results

    async def analyze_message_sentiment(self, message: str) -> Dict[str, Any]:
        """تحليل مشاعر الرسالة"""
        
        try:
            sentiment_prompt = """حلل مشاعر الرسالة التالية وصنفها إلى إيجابية، سلبية، أو محايدة.
            أعط أيضاً درجة من 1 إلى 10 لقوة المشاعر.
            
            الرسالة: {message}
            
            أجب بتنسيق JSON:
            {{"sentiment": "positive/negative/neutral", "score": 1-10, "explanation": "تفسير قصير"}}"""
            
            response = await self.client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": "أنت محلل مشاعر خبير."},
                    {"role": "user", "content": sentiment_prompt.format(message=message)}
                ],
                max_tokens=150,
                temperature=0.3
            )
            
            result = json.loads(response.choices[0].message.content)
            return result
            
        except Exception as e:
            logger.error(f"خطأ في تحليل المشاعر: {str(e)}")
            return {
                "sentiment": "neutral",
                "score": 5,
                "explanation": "لم يتمكن من تحليل المشاعر"
            }

    async def categorize_message(self, message: str) -> Dict[str, Any]:
        """تصنيف الرسالة حسب النوع"""
        
        categories = [
            "استفسار عن المنتج",
            "شكوى",
            "طلب دعم تقني",
            "طلب معلومات",
            "طلب إلغاء",
            "تحية أو شكر",
            "أخرى"
        ]
        
        try:
            categorization_prompt = f"""صنف الرسالة التالية إلى إحدى الفئات:
            {', '.join(categories)}
            
            الرسالة: {message}
            
            أجب بتنسيق JSON:
            {{"category": "الفئة", "confidence": 0.0-1.0, "keywords": ["كلمة1", "كلمة2"]}}"""
            
            response = await self.client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": "أنت مصنف رسائل خبير."},
                    {"role": "user", "content": categorization_prompt}
                ],
                max_tokens=100,
                temperature=0.2
            )
            
            result = json.loads(response.choices[0].message.content)
            return result
            
        except Exception as e:
            logger.error(f"خطأ في تصنيف الرسالة: {str(e)}")
            return {
                "category": "أخرى",
                "confidence": 0.5,
                "keywords": []
            }

    async def suggest_response_improvements(self, original_response: str) -> List[str]:
        """اقتراح تحسينات على الرد"""
        
        try:
            improvement_prompt = f"""اقترح 3 تحسينات على الرد التالي لجعله أكثر فعالية:
            
            الرد الأصلي: {original_response}
            
            أجب بقائمة من 3 اقتراحات قصيرة ومفيدة."""
            
            response = await self.client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": "أنت خبير في تحسين خدمة العملاء."},
                    {"role": "user", "content": improvement_prompt}
                ],
                max_tokens=200,
                temperature=0.7
            )
            
            suggestions = response.choices[0].message.content.strip().split('\n')
            return [s.strip('- ').strip() for s in suggestions if s.strip()]
            
        except Exception as e:
            logger.error(f"خطأ في اقتراح التحسينات: {str(e)}")
            return ["لا توجد اقتراحات متاحة حالياً"]

    def clear_conversation_history(self, contact_phone: Optional[str] = None):
        """مسح تاريخ المحادثات"""
        if contact_phone:
            if contact_phone in self.conversation_history:
                del self.conversation_history[contact_phone]
        else:
            self.conversation_history.clear()

    def get_conversation_summary(self, contact_phone: str) -> Dict[str, Any]:
        """الحصول على ملخص المحادثة"""
        if contact_phone not in self.conversation_history:
            return {"messages_count": 0, "last_interaction": None}
        
        history = self.conversation_history[contact_phone]
        return {
            "messages_count": len(history),
            "last_interaction": datetime.now().isoformat(),
            "conversation_length": len(history) // 2  # عدد التبادلات
        }

    async def test_connection(self) -> Dict[str, Any]:
        """اختبار الاتصال مع OpenAI API"""
        try:
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "user", "content": "مرحبا، هذا اختبار للاتصال"}
                ],
                max_tokens=50
            )
            
            return {
                "status": "success",
                "message": "تم الاتصال بنجاح مع OpenAI API",
                "model": self.model,
                "test_response": response.choices[0].message.content
            }
            
        except Exception as e:
            return {
                "status": "error",
                "message": "فشل في الاتصال مع OpenAI API",
                "error": str(e)
            }