#!/usr/bin/env python3
"""
اختبار الطريقة الجديدة للوصول المباشر لـ WhatsApp Web
"""

import sys
import os
import asyncio

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.getcwd())

from whatsapp_selenium import whatsapp_manager
from services.whatsapp_web_service import WhatsAppWebService

async def test_direct_whatsapp():
    """اختبار الوصول المباشر لـ WhatsApp Web"""
    
    print("🧪 اختبار الطريقة الجديدة للوصول المباشر لـ WhatsApp Web")
    print("=" * 70)
    
    try:
        # فحص حالة WhatsApp Web
        print("🔍 فحص حالة WhatsApp Web...")
        status = whatsapp_manager.get_status()
        
        print(f"📊 الجلسة نشطة: {status.get('session_active', False)}")
        print(f"🔐 مسجل دخول: {status.get('logged_in', False)}")
        
        if not status.get('session_active') or not status.get('logged_in'):
            print("❌ WhatsApp Web غير جاهز!")
            print("💡 يرجى تسجيل الدخول من الإعدادات أولاً")
            print("🌐 http://localhost:5173/settings")
            return
        
        print("✅ WhatsApp Web جاهز!")
        
        # إنشاء خدمة WhatsApp Web
        print("\n🔧 إنشاء خدمة WhatsApp Web...")
        whatsapp_service = WhatsAppWebService(whatsapp_manager)
        
        # أرقام اختبار (غير هذه الأرقام إلى أرقام حقيقية للاختبار)
        test_numbers = [
            "+966501234567",  # رقم سعودي مع +
            "966509876543",   # رقم سعودي بدون +
            "0507654321",     # رقم سعودي يبدأ بصفر
            "501234567"       # رقم سعودي بدون رمز الدولة
        ]
        
        test_message = "مرحباً! هذه رسالة اختبار من النظام الجديد 🚀"
        
        print(f"\n📱 اختبار إرسال رسائل لـ {len(test_numbers)} أرقام...")
        print(f"💬 الرسالة: {test_message}")
        
        success_count = 0
        failed_count = 0
        
        for i, phone in enumerate(test_numbers, 1):
            print(f"\n📤 اختبار {i}/{len(test_numbers)}: {phone}")
            print("-" * 40)
            
            try:
                # إرسال الرسالة
                result = await whatsapp_service.send_text_message(phone, test_message)
                
                if result.get('success'):
                    print(f"   ✅ نجح الإرسال!")
                    print(f"   🆔 معرف الرسالة: {result.get('message_id')}")
                    print(f"   📱 الرقم المُنسق: {result.get('to')}")
                    success_count += 1
                else:
                    print(f"   ❌ فشل الإرسال: {result.get('error')}")
                    failed_count += 1
                
                # تأخير بين الرسائل
                if i < len(test_numbers):
                    print("   ⏳ انتظار 3 ثوانٍ...")
                    await asyncio.sleep(3)
                    
            except Exception as e:
                print(f"   ❌ خطأ في الإرسال: {e}")
                failed_count += 1
        
        print(f"\n📊 ملخص النتائج:")
        print(f"   ✅ نجح: {success_count}")
        print(f"   ❌ فشل: {failed_count}")
        print(f"   📈 معدل النجاح: {(success_count / len(test_numbers) * 100):.1f}%")
        
        if success_count > 0:
            print("\n🎉 الطريقة الجديدة تعمل! يمكن الآن إرسال الحملات بنجاح!")
        else:
            print("\n⚠️ لم تنجح أي رسالة. تحقق من:")
            print("   - أن الأرقام صحيحة ومُسجلة في WhatsApp")
            print("   - أن WhatsApp Web مُسجل دخول")
            print("   - أن المتصفح لم يُغلق")
        
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
    
    print("\n" + "=" * 70)
    print("انتهى اختبار الطريقة الجديدة")

if __name__ == "__main__":
    asyncio.run(test_direct_whatsapp())
