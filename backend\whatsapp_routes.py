"""
WhatsApp Web API Routes
مسارات API لإدارة تسجيل الدخول والجلسات
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel
from typing import Optional, Dict, Any
import asyncio
import logging
from whatsapp_selenium import whatsapp_manager
from services.auto_reply_service import get_auto_reply_service

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/whatsapp", tags=["WhatsApp Web"])

class WhatsAppLoginRequest(BaseModel):
    headless: bool = False
    timeout: int = 60

class WhatsAppStatusResponse(BaseModel):
    session_active: bool
    logged_in: bool
    headless: Optional[bool] = None
    message: str

class WhatsAppLoginResponse(BaseModel):
    success: bool
    message: str
    requires_qr: bool = False
    session_active: bool = False

@router.post("/login", response_model=WhatsAppLoginResponse)
async def login_whatsapp(request: WhatsAppLoginRequest):
    """
    بدء تسجيل الدخول لـ WhatsApp Web
    """
    try:
        # إعداد المدير مع الخيارات المطلوبة
        whatsapp_manager.headless = request.headless
        
        # بدء الجلسة
        result = whatsapp_manager.start_session()
        
        return WhatsAppLoginResponse(**result)
        
    except Exception as e:
        logger.error(f"خطأ في تسجيل الدخول: {e}")
        raise HTTPException(status_code=500, detail=f"خطأ في تسجيل الدخول: {str(e)}")

@router.post("/wait-login")
async def wait_for_login(timeout: int = 60):
    """
    انتظار تسجيل الدخول عبر QR Code
    """
    try:
        if not whatsapp_manager.driver:
            raise HTTPException(status_code=400, detail="لم يتم بدء جلسة WhatsApp")
        
        # تشغيل انتظار تسجيل الدخول في الخلفية
        result = whatsapp_manager.wait_for_login(timeout)
        
        return result
        
    except Exception as e:
        logger.error(f"خطأ في انتظار تسجيل الدخول: {e}")
        raise HTTPException(status_code=500, detail=f"خطأ: {str(e)}")

@router.get("/status", response_model=WhatsAppStatusResponse)
async def get_whatsapp_status():
    """
    الحصول على حالة جلسة WhatsApp Web الحالية
    """
    try:
        status = whatsapp_manager.get_status()
        return WhatsAppStatusResponse(**status)
        
    except Exception as e:
        logger.error(f"خطأ في الحصول على الحالة: {e}")
        raise HTTPException(status_code=500, detail=f"خطأ: {str(e)}")

@router.post("/logout")
async def logout_whatsapp():
    """
    تسجيل الخروج من WhatsApp Web وحذف الجلسة
    """
    try:
        result = whatsapp_manager.logout()
        return result

    except Exception as e:
        logger.error(f"خطأ في تسجيل الخروج: {e}")
        raise HTTPException(status_code=500, detail=f"خطأ: {str(e)}")



@router.post("/close")
async def close_whatsapp():
    """
    إغلاق متصفح WhatsApp Web
    """
    try:
        whatsapp_manager.close()
        return {"success": True, "message": "تم إغلاق المتصفح"}
        
    except Exception as e:
        logger.error(f"خطأ في إغلاق المتصفح: {e}")
        raise HTTPException(status_code=500, detail=f"خطأ: {str(e)}")

@router.post("/toggle-headless")
async def toggle_headless_mode(headless: bool):
    """
    تغيير وضع المتصفح (ظاهر/خلفية)
    """
    try:
        # إذا كان هناك جلسة نشطة، نحتاج لإعادة تشغيلها
        if whatsapp_manager.driver:
            # حفظ الحالة الحالية
            current_status = whatsapp_manager.get_status()
            
            if current_status["logged_in"]:
                # حفظ الجلسة قبل الإغلاق
                whatsapp_manager.save_session()
            
            # إغلاق الجلسة الحالية
            whatsapp_manager.close()
            
            # تحديث الوضع
            whatsapp_manager.headless = headless
            
            # إعادة بدء الجلسة إذا كان المستخدم مسجل دخول
            if current_status["logged_in"]:
                result = whatsapp_manager.start_session()
                return {
                    "success": True,
                    "message": f"تم تغيير الوضع إلى {'الخلفية' if headless else 'ظاهر'} وإعادة تشغيل الجلسة",
                    "headless": headless,
                    "session_restarted": True
                }
        
        # تحديث الوضع فقط
        whatsapp_manager.headless = headless
        
        return {
            "success": True,
            "message": f"تم تحديث الوضع إلى {'الخلفية' if headless else 'ظاهر'}",
            "headless": headless,
            "session_restarted": False
        }
        
    except Exception as e:
        logger.error(f"خطأ في تغيير الوضع: {e}")
        raise HTTPException(status_code=500, detail=f"خطأ: {str(e)}")

@router.get("/session-info")
async def get_session_info():
    """
    الحصول على معلومات مفصلة عن الجلسة
    """
    try:
        import os
        
        session_dir = whatsapp_manager.user_data_dir
        session_file = whatsapp_manager.session_file
        
        session_exists = os.path.exists(session_file)
        session_size = os.path.getsize(session_file) if session_exists else 0
        
        status = whatsapp_manager.get_status()
        
        return {
            "session_directory": session_dir,
            "session_file_exists": session_exists,
            "session_file_size": session_size,
            "current_status": status,
            "headless_mode": whatsapp_manager.headless
        }
        
    except Exception as e:
        logger.error(f"خطأ في الحصول على معلومات الجلسة: {e}")
        raise HTTPException(status_code=500, detail=f"خطأ: {str(e)}")

@router.delete("/clear-session")
async def clear_session_data():
    """
    حذف جميع بيانات الجلسة المحفوظة
    """
    try:
        import shutil
        
        # إغلاق الجلسة الحالية
        whatsapp_manager.close()
        
        # حذف مجلد الجلسة بالكامل
        if os.path.exists(whatsapp_manager.user_data_dir):
            shutil.rmtree(whatsapp_manager.user_data_dir)
        
        # إعادة إنشاء المجلد
        os.makedirs(whatsapp_manager.user_data_dir, exist_ok=True)
        
        return {
            "success": True,
            "message": "تم حذف جميع بيانات الجلسة المحفوظة"
        }
        
    except Exception as e:
        logger.error(f"خطأ في حذف بيانات الجلسة: {e}")
        raise HTTPException(status_code=500, detail=f"خطأ: {str(e)}")

# إضافة معالج لإغلاق الجلسة عند إيقاف الخادم
import atexit

def cleanup_whatsapp():
    """تنظيف الموارد عند إيقاف الخادم"""
    try:
        whatsapp_manager.close()
        logger.info("تم إغلاق جلسة WhatsApp بنجاح")
    except Exception as e:
        logger.error(f"خطأ في تنظيف موارد WhatsApp: {e}")

atexit.register(cleanup_whatsapp)
