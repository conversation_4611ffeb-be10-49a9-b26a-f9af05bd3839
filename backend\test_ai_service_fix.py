#!/usr/bin/env python3
"""
اختبار إصلاح خدمة الذكاء الاصطناعي
"""

import asyncio
import sys
import os
from pathlib import Path

# إضافة مسار المشروع
sys.path.append(str(Path(__file__).parent))

from services.ai_service import AIService
from database import SessionLocal
from models import Settings
import logging

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_ai_service():
    """اختبار خدمة الذكاء الاصطناعي"""
    
    print("🧪 اختبار خدمة الذكاء الاصطناعي")
    print("=" * 60)
    
    try:
        # الحصول على الإعدادات
        db = SessionLocal()
        try:
            settings = db.query(Settings).first()
            if not settings:
                print("❌ لا توجد إعدادات في قاعدة البيانات")
                return False
            
            if not settings.openai_api_key:
                print("❌ مفتاح OpenAI API غير مُعين")
                return False
            
            print(f"✅ تم العثور على مفتاح OpenAI")
            print(f"📊 النموذج: {settings.gpt_model or 'gpt-3.5-turbo'}")
            
            # إنشاء خدمة الذكاء الاصطناعي
            ai_service = AIService(
                api_key=settings.openai_api_key,
                model=settings.gpt_model or "gpt-3.5-turbo",
                system_prompt=settings.gpt_prompt or "أنت مساعد ذكي لخدمة العملاء."
            )
            
            print("✅ تم إنشاء خدمة الذكاء الاصطناعي بنجاح")
            
            # اختبار توليد رد
            test_message = "مرحباً، أريد معلومات عن خدماتكم"
            print(f"\n📨 اختبار الرد على: {test_message}")
            
            response = await ai_service.generate_response(
                user_message=test_message,
                contact_phone="+966501234567"
            )
            
            print(f"🤖 الرد المولد: {response}")
            print("✅ تم اختبار توليد الرد بنجاح")
            
            return True
            
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """الدالة الرئيسية"""
    
    print("🚀 بدء اختبار إصلاح خدمة الذكاء الاصطناعي")
    print("=" * 60)
    
    success = await test_ai_service()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 جميع الاختبارات نجحت!")
        print("💡 يمكنك الآن تشغيل نظام الردود الذكية")
    else:
        print("⚠️ هناك مشاكل تحتاج إلى إصلاح")
        print("💡 تحقق من:")
        print("   - إعدادات OpenAI API في قاعدة البيانات")
        print("   - صحة مفتاح OpenAI API")
        print("   - اتصال الإنترنت")

if __name__ == "__main__":
    asyncio.run(main())
