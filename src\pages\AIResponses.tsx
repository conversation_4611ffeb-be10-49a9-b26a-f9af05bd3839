import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import {
  Bot,
  MessageSquare,
  Save,
  Play,
  Pause,
  Settings,
  Zap,
  Brain,
  TestTube,
  RefreshCw,
  AlertCircle,
  CheckCircle
} from "lucide-react";
import { useApi, useApiMutation } from "@/hooks/useApi";
import {
  getAISettings,
  updateAISettings,
  toggleAutoReply,
  testAIResponse,
  getAIResponses
} from "@/lib/api";

const AIResponses = () => {
  const [isAutoReplyEnabled, setIsAutoReplyEnabled] = useState(false);
  const [isServiceRunning, setIsServiceRunning] = useState(false);
  const [serviceStatus, setServiceStatus] = useState("unknown");
  const [lastStatusCheck, setLastStatusCheck] = useState("");
  const [prompt, setPrompt] = useState("");
  const [testMessage, setTestMessage] = useState("");
  const [testResponse, setTestResponse] = useState("");

  // جلب الإعدادات الحالية
  const { data: settings, loading: settingsLoading, error: settingsError, refetch: refetchSettings } = useApi(getAISettings);

  // جلب الردود التلقائية
  const { data: aiResponses, loading: responsesLoading, refetch: refetchResponses } = useApi(getAIResponses);

  // العمليات
  const { mutate: updateSettings, loading: updating } = useApiMutation(updateAISettings);
  const { mutate: toggleReply, loading: toggling } = useApiMutation(toggleAutoReply);
  const { mutate: testAIResponseMutation, loading: testing } = useApiMutation(testAIResponse);

  // تحديث الحقول عند تحميل الإعدادات
  useEffect(() => {
    if (settings) {
      setIsAutoReplyEnabled(settings.auto_reply_enabled);
      setPrompt(settings.gpt_prompt || "");
    }
  }, [settings]);

  // فحص حالة الخدمة عند تحميل الصفحة وبشكل دوري
  useEffect(() => {
    checkServiceStatus();

    // فحص دوري كل 10 ثوانٍ
    const interval = setInterval(checkServiceStatus, 10000);

    return () => clearInterval(interval);
  }, []);

  const checkServiceStatus = async () => {
    try {
      const response = await fetch('http://localhost:8000/api/ai-responses/auto-reply/status');
      if (response.ok) {
        const data = await response.json();
        setIsServiceRunning(data.is_running);
        setServiceStatus(data.status || "unknown");
        setLastStatusCheck(new Date().toLocaleTimeString('ar-SA'));
        console.log('حالة الخدمة:', data);
      } else {
        console.error('خطأ في فحص حالة الخدمة:', response.status);
        setServiceStatus("error");
        setLastStatusCheck(new Date().toLocaleTimeString('ar-SA'));
      }
    } catch (error) {
      console.error('خطأ في فحص حالة الخدمة:', error);
      setServiceStatus("error");
      setLastStatusCheck(new Date().toLocaleTimeString('ar-SA'));
    }
  };

  const handleSaveSettings = async () => {
    const settingsData = {
      gpt_prompt: prompt,
      auto_reply_enabled: isAutoReplyEnabled
    };

    const result = await updateSettings(settingsData);
    if (result) {
      refetchSettings();
    }
  };

  const handleToggleAutoReply = async () => {
    const result = await toggleReply({});
    if (result) {
      setIsAutoReplyEnabled(result.auto_reply_enabled);
      setIsServiceRunning(result.service_running || false);
      refetchSettings();

      // إعادة فحص حالة الخدمة بعد ثانية
      setTimeout(checkServiceStatus, 1000);
    }
  };

  const handleTestPrompt = async () => {
    if (!testMessage.trim()) return;

    const result = await testAIResponseMutation(testMessage);
    if (result) {
      setTestResponse(result.ai_response);
    }
  };

  const responseTemplates = [
    {
      title: "مساعد مبيعات",
      prompt: "أنت مساعد مبيعات محترف. هدفك مساعدة العملاء في اتخاذ قرار الشراء وتقديم معلومات المنتجات بطريقة مقنعة ومفيدة."
    },
    {
      title: "خدمة العملاء",
      prompt: "أنت ممثل خدمة عملاء ودود. مهمتك حل مشاكل العملاء والإجابة على استفساراتهم بصبر ومهنية عالية."
    },
    {
      title: "مساعد تقني",
      prompt: "أنت مساعد تقني متخصص. ساعد العملاء في حل المشاكل التقنية وقدم إرشادات واضحة خطوة بخطوة."
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      <div className="max-w-7xl mx-auto p-6">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-foreground mb-2">
            نظام الردود الذكية
          </h1>
          <p className="text-muted-foreground text-lg">
            خصص ردود الذكاء الاصطناعي للتفاعل مع عملائك تلقائياً
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Settings */}
          <div className="lg:col-span-2 space-y-6">
            {/* Auto Reply Toggle */}
            <Card className="p-6">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h2 className="text-xl font-semibold text-foreground">
                    الرد التلقائي
                  </h2>
                  <p className="text-sm text-muted-foreground">
                    تفعيل أو إيقاف نظام الرد التلقائي
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <Switch
                    checked={isAutoReplyEnabled}
                    onCheckedChange={handleToggleAutoReply}
                    disabled={toggling}
                  />
                  {isAutoReplyEnabled ? (
                    <Play className="h-5 w-5 text-success" />
                  ) : (
                    <Pause className="h-5 w-5 text-muted-foreground" />
                  )}
                </div>
              </div>
              
              <div className={`p-4 rounded-lg ${isAutoReplyEnabled ? 'bg-success/10 border border-success/20' : 'bg-muted/30'}`}>
                <div className="space-y-2">
                  <p className="text-sm font-medium">
                    {isAutoReplyEnabled ? "🟢 النظام مفعل - سيتم الرد على الرسائل تلقائياً" : "⚪ النظام معطل - لن يتم الرد التلقائي"}
                  </p>
                  {isAutoReplyEnabled && (
                    <div className="space-y-1">
                      <p className="text-xs text-muted-foreground">
                        حالة المراقبة: {isServiceRunning ? "🔄 تعمل" : "⏸️ متوقفة"}
                        {serviceStatus === "error" && " (خطأ في الاتصال)"}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        آخر فحص: {lastStatusCheck || "لم يتم الفحص بعد"}
                      </p>
                      {!isServiceRunning && serviceStatus !== "error" && (
                        <p className="text-xs text-orange-600">
                          💡 قم بتفعيل/إلغاء تفعيل النظام لبدء المراقبة
                        </p>
                      )}
                      <div className="flex gap-2 mt-2">
                        <Button
                          onClick={checkServiceStatus}
                          variant="outline"
                          size="sm"
                          className="text-xs"
                        >
                          <RefreshCw className="h-3 w-3 mr-1" />
                          فحص الحالة
                        </Button>
                        <Button
                          onClick={() => window.open('http://localhost:8000/api/ai-responses/auto-reply/status', '_blank')}
                          variant="outline"
                          size="sm"
                          className="text-xs"
                        >
                          عرض تفاصيل الحالة
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </Card>



            {/* GPT Prompt Configuration */}
            <Card className="p-6">
              <h2 className="text-xl font-semibold text-foreground mb-6">
                إعداد برومبت الذكاء الاصطناعي
              </h2>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    التعليمات الأساسية للذكاء الاصطناعي
                  </label>
                  <Textarea
                    value={prompt}
                    onChange={(e) => setPrompt(e.target.value)}
                    rows={8}
                    className="resize-none"
                    placeholder="اكتب التعليمات التي تريد من الذكاء الاصطناعي اتباعها..."
                  />
                </div>

                <div className="flex gap-3">
                  <Button
                    onClick={handleSaveSettings}
                    className="gap-2"
                    disabled={updating}
                  >
                    <Save className="h-4 w-4" />
                    {updating ? "جاري الحفظ..." : "حفظ البرومبت"}
                  </Button>
                  <Button
                    variant="outline"
                    className="gap-2"
                    onClick={() => window.location.href = '/settings'}
                  >
                    <Settings className="h-4 w-4" />
                    إعدادات OpenAI
                  </Button>
                </div>
              </div>
            </Card>

            {/* Test Prompt */}
            <Card className="p-6">
              <h2 className="text-xl font-semibold text-foreground mb-6">
                اختبار البرومبت
              </h2>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    رسالة تجريبية من العميل
                  </label>
                  <Input
                    value={testMessage}
                    onChange={(e) => setTestMessage(e.target.value)}
                    placeholder="مثال: مرحباً، أريد معلومات عن منتجاتكم"
                  />
                </div>

                <Button
                  onClick={handleTestPrompt}
                  variant="outline"
                  className="gap-2"
                  disabled={testing || !testMessage.trim()}
                >
                  <TestTube className="h-4 w-4" />
                  {testing ? "جاري الاختبار..." : "اختبار الرد"}
                </Button>

                {testResponse && (
                  <div className="p-4 bg-accent/30 rounded-lg">
                    <h4 className="font-medium text-foreground mb-2">رد الذكاء الاصطناعي:</h4>
                    <p className="text-sm text-foreground">{testResponse}</p>
                  </div>
                )}
              </div>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Quick Stats */}
            <Card className="p-6">
              <h3 className="text-lg font-semibold text-foreground mb-4">
                إحصائيات سريعة
              </h3>
              
              {responsesLoading ? (
                <div className="space-y-4">
                  {[1, 2, 3].map((i) => (
                    <div key={i} className="flex items-center justify-between">
                      <div className="w-20 h-4 bg-muted rounded animate-pulse"></div>
                      <div className="w-12 h-4 bg-muted rounded animate-pulse"></div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">إجمالي الردود</span>
                    <span className="font-bold text-foreground">
                      {aiResponses?.length || 0}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">الحالة</span>
                    <span className={`font-bold ${isAutoReplyEnabled ? 'text-success' : 'text-muted-foreground'}`}>
                      {isAutoReplyEnabled ? 'مفعل' : 'معطل'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">النموذج</span>
                    <span className="font-bold text-foreground text-xs">
                      {settings?.gpt_model || "غير محدد"}
                    </span>
                  </div>
                </div>
              )}
            </Card>

            {/* Response Templates */}
            <Card className="p-6">
              <h3 className="text-lg font-semibold text-foreground mb-4">
                قوالب جاهزة
              </h3>
              
              <div className="space-y-3">
                {responseTemplates.map((template, index) => (
                  <button
                    key={index}
                    onClick={() => setPrompt(template.prompt)}
                    className="w-full text-left p-3 rounded-lg border border-border hover:bg-accent/50 transition-colors"
                  >
                    <div className="font-medium text-foreground text-sm">
                      {template.title}
                    </div>
                    <div className="text-xs text-muted-foreground mt-1">
                      انقر لاستخدام هذا القالب
                    </div>
                  </button>
                ))}
              </div>
            </Card>

            {/* AI Features */}
            <Card className="p-6">
              <h3 className="text-lg font-semibold text-foreground mb-4">
                مزايا الذكاء الاصطناعي
              </h3>
              
              <div className="space-y-3">
                <div className="flex items-center gap-2 text-sm">
                  <Brain className="h-4 w-4 text-primary" />
                  <span className="text-foreground">فهم السياق</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <Zap className="h-4 w-4 text-primary" />
                  <span className="text-foreground">ردود فورية</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <MessageSquare className="h-4 w-4 text-primary" />
                  <span className="text-foreground">محادثات طبيعية</span>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AIResponses;